{"id": "so-help-me-god", "name": "So Help Me God", "description": "Announced in February 2015, So Help Me God is now one of <PERSON><PERSON><PERSON>'s most infamous unreleased projects. Essentially being a more advanced version of the songs developed during the Yeezus 2 era, So Help Me God gained significant hype as the teaser tracks of \"Wolves,\" \"All Day,\" and \"Only One\" was revealed to the public. Despite intending to release the album in March 2015, <PERSON><PERSON><PERSON> never finished So Help Me God, and only a few songs from the era ended up on The Life of Pablo.", "backgroundColor": "rgb(107, 95, 90)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17EkVD2qjesvqQj_tNflhxGIalxqJKaxRprn__7jK5Jb99wkG5Clf4Nw5fMkIsVXgeB5EhXCmtMBFdDURlkqkUFvJNd4wfcG3jpSghP9kXBnNe3iPEXU6Rl-4E-E5iwvDrSHH2nODQQ?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "mitus-touch", "name": "Rihanna - <PERSON><PERSON> [V1]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: MELODIES MT RIHANNA\nAnother version with vastly different production, different <PERSON><PERSON><PERSON> vocals and a <PERSON><PERSON><PERSON> ref said to exist by <PERSON><PERSON>. Original snippet leaked January 30th, 2024. Leaked fully as part of the Can U Be groupbuy. Made sometime in 2015.", "length": "85.76", "fileDate": 17166816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/cf47413a6d1fa406f26ed5f994cdb05d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cf47413a6d1fa406f26ed5f994cdb05d\", \"key\": \"Mitus Touch\", \"title\": \"Rihanna - Mitus Touch [V1]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"2 Rihannas\", \"Mitus Touch\"], \"description\": \"OG Filename: MELODIES MT RIHANNA\\nAnother version with vastly different production, different <PERSON><PERSON><PERSON> vocals and a <PERSON><PERSON><PERSON> ref said to exist by <PERSON><PERSON>. Original snippet leaked January 30th, 2024. Leaked fully as part of the Can U Be groupbuy. Made sometime in 2015.\", \"date\": 17166816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dd4904886227750a18ea1555251eac7d\", \"url\": \"https://api.pillowcase.su/api/download/dd4904886227750a18ea1555251eac7d\", \"size\": \"1.58 MB\", \"duration\": 85.76}", "aliases": ["2 Rihannas", "<PERSON><PERSON>"], "size": "1.58 MB"}, {"id": "new-rihanna", "name": "✨ Rihanna - New Rihanna [V2]", "artists": ["Kanye West"], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: NEW RIHANNA (03)\nVersion with finished <PERSON><PERSON><PERSON> vocals and mumble <PERSON><PERSON><PERSON> vocals different from any other version, weirdly pitch shifted. Unknown exactly when this version was made, but its from 2015. The instrumental is slightly different.", "length": "130.78", "fileDate": 17267040, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/7aa6231a196957f9e8b9f76083b66538", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7aa6231a196957f9e8b9f76083b66538\", \"key\": \"New Rihanna\", \"title\": \"\\u2728 Rihanna - New Rihanna [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"2 Rihannas\", \"Mitus Touch\"], \"description\": \"OG Filename: NEW RIHANNA (03)\\nVersion with finished Rihanna vocals and mumble <PERSON><PERSON><PERSON> vocals different from any other version, weirdly pitch shifted. Unknown exactly when this version was made, but its from 2015. The instrumental is slightly different.\", \"date\": 17267040, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c24d6f887b4b44a528c8b984c415e06a\", \"url\": \"https://api.pillowcase.su/api/download/c24d6f887b4b44a528c8b984c415e06a\", \"size\": \"4.51 MB\", \"duration\": 130.78}", "aliases": ["2 Rihannas", "<PERSON><PERSON>"], "size": "4.51 MB"}, {"id": "mitus-touch-3", "name": "Rihanna - <PERSON><PERSON> [V3]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Intro for a version done by <PERSON><PERSON>. Unknown what version it was made for. Fakes has the \"full\" fake version alongside this intro.", "length": "34.34", "fileDate": 15999552, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/f8e976a8aec81f71491953367c66de66", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f8e976a8aec81f71491953367c66de66\", \"key\": \"Mitus Touch\", \"title\": \"Rihanna - Mitus Touch [V3]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"2 Rihannas\"], \"description\": \"Intro for a version done by <PERSON><PERSON>. Unknown what version it was made for. Fakes has the \\\"full\\\" fake version alongside this intro.\", \"date\": 15999552, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c731cf0c4d2bd160eb7d7cd0c6845740\", \"url\": \"https://api.pillowcase.su/api/download/c731cf0c4d2bd160eb7d7cd0c6845740\", \"size\": \"2.96 MB\", \"duration\": 34.34}", "aliases": ["2 Rihannas"], "size": "2.96 MB"}, {"id": "mothership", "name": "<PERSON><PERSON><PERSON> - Mothership [V4]", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON><PERSON>"], "notes": "<PERSON><PERSON> version. <PERSON><PERSON>'s vocals could be partially reference work, but based on his lyrics including \"Cole World\" and other lines referring to himself it's seemingly at least mostly recorded as a potential feature. <PERSON><PERSON> would reuse part of his verse on the 2015 track \"Black Friday\".", "length": "211.51", "fileDate": 16080768, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/65136be0233c1406deb2847db3e11edd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/65136be0233c1406deb2847db3e11edd\", \"key\": \"Mothership\", \"title\": \"Rihanna - Mothership [V4]\", \"artists\": \"(feat. <PERSON><PERSON> <PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"2 Rihannas\", \"Mitus Touch\"], \"description\": \"<PERSON><PERSON> version. <PERSON><PERSON>'s vocals could be partially reference work, but based on his lyrics including \\\"Cole World\\\" and other lines referring to himself it's seemingly at least mostly recorded as a potential feature. <PERSON><PERSON> would reuse part of his verse on the 2015 track \\\"Black Friday\\\".\", \"date\": 16080768, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f073cfd56583202627669e3c7a552688\", \"url\": \"https://api.pillowcase.su/api/download/f073cfd56583202627669e3c7a552688\", \"size\": \"5.8 MB\", \"duration\": 211.51}", "aliases": ["2 Rihannas", "<PERSON><PERSON>"], "size": "5.8 MB"}, {"id": "mitus-touch-5", "name": "⭐ <PERSON>tus <PERSON> [V6]", "artists": ["???"], "producers": ["<PERSON><PERSON>"], "notes": "Kanye reference track for <PERSON><PERSON><PERSON>. Wasn't originally intended for any <PERSON><PERSON><PERSON> project, but according to some it was played at a Def Jam meeting in early 2015, and taken for So Help Me God.", "length": "131.03", "fileDate": 14286240, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/a9dd375b0578e101690f19de431fe3a5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a9dd375b0578e101690f19de431fe3a5\", \"key\": \"Mitus Touch\", \"title\": \"\\u2b50 Mitus Touch [V6]\", \"artists\": \"(feat. ???) (prod. <PERSON><PERSON>)\", \"aliases\": [\"2 Rihannas\"], \"description\": \"Kanye reference track for Rihanna. Wasn't originally intended for any Kanye project, but according to some it was played at a Def Jam meeting in early 2015, and taken for So Help Me God.\", \"date\": 14286240, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"0727a90e16a24d4084c87bfe2c807d04\", \"url\": \"https://api.pillowcase.su/api/download/0727a90e16a24d4084c87bfe2c807d04\", \"size\": \"4.51 MB\", \"duration\": 131.03}", "aliases": ["2 Rihannas"], "size": "4.51 MB"}, {"id": "2-rihannas", "name": "2 <PERSON><PERSON><PERSON><PERSON> [V7]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Version with <PERSON><PERSON><PERSON> punch-ins replacing vocals explicitly for <PERSON><PERSON><PERSON>, including the opening line being changed from \"that new <PERSON><PERSON><PERSON> in the front\" to \"I got two Rihannas out front\". According to a trusted source, this was intended for <PERSON><PERSON><PERSON>'s album and no longer <PERSON><PERSON><PERSON>'s song. Title confirmed by a leaked stem from the song. Played at Glastonbury 2015 immediately after \"Pressure\".", "length": "40.91", "fileDate": 14353632, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/80122ef20fc2ef076f9b2be3f5e15d5a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/80122ef20fc2ef076f9b2be3f5e15d5a\", \"key\": \"2 Rihannas\", \"title\": \"2 Rihannas [V7]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Mitus Touch\"], \"description\": \"Version with Pusha T punch-ins replacing vocals explicitly for <PERSON><PERSON><PERSON>, including the opening line being changed from \\\"that new Rihanna in the front\\\" to \\\"I got two Rihannas out front\\\". According to a trusted source, this was intended for <PERSON><PERSON><PERSON>'s album and no longer <PERSON><PERSON><PERSON>'s song. Title confirmed by a leaked stem from the song. Played at Glastonbury 2015 immediately after \\\"Pressure\\\".\", \"date\": 14353632, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"a1c985bc9a56210a71b9ed9431d02c18\", \"url\": \"https://api.pillowcase.su/api/download/a1c985bc9a56210a71b9ed9431d02c18\", \"size\": \"539 kB\", \"duration\": 40.91}", "aliases": ["<PERSON><PERSON>"], "size": "539 kB"}, {"id": "all-day", "name": "All Day [V8]", "artists": ["<PERSON><PERSON><PERSON>", "Allan Kingdom"], "producers": ["Velous", "<PERSON>", "<PERSON>"], "notes": "Version played at Yeezy Season 3/The Life of Pablo listening party, very similar to the 6.22 version but has some open instrumental in place of <PERSON><PERSON><PERSON>'s mumble lines. It's speculated that <PERSON><PERSON><PERSON> played this version because he was unhappy with the final version of All Day. Full version hasn't leaked.", "length": "145.77", "fileDate": 14551488, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/d51aebcab7fd90e4c3bfe23355685578", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d51aebcab7fd90e4c3bfe23355685578\", \"key\": \"All Day\", \"title\": \"All Day [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> London & Allan Kingdom) (prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"description\": \"Version played at Yeezy Season 3/The Life of Pablo listening party, very similar to the 6.22 version but has some open instrumental in place of <PERSON><PERSON><PERSON>'s mumble lines. It's speculated that <PERSON><PERSON><PERSON> played this version because he was unhappy with the final version of All Day. Full version hasn't leaked.\", \"date\": 14551488, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"5605def0590cc7a1d15d5f383ef55fb9\", \"url\": \"https://api.pillowcase.su/api/download/5605def0590cc7a1d15d5f383ef55fb9\", \"size\": \"4.74 MB\", \"duration\": 145.77}", "aliases": [], "size": "4.74 MB"}, {"id": "all-day-8", "name": "All Day [V9]", "artists": ["<PERSON><PERSON><PERSON>", "Allan Kingdom", "<PERSON>"], "producers": ["Velous", "<PERSON>", "<PERSON>"], "notes": "Consequence reference track. Seems to have the same features and instrumental as the released version. Played by Consequence on an Instagram live in November 2022.", "length": "55.28", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/c86de789ade53014f012d03946191c91", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c86de789ade53014f012d03946191c91\", \"key\": \"All Day\", \"title\": \"All Day [V9]\", \"artists\": \"(ref. Consequence) (feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"description\": \"Consequence reference track. Seems to have the same features and instrumental as the released version. Played by Consequence on an Instagram live in November 2022.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"12f22cc12e08d5091c4d6f306c788614\", \"url\": \"https://api.pillowcase.su/api/download/12f22cc12e08d5091c4d6f306c788614\", \"size\": \"3.29 MB\", \"duration\": 55.28}", "aliases": [], "size": "3.29 MB"}, {"id": "all-day-9", "name": "⭐ All Day [V10]", "artists": ["<PERSON><PERSON><PERSON>", "Allan Kingdom", "<PERSON>"], "producers": ["Velous", "<PERSON>", "<PERSON>"], "notes": "Early version still containing <PERSON><PERSON><PERSON>'s mostly finished vocal takes, but also features <PERSON><PERSON> filling in parts with reference vocals, some of which <PERSON><PERSON><PERSON> uses on the final version. Often mistaken for a scrapped feature. Sent to <PERSON><PERSON> in December 2014 alongside \"Wolves\" and \"Go Pro.\"", "length": "228.55", "fileDate": 14294880, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/1f8d8cead0919a5c3dbd5ebe6d25dced", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1f8d8cead0919a5c3dbd5ebe6d25dced\", \"key\": \"All Day\", \"title\": \"\\u2b50 All Day [V10]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"description\": \"Early version still containing <PERSON><PERSON><PERSON>'s mostly finished vocal takes, but also features <PERSON><PERSON> filling in parts with reference vocals, some of which <PERSON><PERSON><PERSON> uses on the final version. Often mistaken for a scrapped feature. Sent to <PERSON><PERSON> in December 2014 alongside \\\"Wolves\\\" and \\\"Go Pro.\\\"\", \"date\": 14294880, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"496f7c0723e7c4e2f335a2281a695d3e\", \"url\": \"https://api.pillowcase.su/api/download/496f7c0723e7c4e2f335a2281a695d3e\", \"size\": \"6.07 MB\", \"duration\": 228.55}", "aliases": [], "size": "6.07 MB"}, {"id": "all-day-10", "name": "All Day [V11]", "artists": ["2 Chainz", "<PERSON><PERSON><PERSON>", "Allan Kingdom", "<PERSON>"], "producers": ["Velous", "<PERSON>"], "notes": "A \"Remix\", however it is actually an original version featuring early <PERSON><PERSON><PERSON> vocals and different take. Dated later than the <PERSON><PERSON> reference as he records the lines that were written for him. The production has the OG beat also.", "length": "307.18", "fileDate": "", "leakDate": "", "availableLength": "Tagged", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/26bf54e0131a7b8794d828d2db71d6f2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/26bf54e0131a7b8794d828d2db71d6f2\", \"key\": \"All Day\", \"title\": \"All Day [V11]\", \"artists\": \"(feat. <PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"A \\\"Remix\\\", however it is actually an original version featuring early <PERSON><PERSON><PERSON> vocals and different take. Dated later than the <PERSON><PERSON> reference as he records the lines that were written for him. The production has the OG beat also.\", \"date\": null, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"6a33e5cef4301e6e9d63e519be5a54f7\", \"url\": \"https://api.pillowcase.su/api/download/6a33e5cef4301e6e9d63e519be5a54f7\", \"size\": \"7.32 MB\", \"duration\": 307.18}", "aliases": [], "size": "7.32 MB"}, {"id": "all-day-11", "name": "All Day [V14]", "artists": ["Allan Kingdom", "<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON><PERSON>", "French Montana", "Kanye West", "MIKE DEAN", "<PERSON>", "Velous", "<PERSON>", "<PERSON>", "Plain Pat", "<PERSON>"], "notes": "OG Filename: All Day Mix 19 EXPLICIT Re-4_3-1-15_[VM]\nOG file, dated 3 days before the song released. No noticeable differences from the released version.", "length": "310.96", "fileDate": 16049664, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/fa00427a80fa3dd699fef069d3b1acad", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fa00427a80fa3dd699fef069d3b1acad\", \"key\": \"All Day\", \"title\": \"All Day [V14]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>)\", \"description\": \"OG Filename: All Day Mix 19 EXPLICIT Re-4_3-1-15_[VM]\\nOG file, dated 3 days before the song released. No noticeable differences from the released version.\", \"date\": 16049664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"67bfabfc1969a78a0c83771a3c96331e\", \"url\": \"https://api.pillowcase.su/api/download/67bfabfc1969a78a0c83771a3c96331e\", \"size\": \"7.39 MB\", \"duration\": 310.96}", "aliases": [], "size": "7.39 MB"}, {"id": "awesome", "name": "Awesome [V10]", "artists": ["<PERSON>", "Ty Dolla $ign"], "producers": ["Hit-Boy", "MIKE DEAN"], "notes": "Has more developed production compared to previous versions. <PERSON> has vocals from <PERSON>ign. Extended snippet was played in Season 9, episoed 20 of Keeping Up With the Kardashians.", "length": "86.36", "fileDate": 14095296, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/f59340505f8f100c71396a4c3c451ce4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f59340505f8f100c71396a4c3c451ce4\", \"key\": \"Awesome\", \"title\": \"Awesome [V10]\", \"artists\": \"(feat. <PERSON> & <PERSON> $ign) (prod. <PERSON><PERSON><PERSON> & MIKE DEAN)\", \"description\": \"Has more developed production compared to previous versions. <PERSON> has vocals from <PERSON> $ign. Extended snippet was played in Season 9, episoed 20 of Keeping Up With the Kardashians.\", \"date\": 14095296, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"bb9615720a56a5504ceffdc2adbec73f\", \"url\": \"https://api.pillowcase.su/api/download/bb9615720a56a5504ceffdc2adbec73f\", \"size\": \"3.79 MB\", \"duration\": 86.36}", "aliases": [], "size": "3.79 MB"}, {"id": "don-t-let-your-guard-down", "name": "Don't Let Your Guard Down", "artists": [], "producers": ["Hit-Boy"], "notes": "OG Filename: KW - Don't Let Your Guard Down Ref (2.14.15)\nMumble demo. Was given to <PERSON> $ign, released as \"Guard Down\".", "length": "211.46", "fileDate": 16647552, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/b323b7eefd3fd76aa1618bb3ce9f102c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b323b7eefd3fd76aa1618bb3ce9f102c\", \"key\": \"Don't Let Your Guard Down\", \"title\": \"Don't Let Your Guard Down\", \"artists\": \"(prod. <PERSON>-<PERSON>)\", \"aliases\": [\"Guard Down\"], \"description\": \"OG Filename: KW - Don't Let Your Guard Down Ref (2.14.15)\\nMumble demo. Was given to Ty Dolla $ign, released as \\\"Guard Down\\\".\", \"date\": 16647552, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"348cf254e3e2d734542dba6effbd86c0\", \"url\": \"https://api.pillowcase.su/api/download/348cf254e3e2d734542dba6effbd86c0\", \"size\": \"5.8 MB\", \"duration\": 211.46}", "aliases": ["Guard Down"], "size": "5.8 MB"}, {"id": "enya", "name": "<PERSON><PERSON> [V3]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Kanye West", "<PERSON>", "<PERSON><PERSON><PERSON>"], "notes": "An early version with <PERSON><PERSON><PERSON> on the hook with extra vocals. Leaked alongside its stems in a Soakbuy.", "length": "139.85", "fileDate": 17432928, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/95ab7bba78818663da0425d664de8eb0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/95ab7bba78818663da0425d664de8eb0\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> Heat & Theophilus London)\", \"aliases\": [\"Waves\"], \"description\": \"An early version with <PERSON><PERSON><PERSON> on the hook with extra vocals. Leaked alongside its stems in a Soakbuy.\", \"date\": 17432928, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c219ccdc58f0cac7dc9fd2f06b717836\", \"url\": \"https://api.pillowcase.su/api/download/c219ccdc58f0cac7dc9fd2f06b717836\", \"size\": \"4.65 MB\", \"duration\": 139.85}", "aliases": ["Waves"], "size": "4.65 MB"}, {"id": "fade", "name": "Fade [V3]", "artists": [], "producers": ["Kanye West"], "notes": "In late 2016, <PERSON><PERSON><PERSON> posted a snippet of a so called \"remix\" to \"Fade\", claiming he would drop it in 2017. However, due to the OG beat, it was most likely a reference track. Features a simpler beat (heard in a video from August 2015) compared to the finalized March version.", "length": "53.13", "fileDate": 14828832, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/7094194a47f9a2e49189cd05670b128e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7094194a47f9a2e49189cd05670b128e\", \"key\": \"Fade\", \"title\": \"Fade [V3]\", \"artists\": \"(ref. Consequence) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"In late 2016, <PERSON><PERSON><PERSON> posted a snippet of a so called \\\"remix\\\" to \\\"Fade\\\", claiming he would drop it in 2017. However, due to the OG beat, it was most likely a reference track. Features a simpler beat (heard in a video from August 2015) compared to the finalized March version.\", \"date\": 14828832, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ac9dabdfe0e933192647ea67b84f5fd7\", \"url\": \"https://api.pillowcase.su/api/download/ac9dabdfe0e933192647ea67b84f5fd7\", \"size\": \"1.06 MB\", \"duration\": 53.13}", "aliases": [], "size": "1.06 MB"}, {"id": "fade-16", "name": "Fade [V4]", "artists": ["Ty Dolla $ign", "<PERSON>"], "producers": ["Kanye West"], "notes": "Version of \"Fade\" that <PERSON> recorded over. Has slightly less production.", "length": "10.97", "fileDate": 16911936, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/5d9b0effd91d764bf2f31adb606a0ed6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5d9b0effd91d764bf2f31adb606a0ed6\", \"key\": \"Fade\", \"title\": \"Fade [V4]\", \"artists\": \"(feat. <PERSON>gn & <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Version of \\\"Fade\\\" that <PERSON> recorded over. Has slightly less production.\", \"date\": 16911936, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"c05bedb98c65d0cca30de586d606bf7b\", \"url\": \"https://api.pillowcase.su/api/download/c05bedb98c65d0cca30de586d606bf7b\", \"size\": \"2.59 MB\", \"duration\": 10.97}", "aliases": [], "size": "2.59 MB"}, {"id": "fade-17", "name": "⭐ Fade [V5]", "artists": ["Ty Dolla $ign", "<PERSON>"], "producers": ["Kanye West"], "notes": "OG Filename: FADE @ 120 BPM Dm\nVersion from March 2015. Has a feature that is most likely <PERSON>, as he was doing sessions with <PERSON><PERSON><PERSON> around the same time for his album The Colour In Anything. Also contains added production from earlier versions, later stripped away in August 2015 before the beat was reworked entirely. Snippet leaked October 23, 2022.", "length": "260.17", "fileDate": 16911936, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/91227b8669c1779b6f639b32f00d9d66", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/91227b8669c1779b6f639b32f00d9d66\", \"key\": \"Fade\", \"title\": \"\\u2b50 Fade [V5]\", \"artists\": \"(feat. <PERSON>gn & <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: FADE @ 120 BPM Dm\\nVersion from March 2015. Has a feature that is most likely <PERSON>, as he was doing sessions with <PERSON><PERSON><PERSON> around the same time for his album The Colour In Anything. Also contains added production from earlier versions, later stripped away in August 2015 before the beat was reworked entirely. Snippet leaked October 23, 2022.\", \"date\": 16911936, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d5b17270e709dc36db8192a8e0e5ee57\", \"url\": \"https://api.pillowcase.su/api/download/d5b17270e709dc36db8192a8e0e5ee57\", \"size\": \"6.57 MB\", \"duration\": 260.17}", "aliases": [], "size": "6.57 MB"}, {"id": "feedback", "name": "Feedback [V1]", "artists": [], "producers": ["<PERSON>", "Kanye West"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> <PERSON><PERSON> (7.18.14)\nFirst idea for the song from 2014. Has no verses, and the drums are beatboxed by <PERSON><PERSON><PERSON>. Original snippet leaked February 14th, 2023. Leaked after a successful Soakbuy.", "length": "382.21", "fileDate": 17385408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/4bbd95715876eaa1e907751ace0cef3b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4bbd95715876eaa1e907751ace0cef3b\", \"key\": \"Feedback\", \"title\": \"Feedback [V1]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON> & Kanye <PERSON>)\", \"aliases\": [\"Good News\", \"A Long Time\"], \"description\": \"OG Filename: KW - Feedback Ref (7.18.14)\\nFirst idea for the song from 2014. Has no verses, and the drums are beatboxed by <PERSON><PERSON><PERSON>. Original snippet leaked February 14th, 2023. Leaked after a successful Soakbuy.\", \"date\": 17385408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1c561e608328c416b48c0c35dd3e6f40\", \"url\": \"https://api.pillowcase.su/api/download/1c561e608328c416b48c0c35dd3e6f40\", \"size\": \"8.53 MB\", \"duration\": 382.21}", "aliases": ["Good News", "A Long Time"], "size": "8.53 MB"}, {"id": "feedback-19", "name": "Feedback [V2]", "artists": [], "producers": ["<PERSON>", "Kanye West"], "notes": "Made in 2014 during So Help Me God sessions. Contains a lot of mumble. Snippet of a recording from <PERSON> playing it in 2015.", "length": "63.5", "fileDate": 14327712, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/1489cd32c0a300cbd86d11bb50f75f29", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1489cd32c0a300cbd86d11bb50f75f29\", \"key\": \"Feedback\", \"title\": \"Feedback [V2]\", \"artists\": \"(prod. <PERSON> & Kanye <PERSON>)\", \"aliases\": [\"Good News\", \"A Long Time\"], \"description\": \"Made in 2014 during So Help Me God sessions. Contains a lot of mumble. Snippet of a recording from <PERSON> playing it in 2015.\", \"date\": 14327712, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"2e4553048698c790f9093b54ec3f720b\", \"url\": \"https://api.pillowcase.su/api/download/2e4553048698c790f9093b54ec3f720b\", \"size\": \"3.43 MB\", \"duration\": 63.5}", "aliases": ["Good News", "A Long Time"], "size": "3.43 MB"}, {"id": "feedback-20", "name": "Feedback [V3]", "artists": [], "producers": ["<PERSON>", "Kanye West"], "notes": "Consequence \"Feedback\" reference track over the OG instrumental. Has lyrics from <PERSON><PERSON><PERSON>'s perspective, so it isn't a remix. Snippets originally previewed by him on Instagram Live on May 5th 2023 and July 22nd, 2024. He played the full song on live September 23rd, 2024. Unknown when it's from, but likely early-mid 2015.", "length": "148.14", "fileDate": 17270496, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/4c95cc3f554482111c7158fd4b16ea05", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4c95cc3f554482111c7158fd4b16ea05\", \"key\": \"Feedback\", \"title\": \"Feedback [V3]\", \"artists\": \"(ref. Consequence) (prod. <PERSON> & Kanye <PERSON>)\", \"description\": \"Consequence \\\"Feedback\\\" reference track over the OG instrumental. Has lyrics from <PERSON><PERSON><PERSON>'s perspective, so it isn't a remix. Snippets originally previewed by him on Instagram Live on May 5th 2023 and July 22nd, 2024. He played the full song on live September 23rd, 2024. Unknown when it's from, but likely early-mid 2015.\", \"date\": 17270496, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"7193445f77e40a57456a8b4ebf5a79e8\", \"url\": \"https://api.pillowcase.su/api/download/7193445f77e40a57456a8b4ebf5a79e8\", \"size\": \"2.58 MB\", \"duration\": 148.14}", "aliases": [], "size": "2.58 MB"}, {"id": "floatin", "name": "Floatin", "artists": [], "producers": [], "notes": "Was originally believed to be from TurboGrafx16 until <PERSON><PERSON><PERSON>, who was given the song, revealed he recorded for it in 2015. Since this version was made before <PERSON><PERSON><PERSON>'s, that means this version is also 2015 or even earlier. Released officially by Os<PERSON> Chill in January 2021.", "length": "156.64", "fileDate": 15799968, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/be334839fce43c29d8df9193b64d87cb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/be334839fce43c29d8df9193b64d87cb\", \"key\": \"Floatin\", \"title\": \"Floatin\", \"aliases\": [\"We Floatin'\", \"Photogenic\", \"Super Starred\"], \"description\": \"Was originally believed to be from TurboGrafx16 until <PERSON><PERSON><PERSON>, who was given the song, revealed he recorded for it in 2015. Since this version was made before <PERSON><PERSON><PERSON>'s, that means this version is also 2015 or even earlier. Released officially by Osbe Chill in January 2021.\", \"date\": 15799968, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"800c53d3eb207606a1125eb2a702eacd\", \"url\": \"https://api.pillowcase.su/api/download/800c53d3eb207606a1125eb2a702eacd\", \"size\": \"4.92 MB\", \"duration\": 156.64}", "aliases": ["We Floatin'", "Photogenic", "Super Starred"], "size": "4.92 MB"}, {"id": "forever", "name": "<PERSON><PERSON><PERSON> - Forever [V1]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: Forever Hook Ideas\nVersion of \"Forever\" with a <PERSON><PERSON><PERSON> hook. Found in a folder labeled \"MTSxRIHANNA2016\", meaning this version was intended for <PERSON><PERSON><PERSON>. Likely dated earlier than 2016 as <PERSON><PERSON> was producing heavily for <PERSON><PERSON><PERSON>'s ANTI album.", "length": "134.16", "fileDate": 16015104, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/ab9a55af77d2243e7d144f5e316d58f3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ab9a55af77d2243e7d144f5e316d58f3\", \"key\": \"Forever\", \"title\": \"Rihanna - Forever [V1]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: Forever Hook Ideas\\nVersion of \\\"Forever\\\" with a <PERSON><PERSON><PERSON> hook. Found in a folder labeled \\\"MTSxRIHANNA2016\\\", meaning this version was intended for <PERSON><PERSON><PERSON>. Likely dated earlier than 2016 as <PERSON><PERSON> was producing heavily for <PERSON><PERSON><PERSON>'s ANTI album.\", \"date\": 16015104, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6b1ae2888d380b030500569f73d0e9cf\", \"url\": \"https://api.pillowcase.su/api/download/6b1ae2888d380b030500569f73d0e9cf\", \"size\": \"2.36 MB\", \"duration\": 134.16}", "aliases": [], "size": "2.36 MB"}, {"id": "forever-23", "name": "<PERSON><PERSON><PERSON> - Forever [V2]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: Forever 10 MTS\nAnother version of \"Forever\" with <PERSON><PERSON><PERSON> vocals. Most likely a reference for <PERSON><PERSON><PERSON> when <PERSON><PERSON><PERSON> was executive producing ANTI.", "length": "289.42", "fileDate": 16015104, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/6e274ffe928e7cae3de4e3f4bd413299", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6e274ffe928e7cae3de4e3f4bd413299\", \"key\": \"Forever\", \"title\": \"Rihanna - Forever [V2]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: Forever 10 MTS\\nAnother version of \\\"Forever\\\" with <PERSON><PERSON><PERSON> vocals. Most likely a reference for <PERSON><PERSON><PERSON> when <PERSON><PERSON><PERSON> was executive producing ANTI.\", \"date\": 16015104, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0af28c06afe82fc5e2018f531b0e5402\", \"url\": \"https://api.pillowcase.su/api/download/0af28c06afe82fc5e2018f531b0e5402\", \"size\": \"4.84 MB\", \"duration\": 289.42}", "aliases": [], "size": "4.84 MB"}, {"id": "forever-24", "name": "✨ Forever [V3]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Originally thought to have been the outro for \"Can U Be\" / \"Pressure\" (it's unrelated entirely). Likely from early 2015 as <PERSON> was working closely with <PERSON><PERSON><PERSON> & <PERSON><PERSON> on her album, as well as it being ripped from the same CD as \"Pick Up Your Speed\" - possibly a CD of references for <PERSON><PERSON><PERSON> - when sent to <PERSON>. There are acapella & instrumental files from July 2015, but those were likely bounced to send to <PERSON> and other producers. <PERSON> also took other Rihanna references for his album, such as \"Mitus Touch\" (later referred to as \"2 Rihanna's\"). The song title was confirmed when <PERSON><PERSON> referred to the snippet that leaked with Kanye vox as \"#Forever\". <PERSON><PERSON> later released a version of the instrumental on SoundCloud.", "length": "141.99", "fileDate": 16749504, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/e07f52d5fbfa233d6bd61fb941a94729", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e07f52d5fbfa233d6bd61fb941a94729\", \"key\": \"Forever\", \"title\": \"\\u2728 Forever [V3]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"Originally thought to have been the outro for \\\"Can U Be\\\" / \\\"Pressure\\\" (it's unrelated entirely). Likely from early 2015 as <PERSON> was working closely with <PERSON><PERSON><PERSON> & <PERSON><PERSON> on her album, as well as it being ripped from the same CD as \\\"Pick Up Your Speed\\\" - possibly a CD of references for R<PERSON>anna - when sent to <PERSON>. There are acapella & instrumental files from July 2015, but those were likely bounced to send to <PERSON> and other producers. <PERSON> also took other Rihanna references for his album, such as \\\"Mitus Touch\\\" (later referred to as \\\"2 R<PERSON><PERSON>'s\\\"). The song title was confirmed when <PERSON><PERSON> referred to the snippet that leaked with <PERSON>ny<PERSON> vox as \\\"#Forever\\\". <PERSON><PERSON> later released a version of the instrumental on SoundCloud.\", \"date\": 16749504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e84440a5f825ad5f4c8e7e40efbc589c\", \"url\": \"https://api.pillowcase.su/api/download/e84440a5f825ad5f4c8e7e40efbc589c\", \"size\": \"2.48 MB\", \"duration\": 141.99}", "aliases": [], "size": "2.48 MB"}, {"id": "4-5-seconds", "name": "4 5 Seconds [V5]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: KW - 4 5 Seconds Ref (7.22.14)\nEarly version of \"FourFiveSeconds\", included on a So Help Me God whiteboard tracklist from 2014. Has <PERSON> from Dirty Projectors on the bridge, and him and <PERSON><PERSON><PERSON> singing at the end of the track.", "length": "188.13", "fileDate": 16729632, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/20f5d98e5c71d7324b17a28e1391b072", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/20f5d98e5c71d7324b17a28e1391b072\", \"key\": \"4 5 Seconds\", \"title\": \"4 5 Seconds [V5]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"3\", \"Don't Wanna Fight\", \"Four Five Seconds\"], \"description\": \"OG Filename: KW - 4 5 Seconds Ref (7.22.14)\\nEarly version of \\\"FourFiveSeconds\\\", included on a So Help Me God whiteboard tracklist from 2014. Has <PERSON> from Dirty Projectors on the bridge, and him and <PERSON><PERSON><PERSON> singing at the end of the track.\", \"date\": 16729632, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bfe9fbd9a490d56422fe39f39fc7565f\", \"url\": \"https://api.pillowcase.su/api/download/bfe9fbd9a490d56422fe39f39fc7565f\", \"size\": \"5.42 MB\", \"duration\": 188.13}", "aliases": ["3", "Don't Wanna Fight", "Four Five Seconds"], "size": "5.42 MB"}, {"id": "four-five-seconds", "name": "Four Five Seconds [V6]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "Ty <PERSON>a $ign \"FourFiveSeconds\" reference track. <PERSON><PERSON><PERSON> is mumbling in the background. Used as the backing track for a performance of the song at FYF Fest 2015. CDQ snippet leaked December 19th, 2022.", "length": "13.09", "fileDate": 16714080, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/aaabc32c8939b26fc12f6bdd7384442e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/aaabc32c8939b26fc12f6bdd7384442e\", \"key\": \"Four Five Seconds\", \"title\": \"Four Five Seconds [V6]\", \"artists\": \"(ref. <PERSON> $ign) (feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"3\", \"Don't Wanna Fight\"], \"description\": \"Ty Dolla $ign \\\"FourFiveSeconds\\\" reference track. <PERSON><PERSON><PERSON> is mumbling in the background. Used as the backing track for a performance of the song at FYF Fest 2015. CDQ snippet leaked December 19th, 2022.\", \"date\": 16714080, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c2ab1c0349444d45b78819cc2613054e\", \"url\": \"https://api.pillowcase.su/api/download/c2ab1c0349444d45b78819cc2613054e\", \"size\": \"2.62 MB\", \"duration\": 13.09}", "aliases": ["3", "Don't Wanna Fight"], "size": "2.62 MB"}, {"id": "four-five-seconds-27", "name": "Four Five Seconds [V6]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "Ty <PERSON>a $ign \"FourFiveSeconds\" reference track. <PERSON><PERSON><PERSON> is mumbling in the background. Used as the backing track for a performance of the song at FYF Fest 2015. CDQ snippet leaked December 19th, 2022.", "length": "", "fileDate": 16714080, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://www.youtube.com/watch?v=vUEw7SePPSg", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.youtube.com/watch?v=vUEw7SePPSg\", \"key\": \"Four Five Seconds\", \"title\": \"Four Five Seconds [V6]\", \"artists\": \"(ref. <PERSON> $ign) (feat. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"3\", \"Don't Wanna Fight\"], \"description\": \"Ty Dolla $ign \\\"FourFiveSeconds\\\" reference track. <PERSON><PERSON><PERSON> is mumbling in the background. Used as the backing track for a performance of the song at FYF Fest 2015. CDQ snippet leaked December 19th, 2022.\", \"date\": 16714080, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["3", "Don't Wanna Fight"], "size": ""}, {"id": "four-five-seconds-don-t-wanna-fight", "name": "Four Five Seconds / Don't Wanna Fight [V9]", "artists": [], "producers": [], "notes": "OG Filename: Four Five Seconds - <PERSON><PERSON> Fight CORRECT VERSION\nOne of <PERSON>'s \"FourFiveSeconds\" reference tracks/ideas. Appears to be a voice memo.", "length": "107.79", "fileDate": 16716672, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/41be8f5ccbe9ad578d04f42063920768", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/41be8f5ccbe9ad578d04f42063920768\", \"key\": \"Four Five Seconds / Don't Wanna Fight\", \"title\": \"Four Five Seconds / Don't Wanna Fight [V9]\", \"artists\": \"(ref. KIRBY)\", \"aliases\": [\"3\", \"FourFiveSeconds\"], \"description\": \"OG Filename: Four Five Seconds - Dont Wanna Fight CORRECT VERSION\\nOne of <PERSON>'s \\\"FourFiveSeconds\\\" reference tracks/ideas. Appears to be a voice memo.\", \"date\": 16716672, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"26eb06d51e596136c5d7509f4fdd6450\", \"url\": \"https://api.pillowcase.su/api/download/26eb06d51e596136c5d7509f4fdd6450\", \"size\": \"3.27 MB\", \"duration\": 107.79}", "aliases": ["3", "FourFiveSeconds"], "size": "3.27 MB"}, {"id": "fourfiveseconds", "name": "R<PERSON>anna - FourFiveSeconds [V14]", "artists": ["Kanye West", "<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: FOUR FIVE SECONDS UP 5 (03)\nA version of \"FourFiveSeconds\" where <PERSON><PERSON><PERSON> does all of <PERSON><PERSON><PERSON>'s vocals placing it after \"3\". <PERSON><PERSON><PERSON>'s mumble demo vocals pitched up are present and <PERSON>'s pitched up vocals are also present as well. Recorded in 2014.", "length": "190.85", "fileDate": 17006976, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/dfc89eab247292097decd1324fc39df6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dfc89eab247292097decd1324fc39df6\", \"key\": \"FourFiveSeconds\", \"title\": \"Rihanna - FourFiveSeconds [V14]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"3\", \"Don't Wanna Fight\"], \"description\": \"OG Filename: FOUR FIVE SECONDS UP 5 (03)\\nA version of \\\"FourFiveSeconds\\\" where <PERSON><PERSON><PERSON> does all of <PERSON><PERSON><PERSON>'s vocals placing it after \\\"3\\\". <PERSON><PERSON><PERSON>'s mumble demo vocals pitched up are present and <PERSON>'s pitched up vocals are also present as well. Recorded in 2014.\", \"date\": 17006976, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"cf46e18be1007d1d7297bfd1da4b253f\", \"url\": \"https://api.pillowcase.su/api/download/cf46e18be1007d1d7297bfd1da4b253f\", \"size\": \"5.47 MB\", \"duration\": 190.85}", "aliases": ["3", "Don't Wanna Fight"], "size": "5.47 MB"}, {"id": "go-pro", "name": "Go Pro [V1]", "artists": [], "producers": [], "notes": "Solo Madonna reference <PERSON> recorded over. Can be heard in the bleed of a specific version of the \"Highlights\" stems.", "length": "3.38", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/cba14673558b23c69b4830ce3fe0920a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cba14673558b23c69b4830ce3fe0920a\", \"key\": \"Go Pro\", \"title\": \"Go Pro [V1]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"Highlights\"], \"description\": \"Solo Madonna reference Ye recorded over. Can be heard in the bleed of a specific version of the \\\"Highlights\\\" stems.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"521ca1ffd4bb0d1465c7e79fe801ab36\", \"url\": \"https://api.pillowcase.su/api/download/521ca1ffd4bb0d1465c7e79fe801ab36\", \"size\": \"2.46 MB\", \"duration\": 3.38}", "aliases": ["Highlights"], "size": "2.46 MB"}, {"id": "go-pro-31", "name": "⭐️ Go Pro [V2]", "artists": ["<PERSON>"], "producers": [], "notes": "OG Filename: Go Pro Ref 12.29.14 FOR RYAN JOHNSON\nCreated while <PERSON><PERSON><PERSON> was helping <PERSON> work on her album Rebel Heart. Has the same drums as \"Down Town\", along with other similarities that lead people to believe it may have evolved into this song. Original snippet leaked May 5th, 2023. Full tagged file leaked after a successful Soakbuy. Edit that removes the tag can be found in Fakes tab.", "length": "221.28", "fileDate": 17385408, "leakDate": "", "availableLength": "Tagged", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/2db692d5e97732b20b17d34ba6fdc2e9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2db692d5e97732b20b17d34ba6fdc2e9\", \"key\": \"Go Pro\", \"title\": \"\\u2b50\\ufe0f Go Pro [V2]\", \"artists\": \"(ref. <PERSON>) (feat. <PERSON>)\", \"aliases\": [\"Highlights\"], \"description\": \"OG Filename: Go Pro Ref 12.29.14 FOR RYAN JOHNSON\\nCreated while <PERSON><PERSON><PERSON> was helping <PERSON> work on her album Rebel Heart. Has the same drums as \\\"Down Town\\\", along with other similarities that lead people to believe it may have evolved into this song. Original snippet leaked May 5th, 2023. Full tagged file leaked after a successful Soakbuy. Edit that removes the tag can be found in Fakes tab.\", \"date\": 17385408, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a57a7d5783eaee78d1900994461f96e6\", \"url\": \"https://api.pillowcase.su/api/download/a57a7d5783eaee78d1900994461f96e6\", \"size\": \"5.95 MB\", \"duration\": 221.28}", "aliases": ["Highlights"], "size": "5.95 MB"}, {"id": "i-feel-like-that", "name": "✨ I Feel Like That [V5]", "artists": ["Ty Dolla $ign", "The WRLDFMS <PERSON>"], "producers": ["MIKE DEAN"], "notes": "OG Filename: KW - I Feel Like That Ref (8.20.14)\nEarly version of the August 22nd demo. Contains some rough mumbly singing and freestyle vocals over the entire song and some parts that sound semi-finished, which evenetually ended up being cut in favor of a purely singing.", "length": "358.67", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/68c0f592a5b3774b6292cd43cdd73502", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/68c0f592a5b3774b6292cd43cdd73502\", \"key\": \"I Feel Like That\", \"title\": \"\\u2728 I Feel Like That [V5]\", \"artists\": \"(feat. <PERSON>ign & The WRLDFMS <PERSON>) (prod. <PERSON><PERSON> DEAN)\", \"description\": \"OG Filename: KW - I Feel Like That Ref (8.20.14)\\nEarly version of the August 22nd demo. Contains some rough mumbly singing and freestyle vocals over the entire song and some parts that sound semi-finished, which evenetually ended up being cut in favor of a purely singing.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7f662b8a3cc0c9da23e93efea7eedd84\", \"url\": \"https://api.pillowcase.su/api/download/7f662b8a3cc0c9da23e93efea7eedd84\", \"size\": \"8.15 MB\", \"duration\": 358.67}", "aliases": [], "size": "8.15 MB"}, {"id": "i-feel-like-that-33", "name": "I Feel Like That [V7]", "artists": ["Ty Dolla $ign", "The WRLDFMS <PERSON>"], "producers": ["MIKE DEAN"], "notes": "OG Filename: I Feel Like That ( <PERSON>) 8.27.14\nAlternate version of \"I Feel Like That\" with the same Yeezus 2 production and <PERSON> reference vocals.", "length": "142.73", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/fdb51b285697205cfb9df9c8b7d0dc1c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fdb51b285697205cfb9df9c8b7d0dc1c\", \"key\": \"I Feel Like That\", \"title\": \"I Feel Like That [V7]\", \"artists\": \"(ref. <PERSON>IR<PERSON><PERSON>) (feat. <PERSON> $ign & The WRLDFMS <PERSON>) (prod. <PERSON>KE DEAN)\", \"description\": \"OG Filename: I Feel Like That ( <PERSON> Ref) 8.27.14\\nAlternate version of \\\"I Feel Like That\\\" with the same Yeezus 2 production and <PERSON> reference vocals.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f902c233296cfe62d930bbb5427745c2\", \"url\": \"https://api.pillowcase.su/api/download/f902c233296cfe62d930bbb5427745c2\", \"size\": \"4.69 MB\", \"duration\": 142.73}", "aliases": [], "size": "4.69 MB"}, {"id": "i-feel-like-that-34", "name": "I Feel Like That [V8]", "artists": [], "producers": ["MIKE DEAN", "Hudson Mohawke"], "notes": "OG Filename: KW - Feel Like that REf 1...\nAlternate version of \"I Feel Like That\" with similar production to the MV version, but lacking a lot (synths, drums, etc).", "length": "228.62", "fileDate": 16698528, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/750f2fcb998e8afb7314548960373744", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/750f2fcb998e8afb7314548960373744\", \"key\": \"I Feel Like That\", \"title\": \"I Feel Like That [V8]\", \"artists\": \"(prod. <PERSON>KE DEAN & Hudson Mohawke)\", \"description\": \"OG Filename: KW - Feel Like that REf 1...\\nAlternate version of \\\"I Feel Like That\\\" with similar production to the MV version, but lacking a lot (synths, drums, etc).\", \"date\": 16698528, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5050ec3ae6f1195df8d4a700e7357fe0\", \"url\": \"https://api.pillowcase.su/api/download/5050ec3ae6f1195df8d4a700e7357fe0\", \"size\": \"6.07 MB\", \"duration\": 228.62}", "aliases": [], "size": "6.07 MB"}, {"id": "i-feel-like-that-35", "name": "⭐ I Feel Like That [V9]", "artists": [], "producers": ["MIKE DEAN", "Hudson Mohawke"], "notes": "Throwaway taken from the scrapped \"All Day\" music video. OG file hasn't been released yet, so that's why <PERSON><PERSON><PERSON>'s breaths are still in the song. Features from Yeezus 2 have been removed and added again on later versions. Contains a completely reworked instrumental from the Yeezus 2 version. Drums from this version were later reused on <PERSON>'s song \"Indian Steps\".", "length": "222.15", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/77ec1a74cf31ecc09a751147f01bf219", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/77ec1a74cf31ecc09a751147f01bf219\", \"key\": \"I Feel Like That\", \"title\": \"\\u2b50 I Feel Like That [V9]\", \"artists\": \"(prod. <PERSON><PERSON> DEAN & Hudson Mohawke)\", \"description\": \"Throwaway taken from the scrapped \\\"All Day\\\" music video. OG file hasn't been released yet, so that's why <PERSON><PERSON><PERSON>'s breaths are still in the song. Features from Yeezus 2 have been removed and added again on later versions. Contains a completely reworked instrumental from the Yeezus 2 version. Drums from this version were later reused on <PERSON> Mohawke's song \\\"Indian Steps\\\".\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dfc7841ed29cc10bec7a535a390c3ba1\", \"url\": \"https://api.pillowcase.su/api/download/dfc7841ed29cc10bec7a535a390c3ba1\", \"size\": \"5.96 MB\", \"duration\": 222.15}", "aliases": [], "size": "5.96 MB"}, {"id": "i-feel-like-that-36", "name": "✨ I Feel Like That [V10]", "artists": ["Ty Dolla $ign", "The WRLDFMS <PERSON>"], "producers": ["MIKE DEAN"], "notes": "OG Filename: KW - I Feel Like That Ref (6.15.15)\nVersion played at Glastonbury 2015. Low quality audio leaked from the stage setup, and a CDQ snippet leaked in July 2021. OG file leaked after a successful Soakbuy.", "length": "274.37", "fileDate": 17382816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/b9c190131265ccf572045948944bdc5a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b9c190131265ccf572045948944bdc5a\", \"key\": \"I Feel Like That\", \"title\": \"\\u2728 I Feel Like That [V10]\", \"artists\": \"(feat. <PERSON>ign & The WRLDFMS <PERSON>) (prod. <PERSON><PERSON> DEAN)\", \"description\": \"OG Filename: KW - I Feel Like That Ref (6.15.15)\\nVersion played at Glastonbury 2015. Low quality audio leaked from the stage setup, and a CDQ snippet leaked in July 2021. OG file leaked after a successful Soakbuy.\", \"date\": 17382816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f3f1f24fd311292ee0e80c7416e5e433\", \"url\": \"https://api.pillowcase.su/api/download/f3f1f24fd311292ee0e80c7416e5e433\", \"size\": \"6.8 MB\", \"duration\": 274.37}", "aliases": [], "size": "6.8 MB"}, {"id": "man-up", "name": "Man Up [V6]", "artists": ["<PERSON> Thug"], "producers": [], "notes": "OG Filename: ....MAN UP\nHas slight production differences on the hook with what sounds like a brass instrument added. Snippet posted by MusicMafia in 2018. Leaked for free.", "length": "159.91", "fileDate": 17218656, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/b742a589675d8754722a3f80476b7e07", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b742a589675d8754722a3f80476b7e07\", \"key\": \"Man Up\", \"title\": \"Man Up [V6]\", \"artists\": \"(feat. <PERSON> Thug)\", \"aliases\": [\"Bad Night\", \"Rap Tarantino\", \"Capri-Sun\", \"Too Reckless\", \"Bad Guy\"], \"description\": \"OG Filename: ....MAN UP\\nHas slight production differences on the hook with what sounds like a brass instrument added. Snippet posted by MusicMafia in 2018. Leaked for free.\", \"date\": 17218656, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6594881e17ff975fd7e33af8e0e8892f\", \"url\": \"https://api.pillowcase.su/api/download/6594881e17ff975fd7e33af8e0e8892f\", \"size\": \"4.97 MB\", \"duration\": 159.91}", "aliases": ["Bad Night", "<PERSON>", "Capri-Sun", "Too Re<PERSON>ss", "Bad Guy"], "size": "4.97 MB"}, {"id": "mass-appeal", "name": "Mass Appeal", "artists": [], "producers": ["DJ Premier"], "notes": "OG Filename: <PERSON> - Mass Appeal KW Ref\nFreestyle over <PERSON>'s \"Mass Appeal.\" Original file leaked August 19th, 2021.", "length": "64.18", "fileDate": 15706656, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/4606fdec55a3c6859e62f7fc68e8615b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4606fdec55a3c6859e62f7fc68e8615b\", \"key\": \"Mass Appeal\", \"title\": \"Mass Appeal\", \"artists\": \"(prod. DJ Premier)\", \"aliases\": [\"Appeal\"], \"description\": \"OG Filename: <PERSON> Starr - Mass Appeal KW Ref\\nFreestyle over <PERSON>'s \\\"Mass Appeal.\\\" Original file leaked August 19th, 2021.\", \"date\": 15706656, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7d3b46c86dc168a5c68b7647be085abe\", \"url\": \"https://api.pillowcase.su/api/download/7d3b46c86dc168a5c68b7647be085abe\", \"size\": \"3.44 MB\", \"duration\": 64.18}", "aliases": ["Appeal"], "size": "3.44 MB"}, {"id": "only-one", "name": "Only One [V7]", "artists": ["<PERSON>", "Ty Dolla $ign"], "producers": ["<PERSON>", "<PERSON>", "MIKE DEAN"], "notes": "OG Filename: <PERSON><PERSON><PERSON> - Only One 12-31-14 44-24 MFiT\nOG file for the released version.", "length": "279.88", "fileDate": 16048800, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/50861be7cbf2bbdd9d51881be1da864b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/50861be7cbf2bbdd9d51881be1da864b\", \"key\": \"Only One\", \"title\": \"Only One [V7]\", \"artists\": \"(feat. <PERSON> & <PERSON> $ign) (prod. <PERSON>, <PERSON> & <PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON><PERSON> - Only One 12-31-14 44-24 MFiT\\nOG file for the released version.\", \"date\": 16048800, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"612645a3e4240f9f281d550f56310c7c\", \"url\": \"https://api.pillowcase.su/api/download/612645a3e4240f9f281d550f56310c7c\", \"size\": \"6.89 MB\", \"duration\": 279.88}", "aliases": [], "size": "6.89 MB"}, {"id": "", "name": "??? - ??? [V1]", "artists": [], "producers": [], "notes": "In the sessions, a different beat can be heard in <PERSON><PERSON>'s vocal stem. Contains a rougher B<PERSON> verse take. Potentially not even <PERSON>'s song at this point - maybe originally for <PERSON><PERSON><PERSON>?", "length": "7.32", "fileDate": 17199648, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/8b5b4c2a48c504e2c16d2319f2943d4f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8b5b4c2a48c504e2c16d2319f2943d4f\", \"key\": \"???\", \"title\": \"??? - ??? [V1]\", \"aliases\": [\"Accelerate\", \"Auxillary\"], \"description\": \"In the sessions, a different beat can be heard in <PERSON><PERSON>'s vocal stem. Contains a rougher B<PERSON> Bo<PERSON>lly verse take. Potentially not even <PERSON>'s song at this point - maybe originally for Rihanna?\", \"date\": 17199648, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"e7d958b305c90e6056f33d9285d4dfb5\", \"url\": \"https://api.pillowcase.su/api/download/e7d958b305c90e6056f33d9285d4dfb5\", \"size\": \"2.53 MB\", \"duration\": 7.32}", "aliases": ["Accelerate", "Auxillary"], "size": "2.53 MB"}, {"id": "-41", "name": "??? - ??? [V1]", "artists": [], "producers": [], "notes": "In the sessions, a different beat can be heard in <PERSON><PERSON>'s vocal stem. Contains a rougher B<PERSON> verse take. Potentially not even <PERSON>'s song at this point - maybe originally for <PERSON><PERSON><PERSON>?", "length": "7.92", "fileDate": 17199648, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/7dbb250fbaf613709869d1c8155900a4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7dbb250fbaf613709869d1c8155900a4\", \"key\": \"???\", \"title\": \"??? - ??? [V1]\", \"aliases\": [\"Accelerate\", \"Auxillary\"], \"description\": \"In the sessions, a different beat can be heard in <PERSON><PERSON>'s vocal stem. Contains a rougher B<PERSON>lly verse take. Potentially not even <PERSON>'s song at this point - maybe originally for Rihanna?\", \"date\": 17199648, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"09c4d1ae8ca5f72f9888aea8bcb96fd8\", \"url\": \"https://api.pillowcase.su/api/download/09c4d1ae8ca5f72f9888aea8bcb96fd8\", \"size\": \"2.54 MB\", \"duration\": 7.92}", "aliases": ["Accelerate", "Auxillary"], "size": "2.54 MB"}, {"id": "pick-up-your-speed", "name": "Pick Up Your Speed [V2]", "artists": [], "producers": [], "notes": "In the sessions, a version of the original beat with different drums can be heard in <PERSON>'s vocal bleed. This version is solo <PERSON><PERSON>. Contains a rougher B<PERSON> Bo<PERSON> verse take.", "length": "17.38", "fileDate": 17199648, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/72a4952eb902e68d6b8ef84a254b9c57", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/72a4952eb902e68d6b8ef84a254b9c57\", \"key\": \"Pick Up Your Speed\", \"title\": \"Pick Up Your Speed [V2]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"aliases\": [\"Accelerate\", \"Auxillary\"], \"description\": \"In the sessions, a version of the original beat with different drums can be heard in <PERSON>'s vocal bleed. This version is solo <PERSON><PERSON> Bourelly. Contains a rougher Bibi Bourelly verse take.\", \"date\": 17199648, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"dd28dae74d50e8f208c080716feb0ee5\", \"url\": \"https://api.pillowcase.su/api/download/dd28dae74d50e8f208c080716feb0ee5\", \"size\": \"2.69 MB\", \"duration\": 17.38}", "aliases": ["Accelerate", "Auxillary"], "size": "2.69 MB"}, {"id": "pick-up-your-speed-43", "name": "Pick Up Your Speed [V4]", "artists": [], "producers": [], "notes": "Version of \"Pick Up Your Speed\" with a new beat similar to later versions. Contains rerecorded <PERSON><PERSON> verse vocals.", "length": "7.63", "fileDate": 17199648, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/bbd5a8f1b9e2f621006e5a2b64aa7d81", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bbd5a8f1b9e2f621006e5a2b64aa7d81\", \"key\": \"Pick Up Your Speed\", \"title\": \"Pick Up Your Speed [V4]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"aliases\": [\"Accelerate\", \"Auxillary\"], \"description\": \"Version of \\\"Pick Up Your Speed\\\" with a new beat similar to later versions. Contains rerecorded B<PERSON> Bourelly verse vocals.\", \"date\": 17199648, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"a590d1161c1443c1e31a22a3157b34c1\", \"url\": \"https://api.pillowcase.su/api/download/a590d1161c1443c1e31a22a3157b34c1\", \"size\": \"2.53 MB\", \"duration\": 7.63}", "aliases": ["Accelerate", "Auxillary"], "size": "2.53 MB"}, {"id": "pick-up-your-speed-44", "name": "Pick Up Your Speed [V7]", "artists": [], "producers": [], "notes": "OG Filename: KW - Pick Up Your Speed Ref 2 (1.15.15) \nMumble demo with back and forth vocals between <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Contains rerecorded <PERSON><PERSON> vocals. Samples a KIRBY song titled \"Come On\" (more info about it in it's own entry). Given to <PERSON> in December 2015, released as \"Accelerate\" with Ty <PERSON> $ign.", "length": "122.53", "fileDate": 17018208, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/f59c816db13ae643f22648e9bd822916", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f59c816db13ae643f22648e9bd822916\", \"key\": \"Pick Up Your Speed\", \"title\": \"Pick Up Your Speed [V7]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"aliases\": [\"Accelerate\", \"Auxillary\"], \"description\": \"OG Filename: KW - Pick Up Your Speed Ref 2 (1.15.15) \\nMumble demo with back and forth vocals between <PERSON><PERSON><PERSON> and <PERSON><PERSON>. Contains rerecorded <PERSON><PERSON> vocals. Samples a KIRBY song titled \\\"Come On\\\" (more info about it in it's own entry). Given to <PERSON> in December 2015, released as \\\"Accelerate\\\" with Ty <PERSON> $ign.\", \"date\": 17018208, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d263f17e50ab8d7e089359514ebc893e\", \"url\": \"https://api.pillowcase.su/api/download/d263f17e50ab8d7e089359514ebc893e\", \"size\": \"2.17 MB\", \"duration\": 122.53}", "aliases": ["Accelerate", "Auxillary"], "size": "2.17 MB"}, {"id": "auxillary", "name": "Auxillary [V8]", "artists": [], "producers": [], "notes": "OG Filename: KW - Auxillary Ref (5.19.15)\nFull unofficial bounce for a 2015 version of \"Auxillary\".", "length": "108.75", "fileDate": 17199648, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/5b855f7dbe9b96c3a09ebc32ab156c82", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5b855f7dbe9b96c3a09ebc32ab156c82\", \"key\": \"Auxillary\", \"title\": \"Auxillary [V8]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"aliases\": [\"Accelerate\", \"Pick Up Your Speed\"], \"description\": \"OG Filename: KW - Auxillary Ref (5.19.15)\\nFull unofficial bounce for a 2015 version of \\\"Auxillary\\\".\", \"date\": 17199648, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"7628fff65e2e1689d83ef1da7ebb026d\", \"url\": \"https://api.pillowcase.su/api/download/7628fff65e2e1689d83ef1da7ebb026d\", \"size\": \"4.15 MB\", \"duration\": 108.75}", "aliases": ["Accelerate", "Pick Up Your Speed"], "size": "4.15 MB"}, {"id": "piss-on-your-grave", "name": "Piss On Your Grave [V4]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "Solo version with mumble bars before it was given to <PERSON> in mid 2015. Shown to label execs in December 2014 or January 2015 as a SHMG album track. Stems for this version are dated as being from October 2nd, 2014. Leaked July 16th, 2019 as a stem bounce.", "length": "108.58", "fileDate": 15632352, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/66ab7e186916f136b7f46e4590048800", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/66ab7e186916f136b7f46e4590048800\", \"key\": \"Piss On Your Grave\", \"title\": \"Piss On Your Grave [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & Charlie Heat)\", \"description\": \"Solo version with mumble bars before it was given to <PERSON> in mid 2015. Shown to label execs in December 2014 or January 2015 as a SHMG album track. Stems for this version are dated as being from October 2nd, 2014. Leaked July 16th, 2019 as a stem bounce.\", \"date\": 15632352, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ca3c7a2a8a86075702a1bdc370f31bab\", \"url\": \"https://api.pillowcase.su/api/download/ca3c7a2a8a86075702a1bdc370f31bab\", \"size\": \"3 MB\", \"duration\": 108.58}", "aliases": [], "size": "3 MB"}, {"id": "prayer", "name": "Prayer [V2]", "artists": ["<PERSON>"], "producers": [], "notes": "OG Filename: JV CHOIR KIRBY REF 10 18 14\nVersion of \"Prayer\" with reference vocals from <PERSON>.", "length": "215.48", "fileDate": 16960320, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/c2632ff3f2ab33f3c21a3f35ae0d6d3c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c2632ff3f2ab33f3c21a3f35ae0d6d3c\", \"key\": \"Prayer\", \"title\": \"Prayer [V2]\", \"artists\": \"(ref. <PERSON>IRB<PERSON>) (feat. <PERSON>)\", \"description\": \"OG Filename: JV CHOIR KIRBY REF 10 18 14\\nVersion of \\\"Prayer\\\" with reference vocals from <PERSON>.\", \"date\": 16960320, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"8d8396b2cb4dad1aacf49baae2193d25\", \"url\": \"https://api.pillowcase.su/api/download/8d8396b2cb4dad1aacf49baae2193d25\", \"size\": \"5.86 MB\", \"duration\": 215.48}", "aliases": [], "size": "5.86 MB"}, {"id": "pressure", "name": "Pressure [V5]", "artists": [], "producers": ["Havoc"], "notes": "Version made after the song became \"Pressure\". Has a completely different beat compared to all previous \"Never Let Me Go\" versions. Played backstage at Glastonbury 2015 before <PERSON><PERSON><PERSON>'s set.", "length": "", "fileDate": 14353632, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/4bff3d2d6c290e0852a1825186f39b59", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4bff3d2d6c290e0852a1825186f39b59\", \"key\": \"Pressure\", \"title\": \"Pressure [V5]\", \"artists\": \"(prod. Havoc)\", \"aliases\": [\"CAN U BE\", \"Never Let Me Go\"], \"description\": \"Version made after the song became \\\"Pressure\\\". Has a completely different beat compared to all previous \\\"Never Let Me Go\\\" versions. Played backstage at Glastonbury 2015 before <PERSON><PERSON><PERSON>'s set.\", \"date\": 14353632, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["CAN U BE", "Never Let Me Go"], "size": ""}, {"id": "pressure-49", "name": "Pressure [V5]", "artists": [], "producers": ["Havoc"], "notes": "Version made after the song became \"Pressure\". Has a completely different beat compared to all previous \"Never Let Me Go\" versions. Played backstage at Glastonbury 2015 before <PERSON><PERSON><PERSON>'s set.", "length": "116.04", "fileDate": 14353632, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/7cd492aaa0279059c382dc897f996ae7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7cd492aaa0279059c382dc897f996ae7\", \"key\": \"Pressure\", \"title\": \"Pressure [V5]\", \"artists\": \"(prod. Havoc)\", \"aliases\": [\"CAN U BE\", \"Never Let Me Go\"], \"description\": \"Version made after the song became \\\"Pressure\\\". Has a completely different beat compared to all previous \\\"Never Let Me Go\\\" versions. Played backstage at Glastonbury 2015 before <PERSON><PERSON><PERSON>'s set.\", \"date\": 14353632, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"5a3632d98f0986f298bdeae00e5a046a\", \"url\": \"https://api.pillowcase.su/api/download/5a3632d98f0986f298bdeae00e5a046a\", \"size\": \"4.27 MB\", \"duration\": 116.04}", "aliases": ["CAN U BE", "Never Let Me Go"], "size": "4.27 MB"}, {"id": "rule-the-world", "name": "Rule The World [V7]", "artists": [], "producers": ["Lido", "<PERSON> Made-It"], "notes": "OG Filename: Rule The World Kirby Ref (7.20.14)\n<PERSON> \"Rule The World\" reference track. Dated months later than the other versions.", "length": "242.08", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/18900323f74adc5d14fa1ac28e1c1bd1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/18900323f74adc5d14fa1ac28e1c1bd1\", \"key\": \"Rule The World\", \"title\": \"Rule The World [V7]\", \"artists\": \"(ref. KIRBY) (prod. <PERSON><PERSON> & <PERSON>LL Made-It)\", \"aliases\": [\"Everybody Wants To Rule The World\", \"Black Skinhead Remix\"], \"description\": \"OG Filename: Rule The World Kirby Ref (7.20.14)\\n<PERSON>irby <PERSON> \\\"Rule The World\\\" reference track. Dated months later than the other versions.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7806618aa1ab93b2c69cd3b8a19f2a90\", \"url\": \"https://api.pillowcase.su/api/download/7806618aa1ab93b2c69cd3b8a19f2a90\", \"size\": \"6.28 MB\", \"duration\": 242.08}", "aliases": ["Everybody Wants To Rule The World", "Black Skinhead Remix"], "size": "6.28 MB"}, {"id": "southside-serenade", "name": "Southside Serenade [V3]", "artists": [], "producers": [], "notes": "Version of \"Southside Serenade\" with worse mixing and production compared to later versions. Leaked after a successful Soakbuy.", "length": "86.63", "fileDate": 17385408, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/8b89977bbf5e7baea8e63a1e215d710f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8b89977bbf5e7baea8e63a1e215d710f\", \"key\": \"Southside Serenade\", \"title\": \"Southside Serenade [V3]\", \"description\": \"Version of \\\"Southside Serenade\\\" with worse mixing and production compared to later versions. Leaked after a successful Soakbuy.\", \"date\": 17385408, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"247d175444c1b7a5f7cb62ba4c0957e9\", \"url\": \"https://api.pillowcase.su/api/download/247d175444c1b7a5f7cb62ba4c0957e9\", \"size\": \"3.8 MB\", \"duration\": 86.63}", "aliases": [], "size": "3.8 MB"}, {"id": "southside-serenade-52", "name": "⭐ Southside Serenade [V4]", "artists": [], "producers": [], "notes": "Finished track with singing. <PERSON><PERSON><PERSON> can be seen singing the lyrics in the 2nd part of the \"All Day\" Revolt studio session video with <PERSON>. Samples \"Manhattan Island Serenade\" by <PERSON>, and \"All Day\".", "length": "127.97", "fileDate": 15198624, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/68812ae8208bb0b01b511b99281577cf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/68812ae8208bb0b01b511b99281577cf\", \"key\": \"Southside Serenade\", \"title\": \"\\u2b50 Southside Serenade [V4]\", \"description\": \"Finished track with singing. <PERSON><PERSON><PERSON> can be seen singing the lyrics in the 2nd part of the \\\"All Day\\\" Revolt studio session video with <PERSON>. <PERSON> \\\"Manhattan Island Serenade\\\" by <PERSON>, and \\\"All Day\\\".\", \"date\": 15198624, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"34e7def62ea45f39e124532e0beaec57\", \"url\": \"https://api.pillowcase.su/api/download/34e7def62ea45f39e124532e0beaec57\", \"size\": \"4.46 MB\", \"duration\": 127.97}", "aliases": [], "size": "4.46 MB"}, {"id": "southside-serenade-53", "name": "Southside Serenade [V5]", "artists": ["<PERSON> and the Lights"], "producers": ["Cashmere Cat"], "notes": "Version with <PERSON> and the Lights background vocals. Has some different lyrics and altered production, including a different beat drop.", "length": "116.77", "fileDate": 16761600, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/a60423797120b5c746a64c512d4c0e7c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a60423797120b5c746a64c512d4c0e7c\", \"key\": \"Southside Serenade\", \"title\": \"Southside Serenade [V5]\", \"artists\": \"(feat. <PERSON> and the Lights) (prod. Cash<PERSON> Cat)\", \"description\": \"Version with <PERSON> and the Lights background vocals. Has some different lyrics and altered production, including a different beat drop.\", \"date\": 16761600, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"cac0be71001fd44edc6c9e3cfe45c505\", \"url\": \"https://api.pillowcase.su/api/download/cac0be71001fd44edc6c9e3cfe45c505\", \"size\": \"4.28 MB\", \"duration\": 116.77}", "aliases": [], "size": "4.28 MB"}, {"id": "the-mind-is-powerful", "name": "The Mind Is Powerful [V1]", "artists": [], "producers": ["88-<PERSON>"], "notes": "So Help Me God-era throwaway. Samples \"Jungle\" by <PERSON>, and a live performance of \"Mary Jane\" by <PERSON>.", "length": "269.04", "fileDate": 14557536, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/c25a42a45ce4a4bd87020fc26c47bc56", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c25a42a45ce4a4bd87020fc26c47bc56\", \"key\": \"The Mind Is Powerful\", \"title\": \"The Mind Is Powerful [V1]\", \"artists\": \"(prod. 88-<PERSON>)\", \"aliases\": [\"Start Time\", \"Star Time\"], \"description\": \"So Help Me God-era throwaway. Samples \\\"Jungle\\\" by <PERSON>, and a live performance of \\\"Mary Jane\\\" by <PERSON>.\", \"date\": 14557536, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9b56eec9d9aa60dfcde8721c6c8cad0e\", \"url\": \"https://api.pillowcase.su/api/download/9b56eec9d9aa60dfcde8721c6c8cad0e\", \"size\": \"6.71 MB\", \"duration\": 269.04}", "aliases": ["Start Time", "Star Time"], "size": "6.71 MB"}, {"id": "mind-is-powerful", "name": "Mind Is Powerful [V2]", "artists": [], "producers": ["88-<PERSON>"], "notes": "Said to be made sometime in 2014 So Help Me God sessions. Snippet leaked June 22nd 2024. Most likely a feature, but currently unknown.", "length": "10.19", "fileDate": 17190144, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/ee61d81d37bd3823882ebd83d6a5da46", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ee61d81d37bd3823882ebd83d6a5da46\", \"key\": \"Mind Is Powerful\", \"title\": \"Mind Is Powerful [V2]\", \"artists\": \"(???. <PERSON><PERSON><PERSON>) (prod. 88-Keys)\", \"aliases\": [\"The Mind Is Powerful\", \"Star Time\", \"Start Time\"], \"description\": \"Said to be made sometime in 2014 So Help Me God sessions. Snippet leaked June 22nd 2024. Most likely a feature, but currently unknown.\", \"date\": 17190144, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d540369f45df226bea02e8667ff8cba2\", \"url\": \"https://api.pillowcase.su/api/download/d540369f45df226bea02e8667ff8cba2\", \"size\": \"2.57 MB\", \"duration\": 10.19}", "aliases": ["The Mind Is Powerful", "Star Time", "Start Time"], "size": "2.57 MB"}, {"id": "start-time", "name": "Start Time [V3]", "artists": ["The-Dream"], "producers": ["88-<PERSON>"], "notes": "OG Filename: KW - Start Time Ref (2.4.15)\nVersion where <PERSON><PERSON><PERSON> replaces <PERSON><PERSON><PERSON> on the hook. Has additional drums.", "length": "269.52", "fileDate": 16988832, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/afc1f54d0a4f866895a14fcd73b4e8cf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/afc1f54d0a4f866895a14fcd73b4e8cf\", \"key\": \"Start Time\", \"title\": \"Start Time [V3]\", \"artists\": \"(feat. <PERSON>-<PERSON>) (prod. 88-Keys)\", \"aliases\": [\"The Mind Is Powerful\", \"Star Time\"], \"description\": \"OG Filename: KW - Start Time Ref (2.4.15)\\nVersion where <PERSON><PERSON><PERSON> replaces <PERSON><PERSON><PERSON> on the hook. Has additional drums.\", \"date\": 16988832, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"36f1517479ee244422f0789f45ba7b1d\", \"url\": \"https://api.pillowcase.su/api/download/36f1517479ee244422f0789f45ba7b1d\", \"size\": \"6.73 MB\", \"duration\": 269.52}", "aliases": ["The Mind Is Powerful", "Star Time"], "size": "6.73 MB"}, {"id": "star-time", "name": "Star Time [V4]", "artists": [], "producers": ["88-<PERSON>"], "notes": "Version with vocals from an unknown artist(s), likely a reference. Dated 2015.", "length": "9.04", "fileDate": 17324928, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/51d9fe23658f52744a172d31a2f0b8ae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/51d9fe23658f52744a172d31a2f0b8ae\", \"key\": \"Star Time\", \"title\": \"Star Time [V4]\", \"artists\": \"(ref. ??? & ???) (prod. 88-Keys)\", \"aliases\": [\"The Mind Is Powerful\", \"Start Time\"], \"description\": \"Version with vocals from an unknown artist(s), likely a reference. Dated 2015.\", \"date\": 17324928, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c9021d689877cf7c9ca9d5cc1c5c0ac7\", \"url\": \"https://api.pillowcase.su/api/download/c9021d689877cf7c9ca9d5cc1c5c0ac7\", \"size\": \"2.55 MB\", \"duration\": 9.04}", "aliases": ["The Mind Is Powerful", "Start Time"], "size": "2.55 MB"}, {"id": "star-time-58", "name": "Star Time [V5]", "artists": [], "producers": ["88-<PERSON>"], "notes": "Version with vocals from an unknown artist, likely a reference. Dated 2015.", "length": "5.77", "fileDate": 17324928, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/654dc2a574fcb264597719acfcd7d002", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/654dc2a574fcb264597719acfcd7d002\", \"key\": \"Star Time\", \"title\": \"Star Time [V5]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. 88-Keys)\", \"aliases\": [\"The Mind Is Powerful\", \"Start Time\"], \"description\": \"Version with vocals from an unknown artist, likely a reference. Dated 2015.\", \"date\": 17324928, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"210fc8563b8ede2fc9b12131ccf5c94b\", \"url\": \"https://api.pillowcase.su/api/download/210fc8563b8ede2fc9b12131ccf5c94b\", \"size\": \"2.5 MB\", \"duration\": 5.77}", "aliases": ["The Mind Is Powerful", "Start Time"], "size": "2.5 MB"}, {"id": "that-s-on-you", "name": "That's On You [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "2014 - 2015 version with more vocals and a Charlie Heat beat. Original snippet leaked April 17th, 2024. Leaked as a bonus for a Soakbuy.", "length": "165.54", "fileDate": 17415648, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/190656f6575c60182b8a2006ce3905db", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/190656f6575c60182b8a2006ce3905db\", \"key\": \"That's On You\", \"title\": \"That's On You [V1]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Come and Go\"], \"description\": \"2014 - 2015 version with more vocals and a Charlie Heat beat. Original snippet leaked April 17th, 2024. Leaked as a bonus for a Soakbuy.\", \"date\": 17415648, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"787f7d5bda3fb6ee59abe0ea616ac1ab\", \"url\": \"https://api.pillowcase.su/api/download/787f7d5bda3fb6ee59abe0ea616ac1ab\", \"size\": \"5.06 MB\", \"duration\": 165.54}", "aliases": ["Come and Go"], "size": "5.06 MB"}, {"id": "ultimate-lie", "name": "Ultimate Lie [V1]", "artists": [], "producers": [], "notes": "OG Filename: KW - Ultimate Lie Ref @ 80.2 BPM\nShorter version with a different vocal take", "length": "77.8", "fileDate": 16514496, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/eeb25ffac7d65285d1e2fd51125a5b0c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/eeb25ffac7d65285d1e2fd51125a5b0c\", \"key\": \"Ultimate Lie\", \"title\": \"Ultimate Lie [V1]\", \"description\": \"OG Filename: KW - Ultimate Lie Ref @ 80.2 BPM\\nShorter version with a different vocal take\", \"date\": 16514496, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"42bc57dfd9341afcac4feb339f60292f\", \"url\": \"https://api.pillowcase.su/api/download/42bc57dfd9341afcac4feb339f60292f\", \"size\": \"3.66 MB\", \"duration\": 77.8}", "aliases": [], "size": "3.66 MB"}, {"id": "ultimate-lie-61", "name": "✨ Ultimate Lie [V2]", "artists": [], "producers": [], "notes": "OG Filename: KW - Ultimate Lie Ref 2 (8.22.14)\nA more finished version with an extra finished line and slight production differences.", "length": "143.58", "fileDate": 16514496, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/c142607be7d57e211d57a4c7cde4ae7a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c142607be7d57e211d57a4c7cde4ae7a\", \"key\": \"Ultimate Lie\", \"title\": \"\\u2728 Ultimate Lie [V2]\", \"description\": \"OG Filename: KW - Ultimate Lie Ref 2 (8.22.14)\\nA more finished version with an extra finished line and slight production differences.\", \"date\": 16514496, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"61429a51c5290fe11a08a9c153c2ac39\", \"url\": \"https://api.pillowcase.su/api/download/61429a51c5290fe11a08a9c153c2ac39\", \"size\": \"4.71 MB\", \"duration\": 143.58}", "aliases": [], "size": "4.71 MB"}, {"id": "ultimate-lie-62", "name": "Ultimate Lie [V3]", "artists": [], "producers": [], "notes": "OG Filename: KW - Ultimate Lie Ref (5.29.15)\nFilename seen from a picture of Post Malone refs.", "length": "215.56", "fileDate": 15633216, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/4a217e5f1b6d90c905ee68d0e1751911", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4a217e5f1b6d90c905ee68d0e1751911\", \"key\": \"Ultimate Lie\", \"title\": \"Ultimate Lie [V3]\", \"description\": \"OG Filename: KW - Ultimate Lie Ref (5.29.15)\\nFilename seen from a picture of Post Malone refs.\", \"date\": 15633216, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"96466088e739349baf7503bcd2dc2224\", \"url\": \"https://api.pillowcase.su/api/download/96466088e739349baf7503bcd2dc2224\", \"size\": \"5.86 MB\", \"duration\": 215.56}", "aliases": [], "size": "5.86 MB"}, {"id": "woke-up", "name": "✨ Ty Dolla $ign - Woke Up [V2]", "artists": ["Kanye West"], "producers": ["<PERSON>"], "notes": "Originally thought to have been a finished SHMG throwaway, but the naming of the original German filename (Ty$ - Woke Up Feat Kanye West) means it was probably given to Ty<PERSON>, similar to \"Guard Down.\" Has a minute of rapping and singing vocals from <PERSON><PERSON><PERSON>, as well as punch-ins and verses from <PERSON> over the initial reference track. Samples \"Paradise\" by <PERSON><PERSON><PERSON>. Mistaken as a Hitler era track, since the producer for \"Paradise\" revealed <PERSON><PERSON><PERSON> had reached out Christmas 2016 for the stems - later using them to make an altered version of the song to use as background for a sentimental home video posted by <PERSON> to twitter. Also thought to be made later because \"Paradise\" wasn't released until December 2015, but it was found that the song was previewed before, with <PERSON><PERSON><PERSON>'s album having already been delayed multiple times since 2013. Original snippet leaked April 17th, 2024. Leaked after a successful Soakbuy,", "length": "180.46", "fileDate": 17381088, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/4e73d1f1eec09e69873eecfc27652d34", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4e73d1f1eec09e69873eecfc27652d34\", \"key\": \"Woke Up\", \"title\": \"\\u2728 Ty Dolla $ign - Woke Up [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"Originally thought to have been a finished SHMG throwaway, but the naming of the original German filename (Ty$ - Woke Up Feat Kanye West) means it was probably given to Ty<PERSON>, similar to \\\"Guard Down.\\\" Has a minute of rapping and singing vocals from <PERSON><PERSON><PERSON>, as well as punch-ins and verses from <PERSON> over the initial reference track. Samples \\\"Paradise\\\" by Je<PERSON><PERSON>. Mistaken as a Hitler era track, since the producer for \\\"Paradise\\\" revealed <PERSON><PERSON><PERSON> had reached out Christmas 2016 for the stems - later using them to make an altered version of the song to use as background for a sentimental home video posted by <PERSON> to twitter. Also thought to be made later because \\\"Paradise\\\" wasn't released until December 2015, but it was found that the song was previewed before, with <PERSON><PERSON><PERSON>'s album having already been delayed multiple times since 2013. Original snippet leaked April 17th, 2024. Leaked after a successful Soakbuy,\", \"date\": 17381088, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cc13df619f5ec2d8e005f51eed83c2c7\", \"url\": \"https://api.pillowcase.su/api/download/cc13df619f5ec2d8e005f51eed83c2c7\", \"size\": \"5.3 MB\", \"duration\": 180.46}", "aliases": [], "size": "5.3 MB"}, {"id": "wolves", "name": "Wolves [V8]", "artists": [], "producers": ["Cashmere Cat", "Sinjin Hawke"], "notes": "Instrumental found in a Cashmere Cat vine. Very little is known about it, but it is known that the beat was made for Kanye. CDQ stem edit snippet using the real instrumental (with fake vocals) leaked October 23rd, 2022.", "length": "", "fileDate": 17005248, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/86dfbcf9531ea34c523f30a4c0f2860d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/86dfbcf9531ea34c523f30a4c0f2860d\", \"key\": \"Wolves\", \"title\": \"Wolves [V8]\", \"artists\": \"(prod. Cashmere Cat & Sinjin Hawke)\", \"aliases\": [\"Lost\"], \"description\": \"Instrumental found in a Cashmere Cat vine. Very little is known about it, but it is known that the beat was made for Kanye. CDQ stem edit snippet using the real instrumental (with fake vocals) leaked October 23rd, 2022.\", \"date\": 17005248, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Lost"], "size": ""}, {"id": "wolves-65", "name": "Wolves [V8]", "artists": [], "producers": ["Cashmere Cat", "Sinjin Hawke"], "notes": "Instrumental found in a Cashmere Cat vine. Very little is known about it, but it is known that the beat was made for Kanye. CDQ stem edit snippet using the real instrumental (with fake vocals) leaked October 23rd, 2022.", "length": "336.12", "fileDate": 17005248, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/ca8246659bd4c5297cbce9b1e703a3a2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ca8246659bd4c5297cbce9b1e703a3a2\", \"key\": \"Wolves\", \"title\": \"Wolves [V8]\", \"artists\": \"(prod. Cashmere Cat & Sinjin Hawke)\", \"aliases\": [\"Lost\"], \"description\": \"Instrumental found in a Cashmere Cat vine. Very little is known about it, but it is known that the beat was made for Kanye. CDQ stem edit snippet using the real instrumental (with fake vocals) leaked October 23rd, 2022.\", \"date\": 17005248, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1dcd977d84ec41affd2e1d5b08d1dbbf\", \"url\": \"https://api.pillowcase.su/api/download/1dcd977d84ec41affd2e1d5b08d1dbbf\", \"size\": \"7.79 MB\", \"duration\": 336.12}", "aliases": ["Lost"], "size": "7.79 MB"}, {"id": "wolves-66", "name": "Wolves [V9]", "artists": [], "producers": ["Cashmere Cat", "Sinjin Hawke"], "notes": "OG Filename: KW - <PERSON> Ref (9.30.14)\nSolo version of \"Wolves\". Leaked as a bonus for the \"Welcome To My Life\" groupbuy. This version was sent to <PERSON><PERSON> in December 2014, alongside \"Go Pro\" and <PERSON><PERSON>'s \"All Day\" reference.", "length": "147.62", "fileDate": 16731360, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/88116c201793c5ea729b5bc6863842fb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/88116c201793c5ea729b5bc6863842fb\", \"key\": \"Wolves\", \"title\": \"Wolves [V9]\", \"artists\": \"(prod. Cashmere Cat & Sinjin Hawke)\", \"aliases\": [\"Lost\"], \"description\": \"OG Filename: KW - Wolves Ref (9.30.14)\\nSolo version of \\\"Wolves\\\". Leaked as a bonus for the \\\"Welcome To My Life\\\" groupbuy. This version was sent to <PERSON><PERSON> in December 2014, alongside \\\"Go Pro\\\" and <PERSON><PERSON>'s \\\"All Day\\\" reference.\", \"date\": 16731360, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fc95a4030021594513dbe35fe8d6cefd\", \"url\": \"https://api.pillowcase.su/api/download/fc95a4030021594513dbe35fe8d6cefd\", \"size\": \"4.77 MB\", \"duration\": 147.62}", "aliases": ["Lost"], "size": "4.77 MB"}, {"id": "wolves-67", "name": "Wolves [V11]", "artists": ["VIC MENSA", "Sia"], "producers": ["Cashmere Cat", "Sinjin Hawke"], "notes": "OG Filename: Wolves Fashion Show Edit 2 (2.12.15)\nFashion Show Edit of \"Wolves\". Very similar to the later version, but contains looped parts and a different instrumental outro.", "length": "827.69", "fileDate": 16720128, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/9c126d375e4debd20acb16032f94caa3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9c126d375e4debd20acb16032f94caa3\", \"key\": \"Wolves\", \"title\": \"Wolves [V11]\", \"artists\": \"(feat. VI<PERSON> MENSA & Sia) (prod. Cashmere Cat & Sinjin Hawke)\", \"aliases\": [\"Lost\"], \"description\": \"OG Filename: Wolves Fashion Show Edit 2 (2.12.15)\\nFashion Show Edit of \\\"Wolves\\\". Very similar to the later version, but contains looped parts and a different instrumental outro.\", \"date\": 16720128, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f20de1c1b45c2cdb1d10eada49714094\", \"url\": \"https://api.pillowcase.su/api/download/f20de1c1b45c2cdb1d10eada49714094\", \"size\": \"15.7 MB\", \"duration\": 827.69}", "aliases": ["Lost"], "size": "15.7 MB"}, {"id": "wolves-68", "name": "Wolves [V12]", "artists": ["VIC MENSA", "Sia"], "producers": ["Cashmere Cat", "Sinjin Hawke"], "notes": "OG Filename: Wolves SNL 40 Edit 1 (2.14.15)\nVersion of the song that was used at SNL 40. Likely the file they used to make the looped YZYSZN1 version but with minor adjustments.", "length": "326.15", "fileDate": 16720128, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/4a5f1e1fc7f5a8c14145a62a1d9461db", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4a5f1e1fc7f5a8c14145a62a1d9461db\", \"key\": \"Wolves\", \"title\": \"Wolves [V12]\", \"artists\": \"(feat. <PERSON><PERSON> MENSA & Sia) (prod. Cashmere Cat & Sinjin Hawke)\", \"aliases\": [\"Lost\"], \"description\": \"OG Filename: Wolves SNL 40 Edit 1 (2.14.15)\\nVersion of the song that was used at SNL 40. Likely the file they used to make the looped YZYSZN1 version but with minor adjustments.\", \"date\": 16720128, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5182fa8dd023d37b1945b8ef6e6f8e13\", \"url\": \"https://api.pillowcase.su/api/download/5182fa8dd023d37b1945b8ef6e6f8e13\", \"size\": \"7.63 MB\", \"duration\": 326.15}", "aliases": ["Lost"], "size": "7.63 MB"}, {"id": "wolves-69", "name": "Wolves [V13]", "artists": ["VIC MENSA", "Sia", "<PERSON>"], "producers": ["Cashmere Cat", "Sinjin Hawke"], "notes": "OG Filename: Wolves Ref (2.24.15) CASHMERE CAT\nRelease ready version of the song. <PERSON><PERSON> has one less line than on previous versions. Meant for the now scrapped A Side / B Side single release of \"All Day\" & \"Wolves\", which was meant to release on February 24th, 2015 based on his iTunes Artist Page updating, <PERSON>'s single art, <PERSON><PERSON> <PERSON>AN hyping it up on Twitter and <PERSON><PERSON> being confused by it's delay on Twitter. Was delayed by a week for unknown reasons and the next release day, (March 3rd, 2015) \"All Day\" released on it's own without \"Wolves\". <PERSON> was purchased from MUSICMAFIA by a Section80 user and eventually leaked in the Feb 2016 leak season.", "length": "327.48", "fileDate": 14557536, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/1bf1f37535ea1c4620315842dc3c05d3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1bf1f37535ea1c4620315842dc3c05d3\", \"key\": \"Wolves\", \"title\": \"Wolves [V13]\", \"artists\": \"(feat. <PERSON><PERSON>, <PERSON><PERSON> & <PERSON>) (prod. Cashmere Cat & Sinjin Hawke)\", \"aliases\": [\"Lost\"], \"description\": \"OG Filename: Wolves Ref (2.24.15) CASHMERE CAT\\nRelease ready version of the song. <PERSON><PERSON> has one less line than on previous versions. Meant for the now scrapped A Side / B Side single release of \\\"All Day\\\" & \\\"Wolves\\\", which was meant to release on February 24th, 2015 based on his iTunes Artist Page updating, <PERSON>'s single art, <PERSON><PERSON> DEAN hyping it up on Twitter and <PERSON><PERSON> being confused by it's delay on Twitter. Was delayed by a week for unknown reasons and the next release day, (March 3rd, 2015) \\\"All Day\\\" released on it's own without \\\"Wolves\\\". Song was purchased from MUSICMAFIA by a Section80 user and eventually leaked in the Feb 2016 leak season.\", \"date\": 14557536, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"acba69a27851b0ddadde932390f8a9ba\", \"url\": \"https://api.pillowcase.su/api/download/acba69a27851b0ddadde932390f8a9ba\", \"size\": \"7.65 MB\", \"duration\": 327.48}", "aliases": ["Lost"], "size": "7.65 MB"}, {"id": "wolves-70", "name": "Wolves [V14]", "artists": ["VIC MENSA", "Sia"], "producers": ["Cashmere Cat", "Sinjin Hawke"], "notes": "Version with production differences, including added horns. Played on April 2nd by Cashmere Cat at a concert.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/0093bf2d8868349f8feee4029bbcfcf6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0093bf2d8868349f8feee4029bbcfcf6\", \"key\": \"Wolves\", \"title\": \"Wolves [V14]\", \"artists\": \"(feat. VIC MENSA & Sia) (prod. Cashmere Cat & Sinjin Hawke)\", \"aliases\": [\"Lost\"], \"description\": \"Version with production differences, including added horns. Played on April 2nd by Cashmere Cat at a concert.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["Lost"], "size": ""}, {"id": "wolves-71", "name": "Wolves [V14]", "artists": ["VIC MENSA", "Sia"], "producers": ["Cashmere Cat", "Sinjin Hawke"], "notes": "Version with production differences, including added horns. Played on April 2nd by Cashmere Cat at a concert.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/f39dab43e909cb28707e91623167ed7e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f39dab43e909cb28707e91623167ed7e\", \"key\": \"Wolves\", \"title\": \"Wolves [V14]\", \"artists\": \"(feat. <PERSON><PERSON> MENSA & Sia) (prod. Cashmere Cat & Sinjin Hawke)\", \"aliases\": [\"Lost\"], \"description\": \"Version with production differences, including added horns. Played on April 2nd by Cashmere Cat at a concert.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["Lost"], "size": ""}, {"id": "wolves-72", "name": "Wolves [V16]", "artists": ["VIC MENSA", "Sia", "<PERSON>"], "producers": ["Cashmere Cat", "Sinjin Hawke"], "notes": "OG Filename: KW - Wolves Glasto 6.24.15\nVersion used for Glastonbury 2015. Contains an early version of <PERSON><PERSON><PERSON>'s verse, with some alternate lines that would later be re-recorded for the final version.", "length": "323.08", "fileDate": 16711488, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/50ed13005fab65b79dae89cca8111117", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/50ed13005fab65b79dae89cca8111117\", \"key\": \"Wolves\", \"title\": \"Wolves [V16]\", \"artists\": \"(feat. <PERSON><PERSON>, <PERSON><PERSON> & <PERSON>) (prod. Cashmere Cat & Sinjin Hawke)\", \"aliases\": [\"Lost\"], \"description\": \"OG Filename: KW - Wolves Glasto 6.24.15\\nVersion used for Glastonbury 2015. Contains an early version of <PERSON><PERSON><PERSON>'s verse, with some alternate lines that would later be re-recorded for the final version.\", \"date\": 16711488, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1d7da5c2742a5e9e971d8e1e7b792296\", \"url\": \"https://api.pillowcase.su/api/download/1d7da5c2742a5e9e971d8e1e7b792296\", \"size\": \"5.38 MB\", \"duration\": 323.08}", "aliases": ["Lost"], "size": "5.38 MB"}, {"id": "-73", "name": "???", "artists": [], "producers": [], "notes": "Listed as being from 2015 sessions. Nothing else is known.", "length": "11.21", "fileDate": 17191008, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/5813b0f15831285157c7a86513bfed06", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5813b0f15831285157c7a86513bfed06\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Freestyle Idea #1\"], \"description\": \"Listed as being from 2015 sessions. Nothing else is known.\", \"date\": 17191008, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0574a9af8a454e3f2b09b90b822ee381\", \"url\": \"https://api.pillowcase.su/api/download/0574a9af8a454e3f2b09b90b822ee381\", \"size\": \"2.59 MB\", \"duration\": 11.21}", "aliases": ["Freestyle Idea #1"], "size": "2.59 MB"}, {"id": "-74", "name": "🏆 ???", "artists": [], "producers": ["<PERSON>"], "notes": "Snippet shown on a documentary about Charlie Heat (snippet at 2:11). Nothing is known apart from the fact that it was made sometime in 2015 and has Charlie Heat production.", "length": "", "fileDate": 15682464, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/b28d7e09b05b55bbdc0c411dc3b4cd92", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b28d7e09b05b55bbdc0c411dc3b4cd92\", \"key\": \"???\", \"title\": \"\\ud83c\\udfc6 ???\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"You Know It\"], \"description\": \"Snippet shown on a documentary about Charlie Heat (snippet at 2:11). Nothing is known apart from the fact that it was made sometime in 2015 and has Charlie Heat production.\", \"date\": 15682464, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["You Know It"], "size": ""}, {"id": "-75", "name": "???", "artists": [], "producers": ["Velous"], "notes": "In a 2014 interview, French Montana said that producer <PERSON><PERSON><PERSON> played him \"like 1,000 songs\" in the process of working on his album Mac & Cheese 4, and that <PERSON><PERSON><PERSON> - who was helping French with the album - \"picked like two beats\" to work on. <PERSON> <PERSON> is an idiot because we know that <PERSON><PERSON><PERSON> produced \"All Day\", \"Highlights\", and \"Never Tried This\", which is more than 2 songs. Snippet for another <PERSON><PERSON>us produced freestyle leaked February 10th, 2025.", "length": "11.08", "fileDate": 17391456, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/483efdf523f28e668f3c38e3137ddfc7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/483efdf523f28e668f3c38e3137ddfc7\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. Velous)\", \"aliases\": [\"I Know When I Know It\"], \"description\": \"In a 2014 interview, French Montana said that producer <PERSON><PERSON><PERSON> played him \\\"like 1,000 songs\\\" in the process of working on his album Mac & Cheese 4, and that <PERSON><PERSON><PERSON> - who was helping French with the album - \\\"picked like two beats\\\" to work on. French Montana is an idiot because we know that <PERSON><PERSON><PERSON> produced \\\"All Day\\\", \\\"Highlights\\\", and \\\"Never Tried This\\\", which is more than 2 songs. Snippet for another Velous produced freestyle leaked February 10th, 2025.\", \"date\": 17391456, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"06aa73a169b53b74c17b617cb4bc3aa0\", \"url\": \"https://api.pillowcase.su/api/download/06aa73a169b53b74c17b617cb4bc3aa0\", \"size\": \"2.59 MB\", \"duration\": 11.08}", "aliases": ["I Know When I Know It"], "size": "2.59 MB"}, {"id": "jukebox-joints", "name": "A$AP Rocky - Jukebox Joints [V2]", "artists": ["Kanye West"], "producers": ["Kanye West", "<PERSON><PERSON> <PERSON>"], "notes": "Earlier version of \"Jukebox Joints\" with 4 extra lines from <PERSON><PERSON><PERSON>. Other differences are currently unknown. Was though to be solo Ye until seller said otherwise. Snippet leaked February 7th, 2024.", "length": "5.62", "fileDate": 17072640, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/b41536df40434d380a5aa5221bf93b30", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b41536df40434d380a5aa5221bf93b30\", \"key\": \"Jukebox Joints\", \"title\": \"A$AP Rocky - Jukebox Joints [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & Che Pope)\", \"description\": \"Earlier version of \\\"Jukebox Joints\\\" with 4 extra lines from <PERSON><PERSON><PERSON>. Other differences are currently unknown. Was though to be solo Ye until seller said otherwise. Snippet leaked February 7th, 2024.\", \"date\": 17072640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c2549875e6f3d1b1b228f6e85b92f6de\", \"url\": \"https://api.pillowcase.su/api/download/c2549875e6f3d1b1b228f6e85b92f6de\", \"size\": \"2.5 MB\", \"duration\": 5.62}", "aliases": [], "size": "2.5 MB"}, {"id": "ass-shots", "name": "French Montana - Ass Shots [V3]", "artists": ["Kanye West", "<PERSON>'ron"], "producers": [], "notes": "OG Filename: Ass Shots _ BBRef1\nMore finished version. Edited file leaked sometime in Dec 2016, with an unedted but still not OG file leaking later in 2025.", "length": "247.33", "fileDate": 17401824, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/6e7c5f5981776004a7569bfcc17752a3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6e7c5f5981776004a7569bfcc17752a3\", \"key\": \"Ass Shots\", \"title\": \"French Montana - Ass Shots [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & Cam<PERSON>ron)\", \"aliases\": [\"Ass Shot\", \"Bang\"], \"description\": \"OG Filename: Ass Shots _ BBRef1\\nMore finished version. Edited file leaked sometime in Dec 2016, with an unedted but still not OG file leaking later in 2025.\", \"date\": 17401824, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f000f70e25489552169a3b96d1468184\", \"url\": \"https://api.pillowcase.su/api/download/f000f70e25489552169a3b96d1468184\", \"size\": \"6.37 MB\", \"duration\": 247.33}", "aliases": ["<PERSON><PERSON> Shot", "<PERSON>"], "size": "6.37 MB"}, {"id": "never-enough", "name": "<PERSON> - Never Enough", "artists": [], "producers": [], "notes": "OG Filename: Never Enough Beat Sketch\nSong found in a folder featuring collaborations from <PERSON>, <PERSON> and <PERSON><PERSON><PERSON>. Unknown if this is a reference track or a one-off John <PERSON> song, but placed here as it may be involved with <PERSON><PERSON><PERSON>.", "length": "165.88", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/6e582c6935074149657fbfc34b19746f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6e582c6935074149657fbfc34b19746f\", \"key\": \"Never Enough\", \"title\": \"<PERSON> Legend - Never Enough\", \"description\": \"OG Filename: Never Enough Beat Sketch\\nSong found in a folder featuring collaborations from <PERSON>, <PERSON> and <PERSON><PERSON><PERSON>. Unknown if this is a reference track or a one-off John Legend song, but placed here as it may be involved with <PERSON><PERSON><PERSON>.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"19f7294764316933a4bcd50c3f2f04b7\", \"url\": \"https://api.pillowcase.su/api/download/19f7294764316933a4bcd50c3f2f04b7\", \"size\": \"5.06 MB\", \"duration\": 165.88}", "aliases": [], "size": "5.06 MB"}, {"id": "come-on", "name": "KIRBY - Come On", "artists": [], "producers": [], "notes": "KIRBY song later sampled at the start of \"Pick Up Your Speed\" (pitched down). Likely titled \"Come On,\" based on stems in the session. Contains a different beat. Attached links contain a snippet of the beat as well as the acapella for the song.", "length": "16.08", "fileDate": 17199648, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/cc429837a2dff9d1169ab26e1025b11e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cc429837a2dff9d1169ab26e1025b11e\", \"key\": \"Come On\", \"title\": \"KIRBY - Come On\", \"aliases\": [\"Accelerate\", \"Auxillary\", \"Pick Up Your Speed\"], \"description\": \"KIRBY song later sampled at the start of \\\"Pick Up Your Speed\\\" (pitched down). Likely titled \\\"Come On,\\\" based on stems in the session. Contains a different beat. Attached links contain a snippet of the beat as well as the acapella for the song.\", \"date\": 17199648, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4bc93b561436660a8d7a98bc00012605\", \"url\": \"https://api.pillowcase.su/api/download/4bc93b561436660a8d7a98bc00012605\", \"size\": \"2.67 MB\", \"duration\": 16.08}", "aliases": ["Accelerate", "Auxillary", "Pick Up Your Speed"], "size": "2.67 MB"}, {"id": "come-on-80", "name": "KIRBY - Come On", "artists": [], "producers": [], "notes": "KIRBY song later sampled at the start of \"Pick Up Your Speed\" (pitched down). Likely titled \"Come On,\" based on stems in the session. Contains a different beat. Attached links contain a snippet of the beat as well as the acapella for the song.", "length": "116.56", "fileDate": 17199648, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/c361499647034429b9c9aca0bfa3b293", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c361499647034429b9c9aca0bfa3b293\", \"key\": \"Come On\", \"title\": \"KIRBY - Come On\", \"aliases\": [\"Accelerate\", \"Auxillary\", \"Pick Up Your Speed\"], \"description\": \"KIRBY song later sampled at the start of \\\"Pick Up Your Speed\\\" (pitched down). Likely titled \\\"Come On,\\\" based on stems in the session. Contains a different beat. Attached links contain a snippet of the beat as well as the acapella for the song.\", \"date\": 17199648, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3a20dcba95ae70378759c37b5c721ac3\", \"url\": \"https://api.pillowcase.su/api/download/3a20dcba95ae70378759c37b5c721ac3\", \"size\": \"4.28 MB\", \"duration\": 116.56}", "aliases": ["Accelerate", "Auxillary", "Pick Up Your Speed"], "size": "4.28 MB"}, {"id": "illuminati", "name": "<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "artists": [], "producers": ["Kanye West", "MIKE DEAN", "<PERSON>", "<PERSON>"], "notes": "Early version with slightly different instrumental, and is shorter in length. <PERSON> said she wrote the track in \"March or April\" of 2014.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://www.youtube.com/watch?v=9wieqHYi4HI", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.youtube.com/watch?v=9wieqHYi4HI\", \"key\": \"<PERSON><PERSON><PERSON><PERSON>\", \"title\": \"<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> & <PERSON>)\", \"description\": \"Early version with slightly different instrumental, and is shorter in length. <PERSON> said she wrote the track in \\\"March or April\\\" of 2014.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}, {"id": "s-e-x", "name": "Madonna - S.E.X", "artists": [], "producers": ["Kanye West"], "notes": "Demo version of \"S.E.X\". Included on the deluxe edition of her album Rebel Heart.", "length": "202.45", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/320f0a970a1ec914c93ed0cb6ed440dc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/320f0a970a1ec914c93ed0cb6ed440dc\", \"key\": \"S.E.X\", \"title\": \"Madonna - S.E.X\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Demo version of \\\"S.E.X\\\". Included on the deluxe edition of her album Rebel Heart.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d5c9211278922ec4c05ed4f60bbbf90e\", \"url\": \"https://api.pillowcase.su/api/download/d5c9211278922ec4c05ed4f60bbbf90e\", \"size\": \"5.65 MB\", \"duration\": 202.45}", "aliases": [], "size": "5.65 MB"}, {"id": "santeria", "name": "Pusha T - Santeria [V1]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Santeria Spanish PushaT Westlake D\nOriginal version intended for <PERSON> Push. Has an incredibly bare bones instrumental, using basically nothing more than the sample. Samples \"É preciso dar um jeito meu amigo\" by <PERSON><PERSON>.", "length": "221.18", "fileDate": 15936480, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/e6e8af6918678e5541017a5cf3f88e16", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e6e8af6918678e5541017a5cf3f88e16\", \"key\": \"Santeria\", \"title\": \"Pusha T - Santeria [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Santeria Spanish PushaT Westlake D\\nOriginal version intended for King Push. Has an incredibly bare bones instrumental, using basically nothing more than the sample. Samples \\\"\\u00c9 preciso dar um jeito meu amigo\\\" by <PERSON><PERSON>\", \"date\": 15936480, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dc17b6d6bc34bc94373bc31290f6adad\", \"url\": \"https://api.pillowcase.su/api/download/dc17b6d6bc34bc94373bc31290f6adad\", \"size\": \"4.42 MB\", \"duration\": 221.18}", "aliases": [], "size": "4.42 MB"}, {"id": "bitch-better-have-my-money", "name": "<PERSON><PERSON><PERSON> - Bitch Better Have My Money [V2]", "artists": [], "producers": ["Kanye West", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Deputy"], "notes": "Version with additional production from <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>Gur<PERSON>. Instrumental was likely similar to release. Likely from late November 2015.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://web.archive.org/web/20211006111223/http://www.mtv.com/news/2137551/rihanna-bitch-better-have-my-money-producer-deputy/?xrs=_s.fb_news_c.mu_h.21", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://web.archive.org/web/20211006111223/http://www.mtv.com/news/2137551/rihanna-bitch-better-have-my-money-producer-deputy/?xrs=_s.fb_news_c.mu_h.21\", \"key\": \"Bitch Better Have My Money\", \"title\": \"Rihanna - Bitch Better Have My Money [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> & Deputy)\", \"description\": \"Version with additional production from <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Instrumental was likely similar to release. Likely from late November 2015.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}, {"id": "bitch-better-have-my-money-85", "name": "<PERSON><PERSON><PERSON> - Bitch Better Have My Money [V2]", "artists": [], "producers": ["Kanye West", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Deputy"], "notes": "Version with additional production from <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>Gur<PERSON>. Instrumental was likely similar to release. Likely from late November 2015.", "length": "215.22", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/a6ac452abffd278a477f8a286527291d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a6ac452abffd278a477f8a286527291d\", \"key\": \"Bitch Better Have My Money\", \"title\": \"Rihanna - Bitch Better Have My Money [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> & Deputy)\", \"description\": \"Version with additional production from <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Instrumental was likely similar to release. Likely from late November 2015.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"91e1227990e393c772e55ceb785c8b06\", \"url\": \"https://api.pillowcase.su/api/download/91e1227990e393c772e55ceb785c8b06\", \"size\": \"5.85 MB\", \"duration\": 215.22}", "aliases": [], "size": "5.85 MB"}, {"id": "heaven", "name": "<PERSON><PERSON><PERSON> - Heaven ", "artists": [], "producers": [], "notes": "2015 version of \"Heaven\" with vocals from <PERSON><PERSON><PERSON>. According to the same person who shared the snippet, this was a ref for <PERSON><PERSON><PERSON>.", "length": "16.54", "fileDate": 17232480, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/465442e0b4935919b247420f8ab82825", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/465442e0b4935919b247420f8ab82825\", \"key\": \"Heaven\", \"title\": \"Rihanna - Heaven \", \"artists\": \"(ref. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> & ???)\", \"description\": \"2015 version of \\\"Heaven\\\" with vocals from <PERSON><PERSON><PERSON>. According to the same person who shared the snippet, this was a ref for <PERSON><PERSON><PERSON>.\", \"date\": 17232480, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5122642bc9f449f640356da8d93d63a4\", \"url\": \"https://api.pillowcase.su/api/download/5122642bc9f449f640356da8d93d63a4\", \"size\": \"2.67 MB\", \"duration\": 16.54}", "aliases": [], "size": "2.67 MB"}, {"id": "blooded", "name": "<PERSON><PERSON> - Blooded [V4]", "artists": [], "producers": [], "notes": "OG Filename: Blooded 041015 pre mix 4\nVersion of \"Cold Blooded\" from 2015. Has different vocals and instrumental. Later revisited in the Wyoming sessions for K.T.S.E..", "length": "192.05", "fileDate": 17180640, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/a2c8a51e424d6fc735517408452c020c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a2c8a51e424d6fc735517408452c020c\", \"key\": \"Blooded\", \"title\": \"<PERSON><PERSON> - Blooded [V4]\", \"aliases\": [\"Cold Blooded\"], \"description\": \"OG Filename: Blooded 041015 pre mix 4\\nVersion of \\\"Cold Blooded\\\" from 2015. Has different vocals and instrumental. Later revisited in the Wyoming sessions for K.T.S.E..\", \"date\": 17180640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1f66e0f49b869af3a8b47b6731578a26\", \"url\": \"https://api.pillowcase.su/api/download/1f66e0f49b869af3a8b47b6731578a26\", \"size\": \"5.48 MB\", \"duration\": 192.05}", "aliases": ["Cold Blooded"], "size": "5.48 MB"}, {"id": "smuckers", "name": "<PERSON>, The Creator - SMUCKERS [V1]", "artists": [], "producers": ["<PERSON>", "The Creator"], "notes": "Solo version, said to exist by <PERSON><PERSON> <PERSON> played in the CHERRY BOMB documentary.", "length": "", "fileDate": 14961024, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://www.billboard.com/music/rb-hip-hop/tyler-creator-kend<PERSON>-jenner-tidal-interview-6545665/", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.billboard.com/music/rb-hip-hop/tyler-creator-k<PERSON><PERSON>-jenne<PERSON>-tidal-interview-6545665/\", \"key\": \"SMUCKERS\", \"title\": \"<PERSON>, The Creator - SMUCKERS [V1]\", \"artists\": \"(prod. <PERSON>, The Creator)\", \"description\": \"Solo version, said to exist by <PERSON><PERSON> <PERSON> played in the CHERRY BOMB documentary.\", \"date\": 14961024, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "smuckers-89", "name": "<PERSON>, The Creator - SMUCKERS [V1]", "artists": [], "producers": ["<PERSON>", "The Creator"], "notes": "Solo version, said to exist by <PERSON><PERSON> <PERSON> played in the CHERRY BOMB documentary.", "length": "", "fileDate": 14961024, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://youtu.be/adqH75zeiKk?t=2190", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://youtu.be/adqH75zeiKk?t=2190\", \"key\": \"SMUCKERS\", \"title\": \"<PERSON>, The Creator - SMUCKERS [V1]\", \"artists\": \"(prod. <PERSON>, The Creator)\", \"description\": \"Solo version, said to exist by <PERSON>. <PERSON> played in the CHERRY BOMB documentary.\", \"date\": 14961024, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "smuckers-90", "name": "<PERSON>, The Creator - SMUCKERS [V2]", "artists": ["Kanye West"], "producers": ["<PERSON>", "The Creator"], "notes": "Early version of \"SMUCKERS\", described as <PERSON><PERSON><PERSON> \"speaking pure nonsense\" with \"insane and ridiculous lyrics, making it unreleasable\", so likely mumble, alongside alternate <PERSON> lyrics. <PERSON>'s verse is also rumored to be <PERSON>'s verse.", "length": "", "fileDate": 14961024, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://youtu.be/adqH75zeiKk?t=2190", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://youtu.be/adqH75zeiKk?t=2190\", \"key\": \"SMUCKERS\", \"title\": \"<PERSON>, The Creator - SMUCKERS [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>, The Creator)\", \"description\": \"Early version of \\\"SMUCKERS\\\", described as <PERSON><PERSON><PERSON> \\\"speaking pure nonsense\\\" with \\\"insane and ridiculous lyrics, making it unreleasable\\\", so likely mumble, alongside alternate <PERSON> lyrics. <PERSON>'s verse is also rumored to be <PERSON>'s verse.\", \"date\": 14961024, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "smuckers-91", "name": "<PERSON>, The Creator - SMUCKERS [V5]", "artists": ["Kanye West", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "The Creator"], "notes": "Version played at Camp Flog Gnaw in 2015. Has an alternate intro to the released version.", "length": "49.32", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/0cb43487699506b447a857a5438108e3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0cb43487699506b447a857a5438108e3\", \"key\": \"SMUCKERS\", \"title\": \"<PERSON>, The Creator - SMUCKERS [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>) (prod. <PERSON>, <PERSON> Creator)\", \"description\": \"Version played at Camp Flog Gnaw in 2015. Has an alternate intro to the released version.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"45e5372b418af40c58d7ce07a43414ab\", \"url\": \"https://api.pillowcase.su/api/download/45e5372b418af40c58d7ce07a43414ab\", \"size\": \"3.2 MB\", \"duration\": 49.32}", "aliases": [], "size": "3.2 MB"}, {"id": "u-mad", "name": "VIC MENSA - U Mad [V2]", "artists": ["Kanye West"], "producers": ["<PERSON>"], "notes": "A demo version. Has a single line difference from <PERSON><PERSON><PERSON>, and lacks <PERSON>'s second verse. Unsure when this originally leaked but was resurfaced on leakthis on May 30th, 2020. This seems to be the original file that was used for a Soundcloud upload.", "length": "214.26", "fileDate": 15907968, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "so-help-me-god", "originalUrl": "https://pillowcase.su/f/0d3b59e4a49535a1602c658f5760233d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0d3b59e4a49535a1602c658f5760233d\", \"key\": \"U Mad\", \"title\": \"VIC MENSA - U Mad [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"A demo version. Has a single line difference from <PERSON><PERSON><PERSON>, and lacks <PERSON>'s second verse. Unsure when this originally leaked but was resurfaced on leakthis on May 30th, 2020. This seems to be the original file that was used for a Soundcloud upload.\", \"date\": 15907968, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d00d8d9b2bde56d86a78007838477f01\", \"url\": \"https://api.pillowcase.su/api/download/d00d8d9b2bde56d86a78007838477f01\", \"size\": \"5.84 MB\", \"duration\": 214.26}", "aliases": [], "size": "5.84 MB"}]}