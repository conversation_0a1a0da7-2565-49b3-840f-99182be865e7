{"id": "ca-h-carti-season", "name": "Ca$h Carti Season", "description": "<PERSON><PERSON> changed his style in early 2016 after meeting many famous rappers. This was his third era, which he referred to as Ca$h <PERSON><PERSON>, and it was the largest embodiment of this era. <PERSON><PERSON> was at the peak of his flexing game and his career was moving at its fastest pace. This era concluded with him signing to AWGE and reworking the mixtape once again, which later released in 2017.", "backgroundColor": "rgb(39, 16, 12)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17FtvuTnRpD7Cr3THhymosFoKp70kJPmouUfO9WXeM-pWcT4UyQsLqdgZiGE1eJ-eviLun-FctP73o_irgu-r7oQg1j2W16o55mNirOoiO3xjfv-DMePpAZtPqFNeag?key=QrLRwW_6ASWi9mCdhUkgTQ", "tracks": [{"id": "boss-up", "name": "Boss Up [V1]", "artists": [], "producers": ["MexikoDro"], "notes": "OG Filename: <PERSON><PERSON> Up\nOG version of 'Had 2'. Has a different second verse and mixing.", "length": "2:44", "fileDate": 14920416, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/7a1f30f42f0bad1348b45f6a5bf6ed6b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/7a1f30f42f0bad1348b45f6a5bf6ed6b/play\", \"key\": \"Boss Up\", \"title\": \"Boss Up [V1]\", \"artists\": \"(prod. Me<PERSON><PERSON><PERSON>)\", \"aliases\": [\"Had 2\"], \"description\": \"OG Filename: Carti <PERSON>\\nOG version of 'Had 2'. Has a different second verse and mixing.\", \"date\": 14920416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d9c009313d7ad95c3c5f8efc83a47c95\", \"url\": \"https://api.pillowcase.su/api/download/d9c009313d7ad95c3c5f8efc83a47c95\", \"size\": \"4.32 MB\", \"duration\": 164.33}", "aliases": ["Had 2"], "size": "4.32 MB"}, {"id": "butterfly-coupe", "name": "Butterfly Coupe", "artists": [], "producers": ["MilanMakesBeats"], "notes": "OG Filename: Butterfly Coupe (Carti)\nOG version without <PERSON><PERSON>.", "length": "1:14", "fileDate": 16517088, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/8034525926a5461978209a782b4b2547/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/8034525926a5461978209a782b4b2547/play\", \"key\": \"Butterfly Coupe\", \"title\": \"Butterfly Coupe\", \"artists\": \"(prod. MilanMakesBeats)\", \"description\": \"OG Filename: Butterfly Coupe (Carti)\\nOG version without <PERSON><PERSON>.\", \"date\": 16517088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d93137957b6604105ec4a4b1fee0779d\", \"url\": \"https://api.pillowcase.su/api/download/d93137957b6604105ec4a4b1fee0779d\", \"size\": \"2.88 MB\", \"duration\": 74.16}", "aliases": [], "size": "2.88 MB"}, {"id": "dats-my-dawg", "name": "Dats My Dawg [V1]", "artists": [], "producers": ["MexikoDro", "StoopidXool"], "notes": "OG Filename: <PERSON><PERSON> My Dawg [8-18-2016]\nDated August 18th, 2016. A majority of the song is open verse. Uses the beat to would be used for \"On Top\".", "length": "3:17", "fileDate": 16728768, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/b771360bdeb4575e8284c7452e98b407/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/b771360bdeb4575e8284c7452e98b407/play\", \"key\": \"Dats My Dawg\", \"title\": \"Dats My Dawg [V1]\", \"artists\": \"(prod. MexikoDro & StoopidXool)\", \"aliases\": [\"On Top\"], \"description\": \"OG Filename: Dats My Dawg [8-18-2016]\\nDated August 18th, 2016. A majority of the song is open verse. Uses the beat to would be used for \\\"On Top\\\".\", \"date\": 16728768, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e572214edbb92f85ee80a9a0c1281d1a\", \"url\": \"https://api.pillowcase.su/api/download/e572214edbb92f85ee80a9a0c1281d1a\", \"size\": \"4.85 MB\", \"duration\": 197.18}", "aliases": ["On Top"], "size": "4.85 MB"}, {"id": "finesse-remix", "name": "⭐ Finesse Remix", "artists": [], "producers": ["Chinatown", "​captaincrunch"], "notes": "OG Filename: <PERSON><PERSON> Finesse Remix rough mix-1\nChinatown released the beat on SoundCloud as \"Aston Martin\". Leaked on May 26, 2022.", "length": "3:01", "fileDate": 16535232, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/99667cb3b6026e6d72ef8354ce34eb90/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/99667cb3b6026e6d72ef8354ce34eb90/play\", \"key\": \"Finesse Remix\", \"title\": \"\\u2b50 Finesse Remix\", \"artists\": \"(prod. Chinatown & \\u200bcaptaincrunch)\", \"aliases\": [\"Fell In Love\", \"Aston Martin\"], \"description\": \"OG Filename: <PERSON><PERSON>sse Remix rough mix-1\\nChinatown released the beat on SoundCloud as \\\"Aston Martin\\\". Leaked on May 26, 2022.\", \"date\": 16535232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6131be881aea550be7231d288b454e6a\", \"url\": \"https://api.pillowcase.su/api/download/6131be881aea550be7231d288b454e6a\", \"size\": \"4.59 MB\", \"duration\": 181}", "aliases": ["Fell In Love", "Aston Martin"], "size": "4.59 MB"}, {"id": "frat-rules", "name": "A$AP Mob - Frat Rules", "artists": [], "producers": ["Southside", "Hit-Boy"], "notes": "OG Filename: Carti - Frat Rules AAP Mob Ref\nOriginal version of \"Frat Rules\" where <PERSON><PERSON> does a solo reference track.", "length": "1:43", "fileDate": 16491168, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/f3285149b67bdba451461d2769d6d13d/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/f3285149b67bdba451461d2769d6d13d/play\", \"key\": \"Frat Rules\", \"title\": \"A$AP Mob - Frat Rules\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. Southside & Hit-Boy)\", \"description\": \"OG Filename: Carti - Frat Rules AAP Mob Ref\\nOriginal version of \\\"Frat Rules\\\" where <PERSON><PERSON> does a solo reference track.\", \"date\": 16491168, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c2de60b810346fc3b6091d2741d09e31\", \"url\": \"https://api.pillowcase.su/api/download/c2de60b810346fc3b6091d2741d09e31\", \"size\": \"3.36 MB\", \"duration\": 103.99}", "aliases": [], "size": "3.36 MB"}, {"id": "fuck-that-ho", "name": "Fuck That Ho", "artists": [], "producers": ["BeatPluggz", "MexikoDro"], "notes": "OG Filename: <PERSON><PERSON> version of LAMB$'s \"Another Day\". The song by itself is untitled.", "length": "3:20", "fileDate": 16728768, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/7bb31bb26ad6df2d83249b4b0ddaecde/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/7bb31bb26ad6df2d83249b4b0ddaecde/play\", \"key\": \"Fuck That Ho\", \"title\": \"Fuck That Ho\", \"artists\": \"(prod. BeatPluggz & MexikoDro)\", \"aliases\": [\"Another Day\"], \"description\": \"OG Filename: <PERSON><PERSON>\\nSolo version of LAMB$'s \\\"Another Day\\\". The song by itself is untitled.\", \"date\": 16728768, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e06d61f417c3873389052c7cbd34a7ec\", \"url\": \"https://api.pillowcase.su/api/download/e06d61f417c3873389052c7cbd34a7ec\", \"size\": \"4.9 MB\", \"duration\": 200.18}", "aliases": ["Another Day"], "size": "4.9 MB"}, {"id": "on-top", "name": "⭐ On Top [V2]", "artists": [], "producers": ["MexikoDro", "StoopidXool"], "notes": "OG Filename: playboi carti - #1\nOriginal solo version. Has an open verse at the end.", "length": "3:53", "fileDate": 15372288, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/72738969cbc356a6aeef83531f5e1f76/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/72738969cbc356a6aeef83531f5e1f76/play\", \"key\": \"On Top\", \"title\": \"\\u2b50 On Top [V2]\", \"artists\": \"(prod. MexikoDro & StoopidXool)\", \"aliases\": [\"Dats My Dawg\"], \"description\": \"OG Filename: playboi carti - #1\\nOriginal solo version. Has an open verse at the end.\", \"date\": 15372288, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"08bd4ec16d536bf332d1ee7c0e26f2bd\", \"url\": \"https://api.pillowcase.su/api/download/08bd4ec16d536bf332d1ee7c0e26f2bd\", \"size\": \"5.42 MB\", \"duration\": 233.17}", "aliases": ["Dats My Dawg"], "size": "5.42 MB"}, {"id": "on-the-block", "name": "⭐ On The Block*", "artists": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "producers": ["Chief <PERSON><PERSON>", "DP Beats"], "notes": "OG Filename: <PERSON><PERSON> carti\nOG File Metadata: fredo gleesh carti (prod. sosa\nLeaked by countingcaskets along with <PERSON><PERSON> and the untitled MexikoDro track. Was likely recorded on Jan 4, 2016 as revealed in a tweet by @BigE_Records.", "length": "1:40", "fileDate": 16728768, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/41b6303ef61c5a553bfcb53a48a80dfc/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/41b6303ef61c5a553bfcb53a48a80dfc/play\", \"key\": \"On The Block*\", \"title\": \"\\u2b50 On The Block*\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON><PERSON>) (prod. Chief <PERSON><PERSON> & <PERSON>P <PERSON>)\", \"aliases\": [\"Choppa On Me\"], \"description\": \"OG Filename: <PERSON><PERSON> carti\\nOG File Metadata: fredo gleesh carti (prod. sosa\\nLeaked by countingcaskets along with <PERSON><PERSON> and the untitled MexikoDro track. Was likely recorded on Jan 4, 2016 as revealed in a tweet by @BigE_Records.\", \"date\": 16728768, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dc91114ca933430c730c4146956b8f6b\", \"url\": \"https://api.pillowcase.su/api/download/dc91114ca933430c730c4146956b8f6b\", \"size\": \"3.3 MB\", \"duration\": 100.27}", "aliases": ["Choppa On Me"], "size": "3.3 MB"}, {"id": "ova-here", "name": "<PERSON><PERSON>", "artists": [], "producers": ["Polo Boy <PERSON>"], "notes": "OG Filename: PlayBoiCarti- Ova Here\nSolo version.", "length": "2:00", "fileDate": 16572384, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/92925f94bbc9f7f1a2fea7436baf07ff/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/92925f94bbc9f7f1a2fea7436baf07ff/play\", \"key\": \"<PERSON>va Here\", \"title\": \"<PERSON><PERSON> Here\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: <PERSON>BoiCarti- Ova Here\\nSolo version.\", \"date\": 16572384, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"24390a56d222004fcc2994d535d0ac90\", \"url\": \"https://api.pillowcase.su/api/download/24390a56d222004fcc2994d535d0ac90\", \"size\": \"3.61 MB\", \"duration\": 120.05}", "aliases": [], "size": "3.61 MB"}, {"id": "time-4-a-pay-khexk", "name": "Time 4 A Pay Khexk", "artists": [], "producers": ["StoopidXool"], "notes": "OG Filename: Time 4 A Pay Khexk [8-18-2016]\nA throwaway from the Ca$h Carti era said to exist by waterfalls. Leaked January 5th, 2023 and is dated August 18th, 2016. Recorded in the same session as Dats My Dawg.", "length": "2:31", "fileDate": 16728768, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/4d0915224207544fcb48aac2e9040d46/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/4d0915224207544fcb48aac2e9040d46/play\", \"key\": \"Time 4 A Pay Khexk\", \"title\": \"Time 4 A Pay Khexk\", \"artists\": \"(prod. StoopidXool)\", \"description\": \"OG Filename: Time 4 A Pay Khexk [8-18-2016]\\nA throwaway from the Ca$h Carti era said to exist by waterfalls. Leaked January 5th, 2023 and is dated August 18th, 2016. Recorded in the same session as Dats My Dawg.\", \"date\": 16728768, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6aeb7c93b9930858202d23b784f6885e\", \"url\": \"https://api.pillowcase.su/api/download/6aeb7c93b9930858202d23b784f6885e\", \"size\": \"4.12 MB\", \"duration\": 151.63}", "aliases": [], "size": "4.12 MB"}, {"id": "too-damn-basic", "name": "Too Damn Basic", "artists": [], "producers": ["BeatPluggz", "MexikoDro"], "notes": "Another song recorded on the Another Day beat. Has only chorus and open verse.", "length": "3:20", "fileDate": 17044992, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/60ba4e6af383b4ede19284e0f155af5c/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/60ba4e6af383b4ede19284e0f155af5c/play\", \"key\": \"Too Damn Basic\", \"title\": \"Too Damn Basic\", \"artists\": \"(prod. BeatPluggz & MexikoDro)\", \"aliases\": [\"Another Day\", \"Fuck That Ho\"], \"description\": \"Another song recorded on the Another Day beat. Has only chorus and open verse.\", \"date\": 17044992, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"35c469a858ae482872d14a455483f942\", \"url\": \"https://api.pillowcase.su/api/download/35c469a858ae482872d14a455483f942\", \"size\": \"4.9 MB\", \"duration\": 200.2}", "aliases": ["Another Day", "Fuck That Ho"], "size": "4.9 MB"}, {"id": "turn-up", "name": "⭐ Turn Up", "artists": [], "producers": ["<PERSON>", "Maaly Raw"], "notes": "\"Turn Up,\" a grail also known as the unreleased Maaly & Cannon beat from 2016, was supposed to be the blind group buy on January 31, 2025. Later that same day, the song was force leaked by Soul.", "length": "3:09", "fileDate": 17382816, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/f38e828bae2b554dd9ffe476346b6c06/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/f38e828bae2b554dd9ffe476346b6c06/play\", \"key\": \"Turn Up\", \"title\": \"\\u2b50 Turn Up\", \"artists\": \"(prod. <PERSON> & Maaly Raw)\", \"aliases\": [\"Hit\", \"The Don Maaly Snippet\"], \"description\": \"\\\"Turn Up,\\\" a grail also known as the unreleased Maaly & Cannon beat from 2016, was supposed to be the blind group buy on January 31, 2025. Later that same day, the song was force leaked by Soul.\", \"date\": 17382816, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"49fb8c46cc6e220c02a2ddb5fafe41b3\", \"url\": \"https://api.pillowcase.su/api/download/49fb8c46cc6e220c02a2ddb5fafe41b3\", \"size\": \"4.73 MB\", \"duration\": 189.38}", "aliases": ["Hit", "The Don Maaly Snippet"], "size": "4.73 MB"}, {"id": "top-me-off", "name": "⭐️ Top Me Off", "artists": [], "producers": ["MexikoDro"], "notes": "OG Filename: <PERSON><PERSON> <PERSON> top me off\nLeaked on September 25, 2022.", "length": "2:58", "fileDate": 16640640, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/4b92784ff5aa31925fc9abb821739f9f/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/4b92784ff5aa31925fc9abb821739f9f/play\", \"key\": \"Top Me Off\", \"title\": \"\\u2b50\\ufe0f Top Me Off\", \"artists\": \"(prod. MexikoDro)\", \"description\": \"OG Filename: Carti - top me off\\nLeaked on September 25, 2022.\", \"date\": 16640640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c193549fd9f50b6ecbb68cb52668e7d9\", \"url\": \"https://api.pillowcase.su/api/download/c193549fd9f50b6ecbb68cb52668e7d9\", \"size\": \"4.55 MB\", \"duration\": 178.8}", "aliases": [], "size": "4.55 MB"}, {"id": "drop", "name": "⭐ Drop", "artists": [], "producers": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "notes": "A throwaway from the 'Ca$h Carti' sessions.", "length": "2:43", "fileDate": 15242688, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/2ea4a7dba0103bf4c9fc06315536dcb4/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/2ea4a7dba0103bf4c9fc06315536dcb4/play\", \"key\": \"Drop\", \"title\": \"\\u2b50 Drop\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"A throwaway from the 'Ca$h Carti' sessions.\", \"date\": 15242688, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7bed94dd21f27db6e9ce407851eb8c64\", \"url\": \"https://api.pillowcase.su/api/download/7bed94dd21f27db6e9ce407851eb8c64\", \"size\": \"4.31 MB\", \"duration\": 163.32}", "aliases": [], "size": "4.31 MB"}, {"id": "loud-pack-shawty", "name": "Loud Pack Shawty", "artists": [], "producers": ["MilanMakesBeats"], "notes": "OG Filename: Carti - Loud Pack Shawty ROUGH\nThe snippet surfaced on May 19, 2023.\nUses the same instrumental as \"Vlone Thug\" with Uno.\nOGF leaked on Apr 4, 2024", "length": "2:24", "fileDate": 17044128, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/3d905ed130fa1380dfc2e92ef90bb604/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/3d905ed130fa1380dfc2e92ef90bb604/play\", \"key\": \"Loud Pack Shawty\", \"title\": \"Loud Pack Shawty\", \"artists\": \"(prod. MilanMakesBeats)\", \"aliases\": [\"Vlone Thug\"], \"description\": \"OG Filename: Carti - Loud Pack Shawty ROUGH\\nThe snippet surfaced on May 19, 2023.\\nUses the same instrumental as \\\"Vlone Thug\\\" with Uno.\\nOGF leaked on Apr 4, 2024\", \"date\": 17044128, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0865ffd34eb6e00ae7eacb85bb60bbfc\", \"url\": \"https://api.pillowcase.su/api/download/0865ffd34eb6e00ae7eacb85bb60bbfc\", \"size\": \"4 MB\", \"duration\": 144.24}", "aliases": ["Vlone Thug"], "size": "4 MB"}, {"id": "make-some-blow", "name": "✨ Make Some Blow (v1)", "artists": [], "producers": ["MexikoDro", "StoopidXool"], "notes": "OG Filename: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> first uploaded by 50kgold to his soundcloud with a lot of producer tags. OG file later leaked with way less tags.", "length": "3:10", "fileDate": 16316640, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/554169d1d1fc51956ffb2c69718ee666/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/554169d1d1fc51956ffb2c69718ee666/play\", \"key\": \"Make Some Blow (v1)\", \"title\": \"\\u2728 Make Some Blow (v1)\", \"artists\": \"(prod. MexikoDro & StoopidXool)\", \"description\": \"OG Filename: makesomeBlowCarti\\nSong first uploaded by 50k<PERSON> to his soundcloud with a lot of producer tags. OG file later leaked with way less tags.\", \"date\": 16316640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f7a201e2b1ab73d3fb8d34dcc3083938\", \"url\": \"https://api.pillowcase.su/api/download/f7a201e2b1ab73d3fb8d34dcc3083938\", \"size\": \"4.75 MB\", \"duration\": 190.78}", "aliases": [], "size": "4.75 MB"}, {"id": "pump-fake", "name": "Pump Fake! (Freestyle)", "artists": [], "producers": ["Ethereal"], "notes": "OG Filename: fake\nOG File for Pump Fake", "length": "4:40", "fileDate": 16968960, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/7b0caa6b94c4bf921fd39d9c4b04cae5/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/7b0caa6b94c4bf921fd39d9c4b04cae5/play\", \"key\": \"Pump Fake! (Freestyle)\", \"title\": \"Pump Fake! (Freestyle)\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Fake\"], \"description\": \"OG Filename: fake\\nOG File for Pump Fake\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"a80ff0199cd342af773342768c0e1bc3\", \"url\": \"https://api.pillowcase.su/api/download/a80ff0199cd342af773342768c0e1bc3\", \"size\": \"6.18 MB\", \"duration\": 280.66}", "aliases": ["Fake"], "size": "6.18 MB"}, {"id": "red-lean", "name": "<PERSON>n [V2]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: CARTI UZI CG1\nOG file for Red Lean", "length": "1:28", "fileDate": 16159392, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/07f72b0f5ee6f2dcb21d5e5e7cd3e86d/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/07f72b0f5ee6f2dcb21d5e5e7cd3e86d/play\", \"key\": \"Red Lean\", \"title\": \"Red Lean [V2]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: CARTI UZI CG1\\nOG file for Red Lean\", \"date\": 16159392, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7df11286825a7828ccece392c040ed34\", \"url\": \"https://api.pillowcase.su/api/download/7df11286825a7828ccece392c040ed34\", \"size\": \"3.11 MB\", \"duration\": 88.73}", "aliases": [], "size": "3.11 MB"}, {"id": "what", "name": "WHAT", "artists": ["UnoTheActivist"], "producers": ["<PERSON>"], "notes": "OG Filename: unoxcardimeditation\nOG Filename (Lossless): Carti uno\nOG file for WHAT", "length": "3:27", "fileDate": "", "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/8a53df44dc384904642201c9cbbf0827/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/8a53df44dc384904642201c9cbbf0827/play\", \"key\": \"WHAT\", \"title\": \"WHAT\", \"artists\": \"(feat. Uno<PERSON>heActivist) (prod. <PERSON>)\", \"description\": \"OG Filename: unoxcardimeditation\\nOG Filename (Lossless): <PERSON>ti uno\\nOG file for WHAT\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"08c1f22e703289074f712c9237cb36c0\", \"url\": \"https://api.pillowcase.su/api/download/08c1f22e703289074f712c9237cb36c0\", \"size\": \"5.01 MB\", \"duration\": 207.1}", "aliases": [], "size": "5.01 MB"}, {"id": "money-counter", "name": "A$AP Ant - Money Counter [v1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["ICYTWAT"], "notes": "OG Filename: ANT-CART-MONEY COUNTER (NO CARTI VERSE WVY WED)\nOG Version of THUMBIN by A$AP ANT & Carti", "length": "2:30", "fileDate": 17116704, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/ea53632230403a093015d63f65a43d0b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/ea53632230403a093015d63f65a43d0b/play\", \"key\": \"Money Counter\", \"title\": \"A$AP Ant - Money Counter [v1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. ICYTWAT)\", \"aliases\": [\"THUMBIN\"], \"description\": \"OG Filename: ANT-CART-MONEY COUNTER (NO CARTI VERSE WVY WED)\\nOG Version of THUMBIN by A$AP ANT & Carti\", \"date\": 17116704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"cbacf9019fdfa34c452499086952685f\", \"url\": \"https://api.pillowcase.su/api/download/cbacf9019fdfa34c452499086952685f\", \"size\": \"4.1 MB\", \"duration\": 150.55}", "aliases": ["THUMBIN"], "size": "4.1 MB"}, {"id": "what-21", "name": "<PERSON> - WHAT (Remix)", "artists": ["UnoTheActivist"], "producers": ["<PERSON>"], "notes": "OG Filename: What Remix X LiL Yachty tag\nOG File for WHAT remix", "length": "5:11", "fileDate": "", "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://pillowcase.su/f/718d1f9597768b4a04982897256c7ec5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/718d1f9597768b4a04982897256c7ec5\", \"key\": \"WHAT (Remix)\", \"title\": \"<PERSON> - WHAT (Remix)\", \"artists\": \"(feat. UnoTheActivist) (prod. <PERSON>)\", \"description\": \"OG Filename: What Remix X LiL Yachty tag\\nOG File for WHAT remix\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"75f86233ba1c4e6f484fb4494c8cdab8\", \"url\": \"https://api.pillowcase.su/api/download/75f86233ba1c4e6f484fb4494c8cdab8\", \"size\": \"6.27 MB\", \"duration\": 311.53}", "aliases": [], "size": "6.27 MB"}, {"id": "speedy-gonza<PERSON>", "name": "<PERSON> - <PERSON><PERSON>", "artists": ["<PERSON><PERSON><PERSON>"], "producers": [], "notes": "OG Filename: <PERSON><PERSON> - <PERSON><PERSON> x <PERSON> Mix for Speedy Gonzales", "length": "2:00", "fileDate": "", "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://pillowcase.su/f/a2fd7ca8e3a22db21e08749e711785fb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a2fd7ca8e3a22db21e08749e711785fb\", \"key\": \"Speedy Gonzales\", \"title\": \"<PERSON> - <PERSON> Gonzales\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON> - <PERSON> x <PERSON>\\nOG Mix for Speedy Gonzales\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"124b76069ab247b6da5ea410d43d4fe9\", \"url\": \"https://api.pillowcase.su/api/download/124b76069ab247b6da5ea410d43d4fe9\", \"size\": \"3.2 MB\", \"duration\": 120.05}", "aliases": [], "size": "3.2 MB"}, {"id": "another-day", "name": "LAMB$ - Another Day", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["BeatPluggz", "MexikoDro"], "notes": "OG Filename: LAMB$ feat reff\nOGF for Another Day.", "length": "3:22", "fileDate": ********, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/92026422491d301748b6727c6a1565cb/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/92026422491d301748b6727c6a1565cb/play\", \"key\": \"Another Day\", \"title\": \"LAMB$ - Another Day\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. BeatPluggz & MexikoDro)\", \"description\": \"OG Filename: LAMB$ feat reff\\nOGF for Another Day.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c66205ba1674ef32711956940f89fc9b\", \"url\": \"https://api.pillowcase.su/api/download/c66205ba1674ef32711956940f89fc9b\", \"size\": \"4.93 MB\", \"duration\": 202.53}", "aliases": [], "size": "4.93 MB"}, {"id": "bankroll", "name": "<PERSON> - Bankroll [V2]", "artists": [], "producers": ["DP Beats", "Greedy Money", "<PERSON>"], "notes": "OG Filename: carti - bankroll ft uzi\nThe original leak of \"Bankroll\", which caused DP to release it. This is an alternate mix of the realesed version.", "length": "4:26", "fileDate": ********, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/c17f60f18cd5ad274cbaad150e7dd7f7/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/c17f60f18cd5ad274cbaad150e7dd7f7/play\", \"key\": \"Bankroll\", \"title\": \"<PERSON><PERSON>ert - Bankroll [V2]\", \"artists\": \"(with <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>, <PERSON>reedy Money & Nicky <PERSON>)\", \"aliases\": [\"Yea\", \"Yea\"], \"description\": \"OG Filename: carti - bankroll ft uzi\\nThe original leak of \\\"Bankroll\\\", which caused DP to release it. This is an alternate mix of the realesed version.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b68f26132ee01c57e8b1ea43adb2b23d\", \"url\": \"https://api.pillowcase.su/api/download/b68f26132ee01c57e8b1ea43adb2b23d\", \"size\": \"4.73 MB\", \"duration\": 266.57}", "aliases": ["<PERSON>a", "<PERSON>a"], "size": "4.73 MB"}, {"id": "swaghollywood", "name": "<PERSON><PERSON><PERSON> - SWAGHOLLYWOOD", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON><PERSON><PERSON>", "​captaincrunch"], "notes": "OG Filename: SWAGHOLLYWOOD - <PERSON><PERSON><PERSON>oof Feat . PlayBoiCarti\nOGF for SWAGHOLLYWOOD", "length": "2:19", "fileDate": "", "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://pillowcase.su/f/d6c9a88465c8ee856a75c301ed8d5634", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d6c9a88465c8ee856a75c301ed8d5634\", \"key\": \"SWAGHOLLYWOOD\", \"title\": \"<PERSON><PERSON><PERSON> Roof - SWAGHOLLYWOOD\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>-<PERSON><PERSON> & \\u200bcaptaincrunch)\", \"description\": \"OG Filename: SWAGHOLLYWOOD - <PERSON><PERSON><PERSON> Roof Feat . PlayBoiCarti\\nOGF for SWAGHOLLYWOOD\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"b850cb361a12d5a7337a9b03015ca6e8\", \"url\": \"https://api.pillowcase.su/api/download/b850cb361a12d5a7337a9b03015ca6e8\", \"size\": \"2.31 MB\", \"duration\": 139.73}", "aliases": [], "size": "2.31 MB"}, {"id": "give-it-up", "name": "Give It Up*", "artists": [], "producers": [], "notes": "A throwaway from the Ca$h Carti sessions. Was up for a buy in may 2023", "length": "0:15", "fileDate": 16835040, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/50ec2401dcbcbac2a91d30f5ce206e17/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/50ec2401dcbcbac2a91d30f5ce206e17/play\", \"key\": \"Give It Up*\", \"title\": \"Give It Up*\", \"aliases\": [\"Fifteen\"], \"description\": \"A throwaway from the Ca$h Carti sessions. Was up for a buy in may 2023\", \"date\": 16835040, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a5cc4c11d706adbaef93d30d93f314e8\", \"url\": \"https://api.pillowcase.su/api/download/a5cc4c11d706adbaef93d30d93f314e8\", \"size\": \"1.94 MB\", \"duration\": 15.19}", "aliases": ["Fifteen"], "size": "1.94 MB"}, {"id": "catch-up", "name": "🥇 Catch Up", "artists": [], "producers": ["MexikoDro"], "notes": "A throwaway from the 'Ca$h Carti' sessions. Features the same <PERSON><PERSON><PERSON>Dro produced instrumental as “BROKE ASF” by Lil $horty & “FR” by Giza$.", "length": "0:11", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/71520ede3d2994c9a604bc94a5ccc5dc/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/71520ede3d2994c9a604bc94a5ccc5dc/play\", \"key\": \"Catch Up\", \"title\": \"\\ud83e\\udd47 Catch Up\", \"artists\": \"(prod. MexikoD<PERSON>)\", \"description\": \"A throwaway from the 'Ca$h Carti' sessions. Features the same <PERSON><PERSON><PERSON><PERSON><PERSON> produced instrumental as \\u201cBROKE ASF\\u201d by <PERSON> <PERSON> & \\u201cFR\\u201d by Giza$.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"40587c3ed51684c188ca20fcfe8f0ddd\", \"url\": \"https://api.pillowcase.su/api/download/40587c3ed51684c188ca20fcfe8f0ddd\", \"size\": \"1.87 MB\", \"duration\": 11.18}", "aliases": [], "size": "1.87 MB"}, {"id": "black-n-white", "name": "Black N White", "artists": [], "producers": ["MexikoDro", "Polo Boy <PERSON>"], "notes": "Black N White version with alt mix.", "length": "0:09", "fileDate": 14826240, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/ba24aadbebb57f1994b32fed6ca225ac/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/ba24aadbebb57f1994b32fed6ca225ac/play\", \"key\": \"Black N White\", \"title\": \"Black N White\", \"artists\": \"(prod. <PERSON>xikoDro & Polo <PERSON>)\", \"description\": \"Black N White version with alt mix.\", \"date\": 14826240, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"7e9da4cc16795dace4f736ab1d3644a6\", \"url\": \"https://api.pillowcase.su/api/download/7e9da4cc16795dace4f736ab1d3644a6\", \"size\": \"1.85 MB\", \"duration\": 9.65}", "aliases": [], "size": "1.85 MB"}, {"id": "pull-up-to-the-spot", "name": "🥇 Pull Up To The Spot", "artists": [], "producers": ["MexikoDro"], "notes": "Previewed in @mexikodro's Instagram Story in April of 2016. Samples 'Call On Me' by <PERSON>.", "length": "0:09", "fileDate": 14594688, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/adbc51e2044cd9ea1991788c3a83c8d1/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/adbc51e2044cd9ea1991788c3a83c8d1/play\", \"key\": \"Pull Up To The Spot\", \"title\": \"\\ud83e\\udd47 Pull Up To The Spot\", \"artists\": \"(prod. MexikoD<PERSON>)\", \"description\": \"Previewed in @mexikodro's Instagram Story in April of 2016. Samples 'Call On Me' by <PERSON>.\", \"date\": 14594688, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"222c5326d797c6ae1c140629baacd5ec\", \"url\": \"https://api.pillowcase.su/api/download/222c5326d797c6ae1c140629baacd5ec\", \"size\": \"1.84 MB\", \"duration\": 9.4}", "aliases": [], "size": "1.84 MB"}, {"id": "red-lean-30", "name": "<PERSON> Lean [V1]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG version of Red Lean.", "length": "0:17", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/1061be8c05a079bab5a25dc1ed5d3c84/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/1061be8c05a079bab5a25dc1ed5d3c84/play\", \"key\": \"Red Lean\", \"title\": \"<PERSON> Lean [V1]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"OG version of Red Lean.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"44bd90317ee1ee4428cba78ee38ed61e\", \"url\": \"https://api.pillowcase.su/api/download/44bd90317ee1ee4428cba78ee38ed61e\", \"size\": \"1.98 MB\", \"duration\": 17.95}", "aliases": [], "size": "1.98 MB"}, {"id": "ros", "name": "<PERSON><PERSON><PERSON> (Freestyle)*", "artists": [], "producers": [], "notes": "A freestyle sold by GucciTunes. From 2016.", "length": "", "fileDate": 17134848, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://pillowcase.su/f/ac11bcd7d483bb42f70153ed1be004dd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ac11bcd7d483bb42f70153ed1be004dd\", \"key\": \"Ros\\u00e9 (Freestyle)*\", \"title\": \"Ros\\u00e9 (Freestyle)*\", \"description\": \"A freestyle sold by GucciTunes. From 2016.\", \"date\": 17134848, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "the-block", "name": "The Block (Freestyle)*", "artists": [], "producers": [], "notes": "A freestyle sold by GucciTunes. From 2016. First snippet leaked on April 19th, 2024, second snippet leaked on January 1st, 2025.", "length": "", "fileDate": 17356896, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://pillowcase.su/f/a4422da197f029c0914132a926683599", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a4422da197f029c0914132a926683599\", \"key\": \"The Block (Freestyle)*\", \"title\": \"The Block (Freestyle)*\", \"description\": \"A freestyle sold by GucciTunes. From 2016. First snippet leaked on April 19th, 2024, second snippet leaked on January 1st, 2025.\", \"date\": 17356896, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "turning-up", "name": "🥇 Turning Up*", "artists": [], "producers": [], "notes": "A throwaway from the 'Ca$h Carti' sessions.", "length": "0:19", "fileDate": 16835040, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/a5b54ff6d3875d18534063eea4c25b6c/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a5b54ff6d3875d18534063eea4c25b6c/play\", \"key\": \"Turning Up*\", \"title\": \"\\ud83e\\udd47 Turning Up*\", \"aliases\": [\"I Made It\", \"Live It Up\"], \"description\": \"A throwaway from the 'Ca$h Carti' sessions.\", \"date\": 16835040, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"23d97fb787584541acd2d42bd177883b\", \"url\": \"https://api.pillowcase.su/api/download/23d97fb787584541acd2d42bd177883b\", \"size\": \"2.01 MB\", \"duration\": 19.93}", "aliases": ["I Made It", "Live It Up"], "size": "2.01 MB"}, {"id": "dem-callin", "name": "Ramriddlz - <PERSON><PERSON>'", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["CP BUDD", "Jaegen"], "notes": "Original version of \"Dem Callin'\".", "length": "0:15", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "ca-h-carti-season", "originalUrl": "https://music.froste.lol/song/8eae73ef7cf1f3b8bead1dddeaee634b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/8eae73ef7cf1f3b8bead1dddeaee634b/play\", \"key\": \"Dem Callin'\", \"title\": \"<PERSON><PERSON><PERSON><PERSON> - Dem Callin'\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. CP BUDD & Jaegen)\", \"description\": \"Original version of \\\"Dem Callin'\\\".\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"a00c737cc1c58b38f6ef4c19014e2e47\", \"url\": \"https://api.pillowcase.su/api/download/a00c737cc1c58b38f6ef4c19014e2e47\", \"size\": \"1.93 MB\", \"duration\": 15.07}", "aliases": [], "size": "1.93 MB"}]}