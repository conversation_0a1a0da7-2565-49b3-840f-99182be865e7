{"id": "collaboration-with-digital-nas", "name": "Collaboration with Digital Nas", "description": "Alongside work on <PERSON><PERSON>'s third mixtape, <PERSON> Nas and <PERSON><PERSON> worked on a collaboration project together. Digital Nas mentioned the project on Instagram, but the album never materialized aside from the song \"Run It\" being released by Digital Nas on soundcloud in 2015. The album as a whole was likely quietly scrapped some time in 2017. The cover for this era is from the soundcloud release of \"Run It.\"", "backgroundColor": "rgb(1, 7, 83)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17GsRBS7PpX7yeKQGNNFKyePxq1qdrOdVzgH5ZOLtuAFisPIZ_xIF7yobqUCOcj7rsRFtzcmA1l9RqQymEOqRQLbzC01sSwIVSrBgsCuc9K28qohwKsM8ageR9b9TjY6oWwPlkNH7e2R6Wd-KgIqKQ?key=QrLRwW_6ASWi9mCdhUkgTQ", "tracks": [{"id": "answer-my-phone", "name": "⭐ Answer My Phone", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: ANSWER MY PHONE MBM 1\nPreviewed in @CashCarti's Snapchat Story. Leaked on July 17, 2022. Has open verse", "length": "4:28", "fileDate": 16580160, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "collaboration-with-digital-nas", "originalUrl": "https://music.froste.lol/song/b90c40762c7e03fdc6b8ec2e83d344eb/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/b90c40762c7e03fdc6b8ec2e83d344eb/play\", \"key\": \"Answer My Phone\", \"title\": \"\\u2b50 Answer My Phone\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"Flexin' Like Dat\"], \"description\": \"OG Filename: ANSWER MY PHONE MBM 1\\nPreviewed in @CashCarti's Snapchat Story. Leaked on July 17, 2022. Has open verse\", \"date\": 16580160, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4183e7e0ada0365fa1d8c842b2cc2526\", \"url\": \"https://api.pillowcase.su/api/download/4183e7e0ada0365fa1d8c842b2cc2526\", \"size\": \"4.43 MB\", \"duration\": 268.9}", "aliases": ["Flexin' <PERSON>"], "size": "4.43 MB"}, {"id": "cartier", "name": "✨ <PERSON><PERSON>", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: CARTIER rough\nUses the same beat that would be used for \"Shut Up\". Unrelated to \"Cartier\" from the Whole Lotta Red sessions.", "length": "2:13", "fileDate": 16728768, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "collaboration-with-digital-nas", "originalUrl": "https://music.froste.lol/song/622dc8ae99897c1a6354fb33a97e5283/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/622dc8ae99897c1a6354fb33a97e5283/play\", \"key\": \"Car<PERSON>\", \"title\": \"\\u2728 <PERSON><PERSON>\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"Shut Up\"], \"description\": \"OG Filename: CARTIER rough\\nUses the same beat that would be used for \\\"Shut Up\\\". Unrelated to \\\"Cartier\\\" from the Whole Lotta Red sessions.\", \"date\": 16728768, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"06fe26420329818c71e900d24f534997\", \"url\": \"https://api.pillowcase.su/api/download/06fe26420329818c71e900d24f534997\", \"size\": \"2.26 MB\", \"duration\": 133.01}", "aliases": ["Shut Up"], "size": "2.26 MB"}, {"id": "lil-boy", "name": "Lil Boy [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: <PERSON><PERSON><PERSON> - <PERSON> [Prod. Digital Nas\nOG File Metadata: <PERSON> <PERSON>\nThrowaway with a open verse.", "length": "3:28", "fileDate": 14866848, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "collaboration-with-digital-nas", "originalUrl": "https://plwcse.top/f/3158b21156558389a7f28d97407a0502", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://plwcse.top/f/3158b21156558389a7f28d97407a0502\", \"key\": \"Lil Boy\", \"title\": \"<PERSON> Boy [V1]\", \"artists\": \"(prod. <PERSON> Nas)\", \"description\": \"OG Filename: <PERSON><PERSON><PERSON> [Prod. Digital Nas\\nOG File Metadata: Lil Boy\\nThrowaway with a open verse.\", \"date\": 14866848, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c5c7d43c34497fabb808b462375b7648\", \"url\": \"https://api.pillowcase.su/api/download/c5c7d43c34497fabb808b462375b7648\", \"size\": \"3.47 MB\", \"duration\": 208.97}", "aliases": [], "size": "3.47 MB"}, {"id": "like-that", "name": "Like That [V2]", "artists": [], "producers": ["Digital Nas"], "notes": "A version with completely new vocals and no open verse", "length": "1:20", "fileDate": 17044992, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "collaboration-with-digital-nas", "originalUrl": "https://music.froste.lol/song/0ced61fa248c22e6ff1b7080067a36b8/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/0ced61fa248c22e6ff1b7080067a36b8/play\", \"key\": \"Like That\", \"title\": \"Like That [V2]\", \"artists\": \"(prod. <PERSON> Nas) \", \"aliases\": [\"Lil Boy\"], \"description\": \"A version with completely new vocals and no open verse\", \"date\": 17044992, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c1a4fd70abe079f7099551bb59222f19\", \"url\": \"https://api.pillowcase.su/api/download/c1a4fd70abe079f7099551bb59222f19\", \"size\": \"1.41 MB\", \"duration\": 80.14}", "aliases": ["Lil Boy"], "size": "1.41 MB"}, {"id": "rollin-up", "name": "⭐ Rollin' Up", "artists": ["Lil L"], "producers": ["Digital Nas"], "notes": "OG Filename: CARTI X LUXXK\nPreviewed in a Snapchat Story. Song was released by Lil L under the name <PERSON><PERSON> & Luxxk (feat. <PERSON><PERSON>) on Feb 16, 2022, song wasnt found by fans untill Mar 16, 2022", "length": "4:40", "fileDate": 16449696, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "collaboration-with-digital-nas", "originalUrl": "https://music.froste.lol/song/5d606b981f4b0d6ec840ad29fbd3f136/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/5d606b981f4b0d6ec840ad29fbd3f136/play\", \"key\": \"Rollin' Up\", \"title\": \"\\u2b50 Rollin' Up\", \"artists\": \"(feat. <PERSON>) (prod. Digital Nas)\", \"aliases\": [\"Burnin Up\"], \"description\": \"OG Filename: CARTI X LUXXK\\nPreviewed in a Snapchat Story. Song was released by Lil L under the name <PERSON><PERSON> & <PERSON> (feat. <PERSON><PERSON>) on Feb 16, 2022, song wasnt found by fans untill Mar 16, 2022\", \"date\": 16449696, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e6030608e611a6f576ab0cc2112c5ebe\", \"url\": \"https://api.pillowcase.su/api/download/e6030608e611a6f576ab0cc2112c5ebe\", \"size\": \"4.61 MB\", \"duration\": 280.18}", "aliases": ["Burnin Up"], "size": "4.61 MB"}, {"id": "run-it", "name": "Run It [V2]", "artists": ["<PERSON>"], "producers": ["Digital Nas"], "notes": "OG Filename: <PERSON><PERSON><PERSON> - Run It Ft. Lil Yachty [Produced by Digital Nas]\nOG File for Run It with <PERSON><PERSON>", "length": "2:41", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "collaboration-with-digital-nas", "originalUrl": "https://music.froste.lol/song/b2504944ec301093ecef5ef80f19d031/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/b2504944ec301093ecef5ef80f19d031/play\", \"key\": \"Run It\", \"title\": \"Run It [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON> Nas)\", \"aliases\": [\"Get The Guap\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON> - Run It Ft. Lil Yachty [Produced by Digital Nas]\\nOG File for Run It with <PERSON>y\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7fc870e672f71287b07af64a06b97cbc\", \"url\": \"https://api.pillowcase.su/api/download/7fc870e672f71287b07af64a06b97cbc\", \"size\": \"2.71 MB\", \"duration\": 161.11}", "aliases": ["Get The Guap"], "size": "2.71 MB"}, {"id": "shut-up", "name": "Shut Up", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: SHUT UP CG1\nA throwaway from the Carti x DN tape.", "length": "2:15", "fileDate": 16585344, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "collaboration-with-digital-nas", "originalUrl": "https://music.froste.lol/song/76bdcc2712db24f45de0286a414ed665/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/76bdcc2712db24f45de0286a414ed665/play\", \"key\": \"Shut Up\", \"title\": \"Shut Up\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"White Couch\"], \"description\": \"OG Filename: SHUT UP CG1\\nA throwaway from the Carti x DN tape.\", \"date\": 16585344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4ba89ce7c8ffb7c08ee903858fc54c9c\", \"url\": \"https://api.pillowcase.su/api/download/4ba89ce7c8ffb7c08ee903858fc54c9c\", \"size\": \"2.29 MB\", \"duration\": 135.05}", "aliases": ["White Couch"], "size": "2.29 MB"}, {"id": "yah", "name": "Yah [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: PLAYBOI CARTI - YAH\nA throwaway from the Carti x DN tape. Uses the same beat as UnoTheActivist's \"Call Up The Troops\" and is registered on ACAP as \"Yah\". Leaked in the 26/05 mega leaks.", "length": "2:50", "fileDate": 16535232, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "collaboration-with-digital-nas", "originalUrl": "https://music.froste.lol/song/082385defdd5b3ef69b41701f102d91e/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/082385defdd5b3ef69b41701f102d91e/play\", \"key\": \"Yah\", \"title\": \"Yah [V1]\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"Call Up The Troops\"], \"description\": \"OG Filename: PLAYBOI CARTI - YAH\\nA throwaway from the Carti x DN tape. Uses the same beat as UnoTheActivist's \\\"Call Up The Troops\\\" and is registered on ACAP as \\\"Yah\\\". Leaked in the 26/05 mega leaks.\", \"date\": 16535232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"81714c9db9ed8c47c0c70667d5529137\", \"url\": \"https://api.pillowcase.su/api/download/81714c9db9ed8c47c0c70667d5529137\", \"size\": \"2.85 MB\", \"duration\": 170.08}", "aliases": ["Call Up The Troops"], "size": "2.85 MB"}, {"id": "yah-9", "name": "Yah [V2]", "artists": ["Offset"], "producers": ["Digital Nas"], "notes": "OG Filename: PLAYBOI CARTE- yah OffSet\nA version with more offset adlibs and different mixing.", "length": "2:48", "fileDate": 17044992, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "collaboration-with-digital-nas", "originalUrl": "https://music.froste.lol/song/50b9fa99f025ae278bfd29fe44a2daf0/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/50b9fa99f025ae278bfd29fe44a2daf0/play\", \"key\": \"Yah\", \"title\": \"Yah [V2]\", \"artists\": \"(feat. Offset) (prod. Digital Nas)\", \"aliases\": [\"Call Up The Troops\"], \"description\": \"OG Filename: PLAYBOI CARTE- yah OffSet\\nA version with more offset adlibs and different mixing.\", \"date\": 17044992, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c7c5e95b365b407330b409efeae2b34e\", \"url\": \"https://api.pillowcase.su/api/download/c7c5e95b365b407330b409efeae2b34e\", \"size\": \"2.83 MB\", \"duration\": 168.67}", "aliases": ["Call Up The Troops"], "size": "2.83 MB"}, {"id": "yah-10", "name": "⭐ Yah [V3]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: <PERSON><PERSON><PERSON> rough mix kk\nLater version of \"Yah\", featuring a much better mix than V1.", "length": "2:48", "fileDate": 16535232, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "collaboration-with-digital-nas", "originalUrl": "https://music.froste.lol/song/60b1d6aa50935618a05e9d324eb717e7/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/60b1d6aa50935618a05e9d324eb717e7/play\", \"key\": \"Yah\", \"title\": \"\\u2b50 Yah [V3]\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"Call Up The Troops\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON> rough mix kk\\nLater version of \\\"Yah\\\", featuring a much better mix than V1.\", \"date\": 16535232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"18c8341b355be18f38451c71b9222811\", \"url\": \"https://api.pillowcase.su/api/download/18c8341b355be18f38451c71b9222811\", \"size\": \"2.83 MB\", \"duration\": 168.67}", "aliases": ["Call Up The Troops"], "size": "2.83 MB"}, {"id": "yah-11", "name": "Yah [V4]", "artists": ["Offset"], "producers": ["Digital Nas"], "notes": "OG Filename: <PERSON><PERSON> new hook 2 20\nLater version of \"Yah\" but has Offset adlibs across the song in addition to an Offset verse. Leaked on October 7, 2022", "length": "2:48", "fileDate": 16651008, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "collaboration-with-digital-nas", "originalUrl": "https://music.froste.lol/song/6c4dc92dafba0a5be3328fb212c54ecf/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/6c4dc92dafba0a5be3328fb212c54ecf/play\", \"key\": \"Yah\", \"title\": \"Yah [V4]\", \"artists\": \"(feat. Offset) (prod. Digital Nas)\", \"aliases\": [\"Call Up The Troops\"], \"description\": \"OG Filename: <PERSON><PERSON> new hook 2 20\\nLater version of \\\"Yah\\\" but has Offset adlibs across the song in addition to an Offset verse. Leaked on October 7, 2022\", \"date\": 16651008, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c65fb6793bc2871e9a80f5635b92f162\", \"url\": \"https://api.pillowcase.su/api/download/c65fb6793bc2871e9a80f5635b92f162\", \"size\": \"2.83 MB\", \"duration\": 168.67}", "aliases": ["Call Up The Troops"], "size": "2.83 MB"}, {"id": "run-it-12", "name": "Run It [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "A snippet of an original version of \"Run It\" was leaked by Waterfalls which has extra adlibs and different mixing.", "length": "3:28", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "collaboration-with-digital-nas", "originalUrl": "https://music.froste.lol/song/552e9cf76ec832774b54b056f26c3bea/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/552e9cf76ec832774b54b056f26c3bea/play\", \"key\": \"Run It\", \"title\": \"Run It [V1]\", \"artists\": \"(prod. <PERSON> Nas)\", \"aliases\": [\"Get The Guap\"], \"description\": \"A snippet of an original version of \\\"Run It\\\" was leaked by Waterfalls which has extra adlibs and different mixing.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7da734087fa6d4c1c2565f194ded5e24\", \"url\": \"https://api.pillowcase.su/api/download/7da734087fa6d4c1c2565f194ded5e24\", \"size\": \"3.47 MB\", \"duration\": 208.99}", "aliases": ["Get The Guap"], "size": "3.47 MB"}]}