{"id": "cruel-winter-v1", "name": "Cruel Winter [V1]", "description": "The 2013 version of Cruel Winter, the first version of the sequel to 2012's Cruel Summer, is a mystery. No single for this album was released, and most of the info comes from leakers and insiders. It had many songs with A Tribe Called Quest member <PERSON><PERSON><PERSON><PERSON>, who was notably absent from Cruel Summer despite having already been signed to the label. There is no official cover for this album, so we're using a fanmade cover to represent this era.", "backgroundColor": "rgb(24, 24, 24)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17Fa-IsgQPgdnrXlhx4ZAKX06nlZXMoIhNyRiBMqxk1Wc9YxvPCd-LCMEnukCtvZ2wOUNcU_AxhVKruHydSpon82xGoRISboutwjednpkdq-itUi45UjfBT4dROjzQTMJEL7pO_Zh6iar0Pk-cY?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "all-the-time", "name": "All The Time [V5]", "artists": ["<PERSON>"], "producers": ["No I.D."], "notes": "OG Filename: NO ID - BIG SEAN - KW - ALL THE TIME _rkROUGH 4.18.13\n2013 version of \"All The Time\" with verses from <PERSON>. Possibly intended for his Good Music Chicago project, but the filename leaves the ownership of the song unclear.", "length": "247.51", "fileDate": 15697152, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "cruel-winter-v1", "originalUrl": "https://pillowcase.su/f/06c3407803e15678a09ec1a5ea647e53", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/06c3407803e15678a09ec1a5ea647e53\", \"key\": \"All The Time\", \"title\": \"All The Time [V5]\", \"artists\": \"(with <PERSON> & Ka<PERSON><PERSON>) (feat. <PERSON>) (prod. No I.D.)\", \"aliases\": [\"She's Like That All The Time\"], \"description\": \"OG Filename: NO ID - BIG SEAN - KW - ALL THE TIME _rkROUGH 4.18.13\\n2013 version of \\\"All The Time\\\" with verses from <PERSON>. Possibly intended for his Good Music Chicago project, but the filename leaves the ownership of the song unclear.\", \"date\": 15697152, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8d7a95709f630a39ccc5d688bcf760f0\", \"url\": \"https://api.pillowcase.su/api/download/8d7a95709f630a39ccc5d688bcf760f0\", \"size\": \"4.74 MB\", \"duration\": 247.51}", "aliases": ["She's Like That All The Time"], "size": "4.74 MB"}, {"id": "can-t-look-in-my-eyes", "name": "Can't Look In My Eyes [V6]", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "Version confirmed to be made for 2013 version of Cruel Winter, after being temporarily cut from <PERSON><PERSON>'s album. <PERSON> has been cut, possibly because <PERSON><PERSON> released and his vocals were likely used on \"Bad Mood / Shit On You\".", "length": "309.42", "fileDate": 14751936, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "cruel-winter-v1", "originalUrl": "https://pillowcase.su/f/e7c51341f3643f9c72f8e151c12a9293", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e7c51341f3643f9c72f8e151c12a9293\", \"key\": \"Can't Look In My Eyes\", \"title\": \"Can't Look In My Eyes [V6]\", \"artists\": \"(with <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Fuck\", \"Too Bad I Have To Destroy You Now\"], \"description\": \"Version confirmed to be made for 2013 version of Cruel Winter, after being temporarily cut from <PERSON><PERSON>'s album. <PERSON> has been cut, possibly because <PERSON><PERSON> released and his vocals were likely used on \\\"Bad Mood / Shit On You\\\".\", \"date\": 14751936, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"1b8084bcbe4cf992d5058d344ca5afc6\", \"url\": \"https://api.pillowcase.su/api/download/1b8084bcbe4cf992d5058d344ca5afc6\", \"size\": \"5.73 MB\", \"duration\": 309.42}", "aliases": ["Fuck", "Too Bad I Have To Destroy You Now"], "size": "5.73 MB"}, {"id": "can-t-look-in-my-eyes-3", "name": "⭐️ Can't Look In My Eyes [V7]", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "Version with some extra <PERSON> intro vocals, an extended outro and small production differences compared to the previous version. <PERSON><PERSON> Daft Punk's song \"Son of Flynn\" from the 2010 movie Tron: Legacy and <PERSON> from \"Billie Jean\".", "length": "326.08", "fileDate": 16164576, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "cruel-winter-v1", "originalUrl": "https://pillowcase.su/f/3a183cd8b18d83c9e34111d2677a1719", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3a183cd8b18d83c9e34111d2677a1719\", \"key\": \"Can't Look In My Eyes\", \"title\": \"\\u2b50\\ufe0f Can't Look In My Eyes [V7]\", \"artists\": \"(with <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Fuck\", \"Too Bad I Have To Destroy You Now\"], \"description\": \"Version with some extra <PERSON> intro vocals, an extended outro and small production differences compared to the previous version. Samples Daft Punk's song \\\"Son of Flynn\\\" from the 2010 movie Tron: Legacy and <PERSON> from \\\"Billie Jean\\\".\", \"date\": 16164576, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"66eabc977b889ac173512d1e9db42da1\", \"url\": \"https://api.pillowcase.su/api/download/66eabc977b889ac173512d1e9db42da1\", \"size\": \"6 MB\", \"duration\": 326.08}", "aliases": ["Fuck", "Too Bad I Have To Destroy You Now"], "size": "6 MB"}, {"id": "come-back-to-me", "name": "✨ Come Back To Me [V1]", "artists": ["<PERSON>"], "producers": ["DJ Camper"], "notes": "OG Filename: Come Back To Me Ref 10.15.12\nEarlier version, features a reference artist on the hook who is assumed to be <PERSON>, with alternate mixing and vocal structure. Confirmed to be from post-Cruel Summer judging from the filename, but unconfirmed if it was <PERSON>' song or <PERSON><PERSON><PERSON>'s song at this time. Samples \"Let Me Prove My Love to You\" by The Main Ingredient.", "length": "201.31", "fileDate": 16657056, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "cruel-winter-v1", "originalUrl": "https://pillowcase.su/f/fe0f83cc9b4825952788827de5a5de33", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fe0f83cc9b4825952788827de5a5de33\", \"key\": \"Come Back To Me\", \"title\": \"\\u2728 Come Back To Me [V1]\", \"artists\": \"(ref. <PERSON>) (with <PERSON><PERSON><PERSON>) (feat. <PERSON>) (prod. DJ <PERSON>er)\", \"description\": \"OG Filename: Come Back To Me Ref 10.15.12\\nEarlier version, features a reference artist on the hook who is assumed to be <PERSON>, with alternate mixing and vocal structure. Confirmed to be from post-Cruel Summer judging from the filename, but unconfirmed if it was <PERSON>' song or <PERSON><PERSON><PERSON>'s song at this time. Samples \\\"Let Me Prove My Love to You\\\" by The Main Ingredient.\", \"date\": 16657056, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"62381b54bf1721fa61c8c870b2ff94ca\", \"url\": \"https://api.pillowcase.su/api/download/62381b54bf1721fa61c8c870b2ff94ca\", \"size\": \"4 MB\", \"duration\": 201.31}", "aliases": [], "size": "4 MB"}, {"id": "come-back-to-me-5", "name": "Come Back To Me [V2]", "artists": [], "producers": ["DJ Camper"], "notes": "OG Filename: Come Back To Me Sevyn pre 1\nVersion with vocals from <PERSON><PERSON>. Made sometime in 2013.", "length": "201.07", "fileDate": 17180640, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "cruel-winter-v1", "originalUrl": "https://pillowcase.su/f/7b180860102a76fcca5f661f438228d4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7b180860102a76fcca5f661f438228d4\", \"key\": \"Come Back To Me\", \"title\": \"Come Back To Me [V2]\", \"artists\": \"(with ??? & <PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: Come Back To Me Sevyn pre 1\\nVersion with vocals from <PERSON><PERSON>. Made sometime in 2013.\", \"date\": 17180640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"09ec54ed43180a73a8c200b00df4515c\", \"url\": \"https://api.pillowcase.su/api/download/09ec54ed43180a73a8c200b00df4515c\", \"size\": \"4 MB\", \"duration\": 201.07}", "aliases": [], "size": "4 MB"}, {"id": "come-back-to-me-6", "name": "Come Back To Me [V3]", "artists": ["<PERSON>"], "producers": ["DJ Camper"], "notes": "OG Filename: Come Back To Me\nHas no Kanye. Made sometime in 2013.", "length": "206.21", "fileDate": 17180640, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "cruel-winter-v1", "originalUrl": "https://pillowcase.su/f/92c369d83d647eb32a2cd4390353da67", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/92c369d83d647eb32a2cd4390353da67\", \"key\": \"Come Back To Me\", \"title\": \"Come Back To Me [V3]\", \"artists\": \"(with <PERSON><PERSON>) (feat. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: Come Back To Me\\nHas no Kanye. Made sometime in 2013.\", \"date\": 17180640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c3ee41bfe668fb3f6411b28d1f7504c3\", \"url\": \"https://api.pillowcase.su/api/download/c3ee41bfe668fb3f6411b28d1f7504c3\", \"size\": \"4.09 MB\", \"duration\": 206.21}", "aliases": [], "size": "4.09 MB"}, {"id": "come-back-to-me-7", "name": "Come Back To Me [V4]", "artists": ["<PERSON>"], "producers": ["DJ Camper"], "notes": "Has <PERSON><PERSON> doing the hook and background vocals. Most likely recorded after the other version as <PERSON><PERSON> is on this version, and she would later release the song in 2020. Leaked in full by P<PERSON><PERSON> in December 2019.", "length": "203.36", "fileDate": 15760224, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "cruel-winter-v1", "originalUrl": "https://pillowcase.su/f/ab6050578284e21b356f49a0a2bd29c6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ab6050578284e21b356f49a0a2bd29c6\", \"key\": \"Come Back To Me\", \"title\": \"Come Back To Me [V4]\", \"artists\": \"(with <PERSON><PERSON> & <PERSON><PERSON><PERSON>) (feat. <PERSON>) (prod. DJ <PERSON>er)\", \"description\": \"<PERSON> <PERSON><PERSON> doing the hook and background vocals. Most likely recorded after the other version as <PERSON><PERSON> is on this version, and she would later release the song in 2020. Leaked in full by Pluto in December 2019.\", \"date\": 15760224, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bbe5a2c9c6c47b8e689fba1eb0fda3bf\", \"url\": \"https://api.pillowcase.su/api/download/bbe5a2c9c6c47b8e689fba1eb0fda3bf\", \"size\": \"4.03 MB\", \"duration\": 203.36}", "aliases": [], "size": "4.03 MB"}, {"id": "cruel-winter", "name": "Cruel Winter", "artists": [], "producers": ["<PERSON><PERSON> <PERSON>"], "notes": "OG Filename: <PERSON><PERSON> \"Cruel Winter\" voice memo. Likely meant to be an album intro. Leaked after the previously leaked file was discovered to be an edit.", "length": "20.87", "fileDate": 17354304, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "cruel-winter-v1", "originalUrl": "https://pillowcase.su/f/333cfc9f56391d272d8a65d0cd58734c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/333cfc9f56391d272d8a65d0cd58734c\", \"key\": \"Cruel Winter\", \"title\": \"Cruel Winter\", \"artists\": \"(with <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: Memo\\nOG \\\"Cruel Winter\\\" voice memo. Likely meant to be an album intro. Leaked after the previously leaked file was discovered to be an edit.\", \"date\": 17354304, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"52ec25944d5b88b7e2adf7ba8b4738f1\", \"url\": \"https://api.pillowcase.su/api/download/52ec25944d5b88b7e2adf7ba8b4738f1\", \"size\": \"946 kB\", \"duration\": 20.87}", "aliases": [], "size": "946 kB"}, {"id": "higher", "name": "Higher", "artists": [], "producers": ["DJ Camper"], "notes": "Mumble freestyle recorded on an instrumental which ended up being used by <PERSON> on his song \"Fire\". <PERSON> interpreted the sample as repeating \"Fire\", but when <PERSON><PERSON><PERSON> recorded he heard it as \"Higher\" and based the idea on that. Snippet comes from a behind the scenes video from <PERSON> <PERSON><PERSON> on how the song was made.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "cruel-winter-v1", "originalUrl": "https://youtu.be/L0bIqD5X5DE", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://youtu.be/L0bIqD5X5DE\", \"key\": \"Higher\", \"title\": \"Higher\", \"artists\": \"(with <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Fire\"], \"description\": \"Mumble freestyle recorded on an instrumental which ended up being used by <PERSON> on his song \\\"Fire\\\". <PERSON> interpreted the sample as repeating \\\"Fire\\\", but when <PERSON><PERSON><PERSON> recorded he heard it as \\\"Higher\\\" and based the idea on that. Snippet comes from a behind the scenes video from <PERSON> <PERSON><PERSON> on how the song was made.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Fire"], "size": ""}, {"id": "higher-10", "name": "Higher", "artists": [], "producers": ["DJ Camper"], "notes": "Mumble freestyle recorded on an instrumental which ended up being used by <PERSON> on his song \"Fire\". <PERSON> interpreted the sample as repeating \"Fire\", but when <PERSON><PERSON><PERSON> recorded he heard it as \"Higher\" and based the idea on that. Snippet comes from a behind the scenes video from <PERSON> <PERSON><PERSON> on how the song was made.", "length": "54.52", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "cruel-winter-v1", "originalUrl": "https://pillowcase.su/f/ec799fe7df0a9c9bc6d68515c85543e4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ec799fe7df0a9c9bc6d68515c85543e4\", \"key\": \"Higher\", \"title\": \"Higher\", \"artists\": \"(with <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"aliases\": [\"Fire\"], \"description\": \"Mumble freestyle recorded on an instrumental which ended up being used by <PERSON> on his song \\\"Fire\\\". <PERSON> interpreted the sample as repeating \\\"Fire\\\", but when <PERSON><PERSON><PERSON> recorded he heard it as \\\"Higher\\\" and based the idea on that. Snippet comes from a behind the scenes video from <PERSON><PERSON> on how the song was made.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"7bb148b65836320dc13ca761de028dc9\", \"url\": \"https://api.pillowcase.su/api/download/7bb148b65836320dc13ca761de028dc9\", \"size\": \"1.65 MB\", \"duration\": 54.52}", "aliases": ["Fire"], "size": "1.65 MB"}, {"id": "enjoy-the-pain", "name": "<PERSON> - <PERSON><PERSON> [V3]", "artists": [], "producers": [], "notes": "Later version of \"Enjoy The Pain\", by <PERSON>. Original snippet leaked October 16th, 2022.", "length": "216.74", "fileDate": 16955136, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "cruel-winter-v1", "originalUrl": "https://pillowcase.su/f/6719b1df75180f1b2583db5da0b0a0fb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6719b1df75180f1b2583db5da0b0a0fb\", \"key\": \"Enjoy The Pain\", \"title\": \"<PERSON> - Enjoy The Pain [V3]\", \"description\": \"Later version of \\\"Enjoy The Pain\\\", by <PERSON>. Original snippet leaked October 16th, 2022.\", \"date\": 16955136, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"6afeb541b5ef5ac2808e53b3836fa254\", \"url\": \"https://api.pillowcase.su/api/download/6afeb541b5ef5ac2808e53b3836fa254\", \"size\": \"4.25 MB\", \"duration\": 216.74}", "aliases": [], "size": "4.25 MB"}, {"id": "", "name": "<PERSON><PERSON> - ???", "artists": [], "producers": ["DJ Camper"], "notes": "After <PERSON><PERSON><PERSON>'s demo <PERSON><PERSON> sang a hook over the \"Fire\" beat before it was given to <PERSON>. Her version inspired the portion sang by <PERSON> on the final song.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "cruel-winter-v1", "originalUrl": "https://youtu.be/L0bIqD5X5DE?t=388", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://youtu.be/L0bIqD5X5DE?t=388\", \"key\": \"???\", \"title\": \"<PERSON><PERSON> - ???\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Fire\"], \"description\": \"After <PERSON><PERSON><PERSON>'s demo <PERSON><PERSON> sang a hook over the \\\"Fire\\\" beat before it was given to <PERSON>. Her version inspired the portion sang by <PERSON> on the final song.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["Fire"], "size": ""}]}