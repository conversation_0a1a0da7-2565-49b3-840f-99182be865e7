{"id": "jesus-is-lord", "name": "JESUS IS LORD", "description": "Shortly after the release of JESUS IS KING, <PERSON><PERSON><PERSON> (almost immediately) started working on new material. Songs from this era revolve around his faith while also consisting of dark themes (such as prison) and lyrics about current social issues. Initially announced as God's Country on May 20th, 2020 by <PERSON>, tracks from this album would go on to be developed further in the DONDA [V1] era, following <PERSON><PERSON><PERSON> getting new inspiration to make an album dedicated to his mother.", "backgroundColor": "rgb(30, 30, 108)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17G3DA1etlhsaCtR0Rp38W2KdyIjFe5YTr0rXiYHBvYRlifqx44jlLFn97lD0sUIrrOx4GLsC0mEE9Djcz6U0DuqoVZp-aewZ7O0RshM72-fj3pleaeg7KwHoKyLIlwV6_DTm1hs9wiRr6W1q665Dg?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "12-000-acres", "name": "12,000 Acres [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 12000 acres fs 2 - Dm - 146\nStructured version of the freestyle, with production changes.", "length": "186.63", "fileDate": 16954272, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/9a4174e0f29c71bf1011260323e13c57", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9a4174e0f29c71bf1011260323e13c57\", \"key\": \"12,000 Acres\", \"title\": \"12,000 Acres [V2]\", \"artists\": \"(prod. BoogzDaBeast)\", \"aliases\": [\"12 Thousand Acres\"], \"description\": \"OG Filename: 12000 acres fs 2 - Dm - 146\\nStructured version of the freestyle, with production changes.\", \"date\": 16954272, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ccddf0586131736f97acefa5c043a544\", \"url\": \"https://api.pillowcase.su/api/download/ccddf0586131736f97acefa5c043a544\", \"size\": \"5.03 MB\", \"duration\": 186.63}", "aliases": ["12 Thousand Acres"], "size": "5.03 MB"}, {"id": "12-000-acres-2", "name": "12,000 Acres [V3]", "artists": [], "producers": ["Wheezy", "BoogzDaBeast"], "notes": "OG Filename: 12000 acres_Selected-1\nEarliest version with production done by <PERSON><PERSON><PERSON>, has different drum placement from the other version.", "length": "171.02", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/6523b185208bfa4cb3797ae365071aca", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6523b185208bfa4cb3797ae365071aca\", \"key\": \"12,000 Acres\", \"title\": \"12,000 Acres [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & BoogzDaBeast)\", \"aliases\": [\"12 Thousand Acres\"], \"description\": \"OG Filename: 12000 acres_Selected-1\\nEarliest version with production done by <PERSON><PERSON><PERSON>, has different drum placement from the other version.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"23d3285eb924a244b0747f4c5e09f623\", \"url\": \"https://api.pillowcase.su/api/download/23d3285eb924a244b0747f4c5e09f623\", \"size\": \"4.78 MB\", \"duration\": 171.02}", "aliases": ["12 Thousand Acres"], "size": "4.78 MB"}, {"id": "12-000-acres-3", "name": "12,000 Acres [V4]", "artists": [], "producers": ["Wheezy", "BoogzDaBeast"], "notes": "OG Filename: 12,000 Acres - Wheezy Ref @ 73bpm\nHas the Wheezy drums placed at a different point.", "length": "171.78", "fileDate": 16943904, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/7cf94548c02bbb5b8dbdaebc18199527", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7cf94548c02bbb5b8dbdaebc18199527\", \"key\": \"12,000 Acres\", \"title\": \"12,000 Acres [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & BoogzDaBeast)\", \"aliases\": [\"12 Thousand Acres\"], \"description\": \"OG Filename: 12,000 Acres - Wheezy Ref @ 73bpm\\nHas the Wheezy drums placed at a different point.\", \"date\": 16943904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"79dbf891f695c4e67af68a9ab19f3bc8\", \"url\": \"https://api.pillowcase.su/api/download/79dbf891f695c4e67af68a9ab19f3bc8\", \"size\": \"4.79 MB\", \"duration\": 171.78}", "aliases": ["12 Thousand Acres"], "size": "4.79 MB"}, {"id": "12-000-acres-4", "name": "12,000 Acres [V5]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 12,000 Acres - 11.21.19 CyHi REF\nFirst CyHi reference track, has slightly alternate production.", "length": "118.42", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/ab3f9acfb485f30bec1ad907995f3573", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ab3f9acfb485f30bec1ad907995f3573\", \"key\": \"12,000 Acres\", \"title\": \"12,000 Acres [V5]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. BoogzDaBeast)\", \"aliases\": [\"12 Thousand Acres\"], \"description\": \"OG Filename: 12,000 Acres - 11.21.19 CyHi REF\\nFirst CyHi reference track, has slightly alternate production.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"8cb5d37cb3f328f8169b5c99e3b18a03\", \"url\": \"https://api.pillowcase.su/api/download/8cb5d37cb3f328f8169b5c99e3b18a03\", \"size\": \"3.94 MB\", \"duration\": 118.42}", "aliases": ["12 Thousand Acres"], "size": "3.94 MB"}, {"id": "12-000-acres-5", "name": "12,000 Acres [V6]", "artists": [], "producers": ["BoogzDaBeast", "Israel Boyd"], "notes": "OG Filename: 12000 Acres - 11.21.19 CyHi Writing\nSecond CyHi reference track, featuring alternate lyrics closer to <PERSON>'s original reference and further along production. Original snippets leaked March 3rd, 2023 & October 5th, 2024, later leaking after a soakbuy💦.", "length": "161.18", "fileDate": 17330112, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/cc0e776cb5c64a1e22d19a7103b15c99", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cc0e776cb5c64a1e22d19a7103b15c99\", \"key\": \"12,000 Acres\", \"title\": \"12,000 Acres [V6]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON>zDaBeast & <PERSON>)\", \"aliases\": [\"12 Thousand Acres\"], \"description\": \"OG Filename: 12000 Acres - 11.21.19 CyHi Writing\\nSecond CyHi reference track, featuring alternate lyrics closer to <PERSON>'s original reference and further along production. Original snippets leaked March 3rd, 2023 & October 5th, 2024, later leaking after a soakbuy\\ud83d\\udca6.\", \"date\": 17330112, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8421e268bcd7732e27a9880b6abb3822\", \"url\": \"https://api.pillowcase.su/api/download/8421e268bcd7732e27a9880b6abb3822\", \"size\": \"4.62 MB\", \"duration\": 161.18}", "aliases": ["12 Thousand Acres"], "size": "4.62 MB"}, {"id": "12-000-acres-6", "name": "12,000 Acres [V7]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "Consequence reference track, played on his Instagram Live.", "length": "6.96", "fileDate": 16897248, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/7d7811c8eef19ab2c40f136de03dcc0e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7d7811c8eef19ab2c40f136de03dcc0e\", \"key\": \"12,000 Acres\", \"title\": \"12,000 Acres [V7]\", \"artists\": \"(ref. Consequence) (prod. BoogzDaBeast) \", \"aliases\": [\"12 Thousand Acres\"], \"description\": \"Consequence reference track, played on his Instagram Live.\", \"date\": 16897248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"3b5f88456a4970ba2f5b58b65ec5efa9\", \"url\": \"https://api.pillowcase.su/api/download/3b5f88456a4970ba2f5b58b65ec5efa9\", \"size\": \"2.16 MB\", \"duration\": 6.96}", "aliases": ["12 Thousand Acres"], "size": "2.16 MB"}, {"id": "12-000-acres-7", "name": "12,000 Acres [V7]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "Consequence reference track, played on his Instagram Live.", "length": "", "fileDate": 16897248, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/f442e997897068eefc611c431368c7fa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f442e997897068eefc611c431368c7fa\", \"key\": \"12,000 Acres\", \"title\": \"12,000 Acres [V7]\", \"artists\": \"(ref. Consequence) (prod. BoogzDaBeast) \", \"aliases\": [\"12 Thousand Acres\"], \"description\": \"Consequence reference track, played on his Instagram Live.\", \"date\": 16897248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["12 Thousand Acres"], "size": ""}, {"id": "all-praises-due-him", "name": "All Praises Due Him [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: All Praises Due Him\nOn a November tracklist. Recorded alongside 100+ other freestyles. <PERSON> mumble and singing. Snippet of the instrumental was leaked on November 19th, 2022. A snippet of the actual song leaked November 30th, 2022.", "length": "249.94", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/5cdb93dabe1472946490b82995b99b50", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5cdb93dabe1472946490b82995b99b50\", \"key\": \"All Praises Due Him\", \"title\": \"All Praises Due Him [V1]\", \"artists\": \"(prod. BoogzDaBeast)\", \"description\": \"OG Filename: All Praises Due Him\\nOn a November tracklist. Recorded alongside 100+ other freestyles. Has mumble and singing. Snippet of the instrumental was leaked on November 19th, 2022. A snippet of the actual song leaked November 30th, 2022.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5f9534e612211aefe2fa0c1d9610b774\", \"url\": \"https://api.pillowcase.su/api/download/5f9534e612211aefe2fa0c1d9610b774\", \"size\": \"6.04 MB\", \"duration\": 249.94}", "aliases": [], "size": "6.04 MB"}, {"id": "all-praises-due-him-9", "name": "All Praises Due Him [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: All Praises Due fs edit\nRestructure and cutdown of the freestyle.", "length": "152.35", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/d259165ddb01519a90f7d869fad6b4fc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d259165ddb01519a90f7d869fad6b4fc\", \"key\": \"All Praises Due Him\", \"title\": \"All Praises Due Him [V2]\", \"artists\": \"(prod. BoogzDaBeast)\", \"description\": \"OG Filename: All Praises Due fs edit\\nRestructure and cutdown of the freestyle.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"181ba0b03b31c243f6f66f98baa3f1cc\", \"url\": \"https://api.pillowcase.su/api/download/181ba0b03b31c243f6f66f98baa3f1cc\", \"size\": \"4.48 MB\", \"duration\": 152.35}", "aliases": [], "size": "4.48 MB"}, {"id": "awakening", "name": "Awakening [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Awakening\nOn a November tracklist. Earliest known version, recorded alongside 100+ other freestyles. Described as having \"crazy ass saxophones, like hardcore 'Crash Landing' energy\". Has different production compared to the July 2020 version. Samples \"<PERSON><PERSON>\" by <PERSON><PERSON>. Instrumental originally leaked November 13th, 2022.", "length": "133.62", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/34349f17d93233bb6cc81e8c2e211a70", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/34349f17d93233bb6cc81e8c2e211a70\", \"key\": \"Awakening\", \"title\": \"Awakening [V1]\", \"artists\": \"(prod. <PERSON><PERSON>zDaBeas<PERSON>)\", \"description\": \"OG Filename: Awakening\\nOn a November tracklist. Earliest known version, recorded alongside 100+ other freestyles. Described as having \\\"crazy ass saxophones, like hardcore 'Crash Landing' energy\\\". Has different production compared to the July 2020 version. Samples \\\"<PERSON><PERSON>\\\" by <PERSON><PERSON>. Instrumental originally leaked November 13th, 2022.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"18835aaa6a3276ee76154d02dd0a18f7\", \"url\": \"https://api.pillowcase.su/api/download/18835aaa6a3276ee76154d02dd0a18f7\", \"size\": \"4.18 MB\", \"duration\": 133.62}", "aliases": [], "size": "4.18 MB"}, {"id": "awakening-11", "name": "Awakening [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Awakening - 11.13.19 <PERSON> Ref\nAlternate mix of the original recording. Leaked on September 22nd, 2023. OG lossless file leaked sometime later.", "length": "132.69", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/c95f5128809d443fd415d213db0ccd29", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c95f5128809d443fd415d213db0ccd29\", \"key\": \"Awakening\", \"title\": \"Awakening [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>aBeas<PERSON>)\", \"description\": \"OG Filename: Awakening - 11.13.19 Ye Ref\\nAlternate mix of the original recording. Leaked on September 22nd, 2023. OG lossless file leaked sometime later.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f3695408ba340191bcea31c469b1c032\", \"url\": \"https://api.pillowcase.su/api/download/f3695408ba340191bcea31c469b1c032\", \"size\": \"4.17 MB\", \"duration\": 132.69}", "aliases": [], "size": "4.17 MB"}, {"id": "awakening-12", "name": "Awakening [V3]", "artists": [], "producers": ["<PERSON>", "BoogzDaBeast"], "notes": "OG Filename: Awakening (Boogz_FedeHorns) v1.2 80.5bpm\nHas added Federico <PERSON> horns. File is corrupt, and stops playing audio midway through.", "length": "150.56", "fileDate": ********, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/f11342397f3ecf90f0db1f73bdd2cac0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f11342397f3ecf90f0db1f73bdd2cac0\", \"key\": \"Awakening\", \"title\": \"Awakening [V3]\", \"artists\": \"(prod. <PERSON> & BoogzDaBeast)\", \"description\": \"OG Filename: Awakening (Boogz_FedeHorns) v1.2 80.5bpm\\nHas added <PERSON> horns. File is corrupt, and stops playing audio midway through.\", \"date\": ********, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"96200584586289bdab3d8fd06af491c5\", \"url\": \"https://api.pillowcase.su/api/download/96200584586289bdab3d8fd06af491c5\", \"size\": \"4.45 MB\", \"duration\": 150.56}", "aliases": [], "size": "4.45 MB"}, {"id": "bank", "name": "Bank", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: <PERSON><PERSON><PERSON>f\nRecorded alongside 100+ other freestyles. Has mumble.", "length": "116.02", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/a738f93ac1bf3f985b5df481a754d6d4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a738f93ac1bf3f985b5df481a754d6d4\", \"key\": \"Bank\", \"title\": \"Bank\", \"artists\": \"(prod. BoogzDaBeast)\", \"description\": \"OG Filename: <PERSON>N<PERSON> Ref\\nRecorded alongside 100+ other freestyles. Has mumble.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"054aa18cb278633f1193ec2a4856717b\", \"url\": \"https://api.pillowcase.su/api/download/054aa18cb278633f1193ec2a4856717b\", \"size\": \"3.9 MB\", \"duration\": 116.02}", "aliases": [], "size": "3.9 MB"}, {"id": "breeverly", "name": "<PERSON><PERSON><PERSON>", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 191108 Ye Free_13 <PERSON><PERSON><PERSON> - Ref\nRecorded alongside 100+ other freestyles. Has singing and mumble.", "length": "104.96", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/f00b1ca706b43e04075c73a0301c8a4c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f00b1ca706b43e04075c73a0301c8a4c\", \"key\": \"<PERSON>ver<PERSON>\", \"title\": \"<PERSON><PERSON><PERSON>\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>DaBeas<PERSON>)\", \"description\": \"OG Filename: 191108 Ye Free_13 Bree<PERSON><PERSON> - Ref\\nRecorded alongside 100+ other freestyles. Has singing and mumble.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4eea2937b71255688b753a3126197ac7\", \"url\": \"https://api.pillowcase.su/api/download/4eea2937b71255688b753a3126197ac7\", \"size\": \"3.72 MB\", \"duration\": 104.96}", "aliases": [], "size": "3.72 MB"}, {"id": "crash-landing", "name": "Crash Landing [V2]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "Snippet played during the leaked 500 Days In UCLA documentary. Features alternate production, and no <PERSON><PERSON><PERSON><PERSON> vocals. <PERSON><PERSON>' vocals were likely recorded in the same session as \"Eternal Life\".", "length": "86.06", "fileDate": 16955136, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/03d26d68da154143600d27a352843d5c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/03d26d68da154143600d27a352843d5c\", \"key\": \"Crash Landing\", \"title\": \"Crash Landing [V2]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"description\": \"Snippet played during the leaked 500 Days In UCLA documentary. Features alternate production, and no <PERSON><PERSON><PERSON><PERSON> vocals. <PERSON><PERSON>' vocals were likely recorded in the same session as \\\"Eternal Life\\\".\", \"date\": 16955136, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"6dea7d37a6873b8b7ed6fc56199bb910\", \"url\": \"https://api.pillowcase.su/api/download/6dea7d37a6873b8b7ed6fc56199bb910\", \"size\": \"3.42 MB\", \"duration\": 86.06}", "aliases": [], "size": "3.42 MB"}, {"id": "we-got-eternal-life", "name": "We Got Eternal Life [V4]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Devil Weak - 01 We Got Eternal Life 11.18.19 Kaycyy REF 2\nKayCyy reference track. Has slightly different drums compared to the <PERSON><PERSON><PERSON> produced version.", "length": "157.15", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/e3ab22aa7a57ad5c6a1a8a5e69ba2492", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e3ab22aa7a57ad5c6a1a8a5e69ba2492\", \"key\": \"We Got Eternal Life\", \"title\": \"We Got Eternal Life [V4]\", \"artists\": \"(ref. <PERSON>) (prod. Bo<PERSON>zDaBeast)\", \"aliases\": [\"Ambitious\", \"Devil Weak\", \"The Devil Is Weak\", \"Eternal Life\"], \"description\": \"OG Filename: Devil Weak - 01 We Got Eternal Life 11.18.19 Kaycyy REF 2\\nKayCyy reference track. Has slightly different drums compared to the S<PERSON>e <PERSON> produced version.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"80219879a370cef3ef7dd99354779324\", \"url\": \"https://api.pillowcase.su/api/download/80219879a370cef3ef7dd99354779324\", \"size\": \"4.56 MB\", \"duration\": 157.15}", "aliases": ["Ambitious", "Devil Weak", "The Devil Is Weak", "Eternal Life"], "size": "4.56 MB"}, {"id": "eternal-life", "name": "Eternal Life [V6]", "artists": ["Big Sean"], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Eternal Life FS\nCut-down, lacks drums.", "length": "160.65", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/142c867570da2413df4ca21868483459", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/142c867570da2413df4ca21868483459\", \"key\": \"Eternal Life\", \"title\": \"Eternal Life [V6]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Ambitious\", \"Devil Weak\", \"The Devil Is Weak\", \"We Got Eternal Life\"], \"description\": \"OG Filename: Eternal Life FS\\nCut-down, lacks drums.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"626452b5f5918a4d265d14f5816ba204\", \"url\": \"https://api.pillowcase.su/api/download/626452b5f5918a4d265d14f5816ba204\", \"size\": \"4.61 MB\", \"duration\": 160.65}", "aliases": ["Ambitious", "Devil Weak", "The Devil Is Weak", "We Got Eternal Life"], "size": "4.61 MB"}, {"id": "eternal-life-18", "name": "Eternal Life [V7]", "artists": ["Big Sean"], "producers": ["BoogzDaBeast", "Wheezy"], "notes": "OG Filename: eternal LIFE_Selected-1\nInitial Wheezy-produced version. Has a mumble Big Sean line that he rerecorded for later versions, as well as extra <PERSON><PERSON><PERSON> at the end.", "length": "162.74", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/faffeaca591b4f6a90af51c2acd4853b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/faffeaca591b4f6a90af51c2acd4853b\", \"key\": \"Eternal Life\", \"title\": \"Eternal Life [V7]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>ogzDaBeast & Wheezy)\", \"aliases\": [\"Ambitious\", \"Devil Weak\", \"The Devil Is Weak\", \"We Got Eternal Life\"], \"description\": \"OG Filename: eternal LIFE_Selected-1\\nInitial Wheezy-produced version. Has a mumble Big Sean line that he rerecorded for later versions, as well as extra Kanye at the end.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4e08976270ff83398fa86f0ab9928287\", \"url\": \"https://api.pillowcase.su/api/download/4e08976270ff83398fa86f0ab9928287\", \"size\": \"4.65 MB\", \"duration\": 162.74}", "aliases": ["Ambitious", "Devil Weak", "The Devil Is Weak", "We Got Eternal Life"], "size": "4.65 MB"}, {"id": "faithful", "name": "Faithful [V1]", "artists": ["Sunday Service Choir"], "producers": ["BoogzDaBeast", "FnZ"], "notes": "OG Filename: 48 Faithful\nInitial freestyle, recorded alongside 100+ other freestyles.", "length": "196.74", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/474c0c799e426eb9d55942e798248a41", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/474c0c799e426eb9d55942e798248a41\", \"key\": \"Faithful\", \"title\": \"Faithful [V1]\", \"artists\": \"(feat. Sunday Service Choir) (prod. BoogzDaBeast & FnZ)\", \"aliases\": [\"Fighting Fires\", \"Fightin Fire\", \"My Soul\"], \"description\": \"OG Filename: 48 Faithful\\nInitial freestyle, recorded alongside 100+ other freestyles.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8edc0727b78d3436f72f675721613691\", \"url\": \"https://api.pillowcase.su/api/download/8edc0727b78d3436f72f675721613691\", \"size\": \"5.19 MB\", \"duration\": 196.74}", "aliases": ["Fighting Fires", "Fightin Fire", "My Soul"], "size": "5.19 MB"}, {"id": "fighting-fires", "name": "Fighting Fires [V2]", "artists": ["Sunday Service Choir"], "producers": ["BoogzDaBeast", "FnZ"], "notes": "Arranged version of the \"Fighting Fires\" freestyle.", "length": "152.21", "fileDate": 15962400, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/d044573efbf134f0dd2d4062d3414be9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d044573efbf134f0dd2d4062d3414be9\", \"key\": \"Fighting Fires\", \"title\": \"Fighting Fires [V2]\", \"artists\": \"(feat. Sunday Service Choir) (prod. BoogzDaBeast & FnZ)\", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"My Soul\"], \"description\": \"Arranged version of the \\\"Fighting Fires\\\" freestyle.\", \"date\": 15962400, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c286b55c47bed7a234737503b67fc55b\", \"url\": \"https://api.pillowcase.su/api/download/c286b55c47bed7a234737503b67fc55b\", \"size\": \"4.48 MB\", \"duration\": 152.21}", "aliases": ["Faithful", "Fightin Fire", "My Soul"], "size": "4.48 MB"}, {"id": "fighting-fires-21", "name": "Fighting Fires [V3]", "artists": ["Sunday Service Choir"], "producers": ["BoogzDaBeast", "FnZ"], "notes": "OG Filename: Fighting Fires - 11.12.19 Ye Short Edit\nShort version of the original freestyle.", "length": "65.57", "fileDate": 16962048, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/1e43071358376a4579311ca04ad525e1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1e43071358376a4579311ca04ad525e1\", \"key\": \"Fighting Fires\", \"title\": \"Fighting Fires [V3]\", \"artists\": \"(feat. Sunday Service Choir) (prod. BoogzDaBeast & FnZ)\", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"My Soul\"], \"description\": \"OG Filename: Fighting Fires - 11.12.19 Ye Short Edit\\nShort version of the original freestyle.\", \"date\": 16962048, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"087fb4b76565afe90bd9f0a0238710c5\", \"url\": \"https://api.pillowcase.su/api/download/087fb4b76565afe90bd9f0a0238710c5\", \"size\": \"3.09 MB\", \"duration\": 65.57}", "aliases": ["Faithful", "Fightin Fire", "My Soul"], "size": "3.09 MB"}, {"id": "fighting-fires-22", "name": "Fighting Fires [V4]", "artists": ["Sunday Service Choir"], "producers": ["BoogzDaBeast", "FnZ"], "notes": "OG Filename: Fighting Fires - 11.12.19 Ye Short Edit_fixed\nShort version of the original freestyle with something fixed from the prior version.", "length": "65.62", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/bb389b4971d331816713998d76e8dd49", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bb389b4971d331816713998d76e8dd49\", \"key\": \"Fighting Fires\", \"title\": \"Fighting Fires [V4]\", \"artists\": \"(feat. Sunday Service Choir) (prod. BoogzDaBeast & FnZ)\", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"My Soul\"], \"description\": \"OG Filename: Fighting Fires - 11.12.19 Ye Short Edit_fixed\\nShort version of the original freestyle with something fixed from the prior version.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e1b0818c601bd94ef8bbb79d77352101\", \"url\": \"https://api.pillowcase.su/api/download/e1b0818c601bd94ef8bbb79d77352101\", \"size\": \"3.09 MB\", \"duration\": 65.62}", "aliases": ["Faithful", "Fightin Fire", "My Soul"], "size": "3.09 MB"}, {"id": "fighting-fires-23", "name": "Fighting Fires [V5]", "artists": ["Sunday Service Choir"], "producers": ["BoogzDaBeast", "FnZ"], "notes": "OG Filename: Fighting Fires - 11.12.19 Kaycyy REF 1\nKayCyy reference track. Leaked after a groupbuy.", "length": "196.8", "fileDate": 16357248, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/5bfd9f9d25d477489940856801394535", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5bfd9f9d25d477489940856801394535\", \"key\": \"Fighting Fires\", \"title\": \"Fighting Fires [V5]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. Sunday Service Choir) (prod. BoogzDaBeast & FnZ)\", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"My Soul\"], \"description\": \"OG Filename: Fighting Fires - 11.12.19 Kaycyy REF 1\\nKayCyy reference track. Leaked after a groupbuy.\", \"date\": 16357248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d62563c012c8b1049c585d0a2695c3e0\", \"url\": \"https://api.pillowcase.su/api/download/d62563c012c8b1049c585d0a2695c3e0\", \"size\": \"5.19 MB\", \"duration\": 196.8}", "aliases": ["Faithful", "Fightin Fire", "My Soul"], "size": "5.19 MB"}, {"id": "fightin-fire", "name": "Fightin Fire [V6]", "artists": ["Sunday Service Choir"], "producers": ["Wheezy", "BoogzDaBeast", "FnZ"], "notes": "OG Filename: fightin fire_Selected-1\nHas Wheezy production. Yes, this is real.", "length": "124.94", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/d624e8da98d2d92851ed30aaa7e73796", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d624e8da98d2d92851ed30aaa7e73796\", \"key\": \"Fightin Fire\", \"title\": \"Fightin Fire [V6]\", \"artists\": \"(feat. Sunday Service Choir) (prod. <PERSON><PERSON><PERSON>, BoogzDaBeast & FnZ)\", \"aliases\": [\"Faithful\", \"Fighting Fires\", \"My Soul\"], \"description\": \"OG Filename: fightin fire_Selected-1\\nHas Wheezy production. Yes, this is real.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5c8aafd7a9c637e3fbf9c0dba4b83e31\", \"url\": \"https://api.pillowcase.su/api/download/5c8aafd7a9c637e3fbf9c0dba4b83e31\", \"size\": \"4.04 MB\", \"duration\": 124.94}", "aliases": ["Faithful", "Fighting Fires", "My Soul"], "size": "4.04 MB"}, {"id": "freedom", "name": "Freedom [V4]", "artists": [], "producers": ["<PERSON>", "Edsclusive"], "notes": "OG Filename: Freedom - 11.16.19 Kaycyy REF\nKayCyy reference track. According to a source, this was considered for the Nebuchadnezzar opera.", "length": "169.74", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/e445d15f8ae567fa6a58e1bb544aee9a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e445d15f8ae567fa6a58e1bb544aee9a\", \"key\": \"Freedom\", \"title\": \"Freedom [V4]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON> & Eds<PERSON>)\", \"aliases\": [\"Heaven Calls\", \"When Heaven Calls\", \"Faith\"], \"description\": \"OG Filename: Freedom - 11.16.19 Kaycyy REF\\nKayCyy reference track. According to a source, this was considered for the Nebuchadnezzar opera.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"43e23911a6572b2a5118c9bfd530f295\", \"url\": \"https://api.pillowcase.su/api/download/43e23911a6572b2a5118c9bfd530f295\", \"size\": \"4.76 MB\", \"duration\": 169.74}", "aliases": ["Heaven Calls", "When Heaven Calls", "Faith"], "size": "4.76 MB"}, {"id": "future-bounce", "name": "Future Bounce [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 41 <PERSON> Bounce\nInitial freestyle, recorded alongside 100+ other freestyles, and back to back with \"Skurrrr\". <PERSON><PERSON> Kingsway sample \"Arena\" by WALLIS LANE.", "length": "150.49", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/3ad3f8cc8000385a3351aa9481fb5a47", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3ad3f8cc8000385a3351aa9481fb5a47\", \"key\": \"Future Bounce\", \"title\": \"Future Bounce [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Future\", \"Future Of Sound\", \"Future Sounds\", \"TELEKINESIS\", \"Ultrasounds\"], \"description\": \"OG Filename: 41 Future Bounce\\nInitial freestyle, recorded alongside 100+ other freestyles, and back to back with \\\"Skurrrr\\\". Samples Kingsway sample \\\"Arena\\\" by WALLIS LANE.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"8964caf76112d020ed50fe41a590b9a5\", \"url\": \"https://api.pillowcase.su/api/download/8964caf76112d020ed50fe41a590b9a5\", \"size\": \"4.45 MB\", \"duration\": 150.49}", "aliases": ["Future", "Future Of Sound", "Future Sounds", "TELEKINESIS", "Ultrasounds"], "size": "4.45 MB"}, {"id": "future-bounce-27", "name": "Future Bounce [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: <PERSON> Bounce - 11.06.19 <PERSON> reference track.", "length": "46.32", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/85eb83c734f7df1a82aeeaebf4510f56", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/85eb83c734f7df1a82aeeaebf4510f56\", \"key\": \"Future Bounce\", \"title\": \"Future Bounce [V2]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON>zDaBeast)\", \"aliases\": [\"Future\", \"Future Of Sound\", \"Future Sounds\", \"TELEKINESIS\", \"Ultrasounds\"], \"description\": \"OG Filename: Future Bounce - 11.06.19 Sean Ref\\nBig Sean reference track.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b5b602215a0165e4d5e4eaa4b51e9b59\", \"url\": \"https://api.pillowcase.su/api/download/b5b602215a0165e4d5e4eaa4b51e9b59\", \"size\": \"2.79 MB\", \"duration\": 46.32}", "aliases": ["Future", "Future Of Sound", "Future Sounds", "TELEKINESIS", "Ultrasounds"], "size": "2.79 MB"}, {"id": "future-bounce-28", "name": "Future Bounce [V3]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Future Bounce - 11.13.19 Kaycyy REF 1\nKayCyy confirmed that he was once on \"Future Sounds\", likely a November 2019 reference track. Features different drums not found in other versions.", "length": "150.48", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/908c01f36e0dd82b881bb68c3d3d7660", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/908c01f36e0dd82b881bb68c3d3d7660\", \"key\": \"Future Bounce\", \"title\": \"Future Bounce [V3]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>aB<PERSON><PERSON>)\", \"aliases\": [\"Future\", \"Future Of Sound\", \"Future Sounds\", \"TELEKINESIS\", \"Ultrasounds\"], \"description\": \"OG Filename: Future Bounce - 11.13.19 Kaycyy REF 1\\nKayCy<PERSON> confirmed that he was once on \\\"Future Sounds\\\", likely a November 2019 reference track. Features different drums not found in other versions.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7e2799691a981de12a4b927fc3355752\", \"url\": \"https://api.pillowcase.su/api/download/7e2799691a981de12a4b927fc3355752\", \"size\": \"4.45 MB\", \"duration\": 150.48}", "aliases": ["Future", "Future Of Sound", "Future Sounds", "TELEKINESIS", "Ultrasounds"], "size": "4.45 MB"}, {"id": "future-bounce-29", "name": "✨ Future Bounce [V4]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Future Bounce - 11.14.19 Ye comp\nVersion of with a rerecorded intro that was unused on later versions, for whatever reason, as well as <PERSON><PERSON><PERSON> beatboxing drum ideas for the song.", "length": "152.17", "fileDate": 16962048, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/cb5eff022a7aeb9a69b8db4895158327", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cb5eff022a7aeb9a69b8db4895158327\", \"key\": \"Future Bounce\", \"title\": \"\\u2728 Future Bounce [V4]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>B<PERSON><PERSON>)\", \"aliases\": [\"Future\", \"Future Of Sound\", \"Future Sounds\", \"TELEKINESIS\", \"Ultrasounds\"], \"description\": \"OG Filename: Future Bounce - 11.14.19 Ye comp\\nVersion of with a rerecorded intro that was unused on later versions, for whatever reason, as well as Kanye beatboxing drum ideas for the song.\", \"date\": 16962048, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ac6892240cdcb5c061237e5743e1997b\", \"url\": \"https://api.pillowcase.su/api/download/ac6892240cdcb5c061237e5743e1997b\", \"size\": \"4.48 MB\", \"duration\": 152.17}", "aliases": ["Future", "Future Of Sound", "Future Sounds", "TELEKINESIS", "Ultrasounds"], "size": "4.48 MB"}, {"id": "future-bounce-30", "name": "Future Bounce [V6]", "artists": [], "producers": ["Wheezy", "BoogzDaBeast"], "notes": "OG Filename: future bouce wheezy_Selected-1\nHas Wheezy production.", "length": "126.38", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/9655a0dcd0e5d1129cdf9b99b1c7110a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9655a0dcd0e5d1129cdf9b99b1c7110a\", \"key\": \"Future Bounce\", \"title\": \"Future Bounce [V6]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & BoogzDaBeast)\", \"aliases\": [\"Future\", \"Future Of Sound\", \"Future Sounds\", \"TELEKINESIS\", \"Ultrasounds\"], \"description\": \"OG Filename: future bouce wheezy_Selected-1\\nHas Wheezy production.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b14281473637105636208cd876ee2f30\", \"url\": \"https://api.pillowcase.su/api/download/b14281473637105636208cd876ee2f30\", \"size\": \"4.07 MB\", \"duration\": 126.38}", "aliases": ["Future", "Future Of Sound", "Future Sounds", "TELEKINESIS", "Ultrasounds"], "size": "4.07 MB"}, {"id": "future-bounce-31", "name": "Future Bounce [V7]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "Snippet of a Mid-November Victory ref, posted by Victory on Jan 10, 2025", "length": "37.25", "fileDate": 17364672, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/9dc5e8104477f3adbd114eb990ef6e55", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9dc5e8104477f3adbd114eb990ef6e55\", \"key\": \"Future Bounce\", \"title\": \"Future Bounce [V7]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON>zDaBeas<PERSON>)\", \"aliases\": [\"Future\", \"Future Of Sound\", \"Future Sounds\", \"TELEKINESIS\", \"Ultrasounds\"], \"description\": \"Snippet of a Mid-November Victory ref, posted by Victory on Jan 10, 2025\", \"date\": 17364672, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"a2731847e7bc3067d223985b0b066303\", \"url\": \"https://api.pillowcase.su/api/download/a2731847e7bc3067d223985b0b066303\", \"size\": \"2.64 MB\", \"duration\": 37.25}", "aliases": ["Future", "Future Of Sound", "Future Sounds", "TELEKINESIS", "Ultrasounds"], "size": "2.64 MB"}, {"id": "future-bounce-32", "name": "Future Bounce [V8]", "artists": [], "producers": ["BoogzDaBeast", "<PERSON>"], "notes": "OG Filename: Future Bounce (Boogz_FedeHorns) v1.2 83bpm\nHas added horns that would not be used for any other version than this one.", "length": "183.98", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/0b548555a036c2f6b517edfe7d70731c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0b548555a036c2f6b517edfe7d70731c\", \"key\": \"Future Bounce\", \"title\": \"Future Bounce [V8]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>a<PERSON><PERSON><PERSON> & Federico <PERSON>)\", \"aliases\": [\"Future\", \"Future Of Sound\", \"Future Sounds\", \"TELEKINESIS\", \"Ultrasounds\"], \"description\": \"OG Filename: Future Bounce (Boogz_FedeHorns) v1.2 83bpm\\nHas added horns that would not be used for any other version than this one.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"179ed4846caee094c2b90118a3f4a500\", \"url\": \"https://api.pillowcase.su/api/download/179ed4846caee094c2b90118a3f4a500\", \"size\": \"4.99 MB\", \"duration\": 183.98}", "aliases": ["Future", "Future Of Sound", "Future Sounds", "TELEKINESIS", "Ultrasounds"], "size": "4.99 MB"}, {"id": "he-gave-it-all", "name": "He Gave It All [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: <PERSON> Gave It All\nInitial freestyle, recorded alongside 100+ other freestyles. Was on a November 2019 tracklist. Original snippet leaked November 30th, 2022.", "length": "271.63", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/c3af3c9cbf7a733ec0b0ef9bf6fdd782", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c3af3c9cbf7a733ec0b0ef9bf6fdd782\", \"key\": \"He Gave It All\", \"title\": \"He Gave It All [V1]\", \"artists\": \"(prod. BoogzDaBeast)\", \"description\": \"OG Filename: He Gave It All\\nInitial freestyle, recorded alongside 100+ other freestyles. Was on a November 2019 tracklist. Original snippet leaked November 30th, 2022.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4d71d30b91305703b6823199891f323d\", \"url\": \"https://api.pillowcase.su/api/download/4d71d30b91305703b6823199891f323d\", \"size\": \"6.39 MB\", \"duration\": 271.63}", "aliases": [], "size": "6.39 MB"}, {"id": "he-gave-it-all-34", "name": "He Gave It All [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: <PERSON> Gave It All - 11.14.19 <PERSON><PERSON><PERSON> reference track, over a similar instrumental to the SSC version. <PERSON><PERSON> vocal effects. <PERSON><PERSON><PERSON><PERSON> said that <PERSON><PERSON><PERSON> told him to send him this version of the song for use in Donda 2 sessions, as seen in leaked texts.", "length": "106.14", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/065296b90fa710ba3e8f93e0a2ec8eb1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/065296b90fa710ba3e8f93e0a2ec8eb1\", \"key\": \"He Gave It All\", \"title\": \"He Gave It All [V2]\", \"artists\": \"(ref. <PERSON>) (prod. BoogzDaBeast)\", \"description\": \"OG Filename: He Gave It All - 11.14.19 Kaycyy\\nKayCyy reference track, over a similar instrumental to the SSC version. <PERSON>ks vocal effects. <PERSON><PERSON><PERSON><PERSON> said that <PERSON><PERSON><PERSON> told him to send him this version of the song for use in Donda 2 sessions, as seen in leaked texts.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"74f7e15592897bdabef50cbb0abc6416\", \"url\": \"https://api.pillowcase.su/api/download/74f7e15592897bdabef50cbb0abc6416\", \"size\": \"3.74 MB\", \"duration\": 106.14}", "aliases": [], "size": "3.74 MB"}, {"id": "he-gave-it-all-35", "name": "He Gave It All [V3]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: He Gave It All - 11.22.19 FSEDIT\nTuned and cutdown version of the initial freestyle.. OG WAV file leaked on October 2, 2023, which is a rebounce from Pro Tools dated June 13, 2023. Lossless leaked sometime later.", "length": "135.41", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/0ed3693c474f7b48bc163a43b83000d8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0ed3693c474f7b48bc163a43b83000d8\", \"key\": \"He Gave It All\", \"title\": \"He Gave It All [V3]\", \"artists\": \"(prod. BoogzDaBeast)\", \"description\": \"OG Filename: He Gave It All - 11.22.19 FSEDIT\\nTuned and cutdown version of the initial freestyle.. OG WAV file leaked on October 2, 2023, which is a rebounce from Pro Tools dated June 13, 2023. Lossless leaked sometime later.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"252e7fe662ad9615248720baf6fb0a49\", \"url\": \"https://api.pillowcase.su/api/download/252e7fe662ad9615248720baf6fb0a49\", \"size\": \"4.21 MB\", \"duration\": 135.41}", "aliases": [], "size": "4.21 MB"}, {"id": "head-down", "name": "Head Down [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 191108 Ye Free_4 Head Down - Ref\nInitial freestyle, recorded alongside 100+ other freestyles. Original snippet leaked November 21st, 2022.", "length": "286.88", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/52a146c62a46be6977196bc8cf601517", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/52a146c62a46be6977196bc8cf601517\", \"key\": \"Head Down\", \"title\": \"Head Down [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>aB<PERSON><PERSON>)\", \"aliases\": [\"Hold The Line\"], \"description\": \"OG Filename: 191108 Ye Free_4 Head Down - Ref\\nInitial freestyle, recorded alongside 100+ other freestyles. Original snippet leaked November 21st, 2022.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d10f8c14e9b9005864d8370199d97c03\", \"url\": \"https://api.pillowcase.su/api/download/d10f8c14e9b9005864d8370199d97c03\", \"size\": \"6.63 MB\", \"duration\": 286.88}", "aliases": ["Hold The Line"], "size": "6.63 MB"}, {"id": "hear-our-prayers", "name": "Hear Our Prayers [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "Mumble demo from late 2019. Leaked after a groupbuy.", "length": "189.34", "fileDate": 16223328, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/5d91549428bf67d3069c4067d9987540", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5d91549428bf67d3069c4067d9987540\", \"key\": \"Hear Our Prayers\", \"title\": \"Hear Our Prayers [V1]\", \"artists\": \"(prod. <PERSON><PERSON>z<PERSON>aBeast)\", \"description\": \"Mumble demo from late 2019. Leaked after a groupbuy.\", \"date\": 16223328, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0d053b9195d7ed12d8dc2aa7587ee08f\", \"url\": \"https://api.pillowcase.su/api/download/0d053b9195d7ed12d8dc2aa7587ee08f\", \"size\": \"5.07 MB\", \"duration\": 189.34}", "aliases": [], "size": "5.07 MB"}, {"id": "hear-our-prayers-38", "name": "Hear Our Prayers [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "KayCyy reference track. Leaked after a groupbuy.", "length": "135.17", "fileDate": 16223328, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/2eabd79782e44628359749e4fc016c3d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2eabd79782e44628359749e4fc016c3d\", \"key\": \"Hear Our Prayers\", \"title\": \"Hear Our Prayers [V2]\", \"artists\": \"(ref. <PERSON>) (prod. BoogzDaBeast)\", \"description\": \"KayCyy reference track. Leaked after a groupbuy.\", \"date\": 16223328, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8332d83a4ee31659dd819c28c00b1265\", \"url\": \"https://api.pillowcase.su/api/download/8332d83a4ee31659dd819c28c00b1265\", \"size\": \"4.21 MB\", \"duration\": 135.17}", "aliases": [], "size": "4.21 MB"}, {"id": "i-mvula", "name": "<PERSON><PERSON>mvula", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 191108 Ye Free_7 I<PERSON>m<PERSON><PERSON> - <PERSON><PERSON> dubbed the \"God's Country Christmas Carol\". Recorded alongside 100+ other freestyles in the month of November. Has mumble and singing. Interpolates \"The Carol Of The Bells\". Original snippet leaked November 28th, 2022.", "length": "276.43", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/b88f7fb2749087d52469988f7b56e10a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b88f7fb2749087d52469988f7b56e10a\", \"key\": \"I.mvula\", \"title\": \"I.mvula\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>aBeas<PERSON>)\", \"aliases\": [\"He Did The Most\"], \"description\": \"OG Filename: 191108 Ye Free_7 I.mvula - Ref\\nFreestyle dubbed the \\\"God's Country Christmas Carol\\\". Recorded alongside 100+ other freestyles in the month of November. Has mumble and singing. Interpolates \\\"The Carol Of The Bells\\\". Original snippet leaked November 28th, 2022.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7a25ebaf91c5286bdc6bd0d1a5dd1ac1\", \"url\": \"https://api.pillowcase.su/api/download/7a25ebaf91c5286bdc6bd0d1a5dd1ac1\", \"size\": \"6.47 MB\", \"duration\": 276.43}", "aliases": ["He Did The Most"], "size": "6.47 MB"}, {"id": "jah", "name": "<PERSON><PERSON>", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 55 <PERSON>ah <PERSON><PERSON> recorded on November 8th, along 100+ other freestyles. Original snippet leaked November 21st, 2022.", "length": "88.68", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/8424e9c584823a9bc1b99e5884941fbc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8424e9c584823a9bc1b99e5884941fbc\", \"key\": \"Jah\", \"title\": \"<PERSON>ah\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>DaBeas<PERSON>)\", \"aliases\": [\"Take Me\"], \"description\": \"OG Filename: 55 Jah Ref\\nFreestyle recorded on November 8th, along 100+ other freestyles. Original snippet leaked November 21st, 2022.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"db4f0ba03d4d435b4b70600ca3f00f00\", \"url\": \"https://api.pillowcase.su/api/download/db4f0ba03d4d435b4b70600ca3f00f00\", \"size\": \"3.46 MB\", \"duration\": 88.68}", "aliases": ["Take Me"], "size": "3.46 MB"}, {"id": "pco", "name": "PCO [V1]", "artists": [], "producers": ["FnZ", "Styalz <PERSON>"], "notes": "OG Filename: 52 PCO Ref\nInitial freestyle, recorded alongside 100+ other freestyles; likely the 52nd freestyle done that day. According to a source, this was considered for the Nebuchadnezzar opera.", "length": "44.59", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/9e31ab83e4b7a4e68d9afaa9e42def57", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9e31ab83e4b7a4e68d9afaa9e42def57\", \"key\": \"PCO\", \"title\": \"PCO [V1]\", \"artists\": \"(prod. FnZ & Styalz Fuego)\", \"aliases\": [\"Let The Spirit Go Wild\"], \"description\": \"OG Filename: 52 PCO Ref\\nInitial freestyle, recorded alongside 100+ other freestyles; likely the 52nd freestyle done that day. According to a source, this was considered for the Nebuchadnezzar opera.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"1a686d9473849d902a7fcb21cd4a6cc1\", \"url\": \"https://api.pillowcase.su/api/download/1a686d9473849d902a7fcb21cd4a6cc1\", \"size\": \"2.76 MB\", \"duration\": 44.59}", "aliases": ["Let The Spirit Go Wild"], "size": "2.76 MB"}, {"id": "lord-i-need-you", "name": "Lord I Need You [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 40 Lord I Need You\nSeen on multiple 2019 tracklists. Recorded alongside 100+ other freestyles. Includes lyrics from \"Don't Give Up\". Samples \"Make Me Over Again\" by <PERSON><PERSON>.", "length": "266.03", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/6efabcfb23b45f5e17dc33d053f489fb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6efabcfb23b45f5e17dc33d053f489fb\", \"key\": \"Lord I Need You\", \"title\": \"Lord I Need You [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Lord\", \"Lord I Need You Right Now\", \"Wrap Your Arms Around Me\"], \"description\": \"OG Filename: 40 Lord I Need You\\nSeen on multiple 2019 tracklists. Recorded alongside 100+ other freestyles. Includes lyrics from \\\"Don't Give Up\\\". <PERSON><PERSON> \\\"Make Me Over Again\\\" by <PERSON><PERSON>.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"cb5d2c0fde23dda681e754806474346c\", \"url\": \"https://api.pillowcase.su/api/download/cb5d2c0fde23dda681e754806474346c\", \"size\": \"6.3 MB\", \"duration\": 266.03}", "aliases": ["Lord", "Lord I Need You Right Now", "Wrap Your Arms Around Me"], "size": "6.3 MB"}, {"id": "lord-i-need-you-43", "name": "Lord I Need You [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Lord I Need You FS 2\nCutdown of the initial freestyle, featuring structure that matches later versions, and additional production.", "length": "104.83", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/526ddb697e4486cb842f95f98b8d7535", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/526ddb697e4486cb842f95f98b8d7535\", \"key\": \"Lord I Need You\", \"title\": \"Lord I Need You [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Lord\", \"Lord I Need You To Wrap Your Arms Around Me\", \"Lord I Need You Right Now\", \"Wrap Your Arms Around Me\"], \"description\": \"OG Filename: Lord I Need You FS 2\\nCutdown of the initial freestyle, featuring structure that matches later versions, and additional production.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"51b60b68e8608f61d95fd461a38510ca\", \"url\": \"https://api.pillowcase.su/api/download/51b60b68e8608f61d95fd461a38510ca\", \"size\": \"3.72 MB\", \"duration\": 104.83}", "aliases": ["Lord", "Lord I Need You To Wrap Your Arms Around Me", "Lord I Need You Right Now", "Wrap Your Arms Around Me"], "size": "3.72 MB"}, {"id": "lord-i-need-you-to-wrap-your-arms-around-me", "name": "Lord I Need You To Wrap Your Arms Around Me [V3]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Lord I Need You - 11.12.19 Kaycyy REF\nKayCyy reference track", "length": "104.81", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/cc2bb154c2b7cf0cf7413f3379cd8e26", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cc2bb154c2b7cf0cf7413f3379cd8e26\", \"key\": \"Lord I Need You To Wrap Your Arms Around Me\", \"title\": \"Lord I Need You To Wrap Your Arms Around Me [V3]\", \"artists\": \"(ref. <PERSON>) (prod. Bo<PERSON>z<PERSON>aBeast)\", \"aliases\": [\"Lord\", \"Lord I Need You\", \"Lord I Need You Right Now\", \"Wrap Your Arms Around Me\"], \"description\": \"OG Filename: Lord I Need You - 11.12.19 Kaycyy REF\\nKayCyy reference track\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"6d44a3de7715c2f2ba2451b5ec2bee3c\", \"url\": \"https://api.pillowcase.su/api/download/6d44a3de7715c2f2ba2451b5ec2bee3c\", \"size\": \"3.72 MB\", \"duration\": 104.81}", "aliases": ["Lord", "Lord I Need You", "Lord I Need You Right Now", "Wrap Your Arms Around Me"], "size": "3.72 MB"}, {"id": "lord-i-need-you-to-wrap-your-arms-around-me-45", "name": "Lord I Need You To Wrap Your Arms Around Me [V4]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Lord I Need You To Wrap Your Arms Around Me - 11.13.19 FS Ref\nSimilar to the other Francis Starlite arranges, suggesting that version may be from the same date. Rebounced as a fake WAV from Pro Tools on June 14, 2023.", "length": "104.73", "fileDate": 16962048, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/b760caa38ee25bc675b6ff3978ccaeb6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b760caa38ee25bc675b6ff3978ccaeb6\", \"key\": \"Lord I Need You To Wrap Your Arms Around Me\", \"title\": \"Lord I Need You To Wrap Your Arms Around Me [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Lord\", \"Lord I Need You To Wrap Your Arms Around Me\", \"Lord I Need You Right Now\", \"Wrap Your Arms Around Me\"], \"description\": \"OG Filename: Lord I Need You To Wrap Your Arms Around Me - 11.13.19 FS Ref\\nSimilar to the other Francis Starlite arranges, suggesting that version may be from the same date. Rebounced as a fake WAV from Pro Tools on June 14, 2023.\", \"date\": 16962048, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e336199e49209e159353bfac83e48b3c\", \"url\": \"https://api.pillowcase.su/api/download/e336199e49209e159353bfac83e48b3c\", \"size\": \"3.72 MB\", \"duration\": 104.73}", "aliases": ["Lord", "Lord I Need You To Wrap Your Arms Around Me", "Lord I Need You Right Now", "Wrap Your Arms Around Me"], "size": "3.72 MB"}, {"id": "lord-i-need-you-to-wrap-your-arms-around-me-46", "name": "Lord I Need You To Wrap Your Arms Around Me [V5]", "artists": [], "producers": ["Wheezy", "BoogzDaBeast"], "notes": "OG Filename: <PERSON>OR<PERSON> I NEED U TO WRAP WHEEZY'_Selected-1\nFirst version with Wheezy production, made the same time as his version of \"Wash Us In The Blood\".", "length": "177.53", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/ffc40226aa03a270b740afc13d7fec99", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ffc40226aa03a270b740afc13d7fec99\", \"key\": \"Lord I Need You To Wrap Your Arms Around Me\", \"title\": \"Lord I Need You To Wrap Your Arms Around Me [V5]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & BoogzDaBeast)\", \"aliases\": [\"Lord\", \"Lord I Need You\", \"Lord I Need You Right Now\", \"Wrap Your Arms Around Me\"], \"description\": \"OG Filename: LORD I NEED U TO WRAP WHEEZY'_Selected-1\\nFirst version with Wheezy production, made the same time as his version of \\\"Wash Us In The Blood\\\".\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"652ff39a877b6c154547a2d16263480c\", \"url\": \"https://api.pillowcase.su/api/download/652ff39a877b6c154547a2d16263480c\", \"size\": \"4.88 MB\", \"duration\": 177.53}", "aliases": ["Lord", "Lord I Need You", "Lord I Need You Right Now", "Wrap Your Arms Around Me"], "size": "4.88 MB"}, {"id": "lord-i-need-you-to-wrap-your-arms-around-me-47", "name": "Lord I Need You To Wrap Your Arms Around Me [V6]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "Consequence reference track, from November 2019.", "length": "9.26", "fileDate": 16531776, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/d397d6a058d9d46014921f39be631565", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d397d6a058d9d46014921f39be631565\", \"key\": \"Lord I Need You To Wrap Your Arms Around Me\", \"title\": \"Lord I Need You To Wrap Your Arms Around Me [V6]\", \"artists\": \"(ref. Consequence) (prod. <PERSON><PERSON><PERSON>aBeast)\", \"aliases\": [\"Lord\", \"Lord I Need You\", \"Lord I Need You Right Now\", \"Wrap Your Arms Around Me\"], \"description\": \"Consequence reference track, from November 2019.\", \"date\": 16531776, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3da5fdfcd138555a7f8d3020ee8ae019\", \"url\": \"https://api.pillowcase.su/api/download/3da5fdfcd138555a7f8d3020ee8ae019\", \"size\": \"2.19 MB\", \"duration\": 9.26}", "aliases": ["Lord", "Lord I Need You", "Lord I Need You Right Now", "Wrap Your Arms Around Me"], "size": "2.19 MB"}, {"id": "lord-i-need-you-48", "name": "Lord I Need You [V7]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: Lord I Need You -11.17.19 <PERSON>EF 2\n<PERSON> reference / idea for \"Lord I Need You\".", "length": "200.81", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/7c5f2ad9b4b9b4b62ad04faf8ac75cb9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7c5f2ad9b4b9b4b62ad04faf8ac75cb9\", \"key\": \"Lord I Need You\", \"title\": \"Lord I Need You [V7]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"Lord\", \"Lord I Need You To Wrap Your Arms Around Me\", \"Lord I Need You Right Now\", \"Wrap Your Arms Around Me\"], \"description\": \"OG Filename: Lord I Need You -11.17.19 <PERSON> REF 2\\nPeter Collins reference / idea for \\\"Lord I Need You\\\".\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"e8cfca15294d780bb6c09f2e88ae1057\", \"url\": \"https://api.pillowcase.su/api/download/e8cfca15294d780bb6c09f2e88ae1057\", \"size\": \"5.26 MB\", \"duration\": 200.81}", "aliases": ["Lord", "Lord I Need You To Wrap Your Arms Around Me", "Lord I Need You Right Now", "Wrap Your Arms Around Me"], "size": "5.26 MB"}, {"id": "northside-kings", "name": "Northside Kings", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 73 kml northside kings\nFreestyle recorded alongside 100+ other freestyles. Likely the 73rd freestyle recorded that day.", "length": "78.05", "fileDate": 16689888, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/61554e139c8623144b91cd05e671adb9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/61554e139c8623144b91cd05e671adb9\", \"key\": \"Northside Kings\", \"title\": \"Northside Kings\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>aBeas<PERSON>)\", \"description\": \"OG Filename: 73 kml northside kings\\nFreestyle recorded alongside 100+ other freestyles. Likely the 73rd freestyle recorded that day.\", \"date\": 16689888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"8caab98de6d752ad65825e1523dccc56\", \"url\": \"https://api.pillowcase.su/api/download/8caab98de6d752ad65825e1523dccc56\", \"size\": \"3.29 MB\", \"duration\": 78.05}", "aliases": [], "size": "3.29 MB"}, {"id": "our-king", "name": "Our King [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 29 Our King - Sketch for Choir\nRecorded alongside 100+ other freestyles. A Sunday Service Choir cover was performed at Nebuchadnezzar, and was later used for JESUS IS LORD, titled \"<PERSON> Is Troubled.\" The song then made it to DONDA 2020 tracklists.", "length": "216.31", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/a3cfae332208f9985d2d726845f4d0b7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a3cfae332208f9985d2d726845f4d0b7\", \"key\": \"Our King\", \"title\": \"Our King [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON> Is Troubled\"], \"description\": \"OG Filename: 29 Our King - Sketch for Choir\\nRecorded alongside 100+ other freestyles. A Sunday Service Choir cover was performed at Nebuchadnezzar, and was later used for JESUS IS LORD, titled \\\"Daniel Is Troubled.\\\" The song then made it to DONDA 2020 tracklists.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"737478e2e2c9d92cad368d604ae115f5\", \"url\": \"https://api.pillowcase.su/api/download/737478e2e2c9d92cad368d604ae115f5\", \"size\": \"5.51 MB\", \"duration\": 216.31}", "aliases": ["<PERSON>"], "size": "5.51 MB"}, {"id": "our-king-51", "name": "Our King (Trust) [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Our King(Trust) 11.21.19 Cons\nConsequence reference track with a <PERSON><PERSON><PERSON><PERSON> hook. Consequence's vocals are different from later versions.", "length": "188.13", "fileDate": 16531776, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/f3914a0c1f6304824f38db3a49d1cf81", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f3914a0c1f6304824f38db3a49d1cf81\", \"key\": \"Our King (Trust)\", \"title\": \"Our King (Trust) [V2]\", \"artists\": \"(ref. Consequence & KayCyy) (prod. BoogzDaBeast)\", \"aliases\": [\"Daniel Is Troubled\"], \"description\": \"OG Filename: Our King(Trust) 11.21.19 Cons\\nConsequence reference track with a Kay<PERSON><PERSON><PERSON> hook. Consequence's vocals are different from later versions.\", \"date\": 16531776, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"311e14c1a6b22d071d91994d81f8023f\", \"url\": \"https://api.pillowcase.su/api/download/311e14c1a6b22d071d91994d81f8023f\", \"size\": \"3.55 MB\", \"duration\": 188.13}", "aliases": ["<PERSON>"], "size": "3.55 MB"}, {"id": "the-legend-of-magis", "name": "🗑️ The Legend of Magis [V3]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: The Legend of Ma<PERSON>\nWeird, but real, bounce of <PERSON><PERSON><PERSON> yelling over his \"Our King\" freestyle before the Nebuchadnezzar opera.", "length": "57.89", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "Recording", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/d3bf07142a7cef7b7b5b141b5b47f712", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d3bf07142a7cef7b7b5b141b5b47f712\", \"key\": \"The Legend of Magis\", \"title\": \"\\ud83d\\uddd1\\ufe0f The Legend of Magis [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>aBeast)\", \"aliases\": [\"Our King\", \"Daniel Is Troubled\"], \"description\": \"OG Filename: The Legend of Magis\\nWeird, but real, bounce of <PERSON><PERSON><PERSON> yelling over his \\\"Our King\\\" freestyle before the Nebuchadnezzar opera.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"0f9ef27bdb18fa0f644b4dea4f4a6157\", \"url\": \"https://api.pillowcase.su/api/download/0f9ef27bdb18fa0f644b4dea4f4a6157\", \"size\": \"2.97 MB\", \"duration\": 57.89}", "aliases": ["Our King", "<PERSON>"], "size": "2.97 MB"}, {"id": "palm-springs", "name": "Palm Springs [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Palm Springs, 191104 Ye Free_71 A The Way\nInitial freestyle, recorded alongside 100+ other freestyles.", "length": "204.2", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/4b7b4cf5ce36e533dd32b6fc37de6ba5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4b7b4cf5ce36e533dd32b6fc37de6ba5\", \"key\": \"Palm Springs\", \"title\": \"Palm Springs [V1]\", \"artists\": \"(prod. <PERSON>ogzDaBeas<PERSON>)\", \"description\": \"OG Filename: Palm Springs, 191104 Ye Free_71 A The Way\\nInitial freestyle, recorded alongside 100+ other freestyles.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6aec0452161aa24e4bdec23ffa33bec3\", \"url\": \"https://api.pillowcase.su/api/download/6aec0452161aa24e4bdec23ffa33bec3\", \"size\": \"5.31 MB\", \"duration\": 204.2}", "aliases": [], "size": "5.31 MB"}, {"id": "palm-springs-54", "name": "Palm Springs [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filenames: Palm Springs - 11.08.19 Chorus &\nPalm Springs - 11.08.19 Chorus - F - 139\nSeen on multiple God's Country tracklists. Recorded alongside 100+ other freestyles.", "length": "138.24", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/85786f1d40134c61d7cb0749f5f626f8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/85786f1d40134c61d7cb0749f5f626f8\", \"key\": \"Palm Springs\", \"title\": \"Palm Springs [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>aBeas<PERSON>)\", \"description\": \"OG Filenames: Palm Springs - 11.08.19 Chorus &\\nPalm Springs - 11.08.19 Chorus - F - 139\\nSeen on multiple God's Country tracklists. Recorded alongside 100+ other freestyles.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ca80c9e75261ba78d8ba30ace9c88dae\", \"url\": \"https://api.pillowcase.su/api/download/ca80c9e75261ba78d8ba30ace9c88dae\", \"size\": \"4.26 MB\", \"duration\": 138.24}", "aliases": [], "size": "4.26 MB"}, {"id": "palm-springs-55", "name": "Palm Springs [V4]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Palm Springs - 11.11.19 Tuned\nTuned version of <PERSON><PERSON><PERSON>'s version of \"Palm Springs\".", "length": "138.13", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/1faa2006983241cdf74ac269c6d020f9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1faa2006983241cdf74ac269c6d020f9\", \"key\": \"Palm Springs\", \"title\": \"Palm Springs [V4]\", \"artists\": \"(prod. <PERSON>ogzDaBeas<PERSON>)\", \"description\": \"OG Filename: Palm Springs - 11.11.19 Tuned\\nTuned version of <PERSON><PERSON><PERSON>'s version of \\\"Palm Springs\\\".\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1391c6e4b7afc9e8339217a1027f05c7\", \"url\": \"https://api.pillowcase.su/api/download/1391c6e4b7afc9e8339217a1027f05c7\", \"size\": \"4.25 MB\", \"duration\": 138.13}", "aliases": [], "size": "4.25 MB"}, {"id": "palm-springs-56", "name": "Palm Springs [V6]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Palm Springs - Dream REF\nThe-Dream reference track. From November 2019 - according to <PERSON><PERSON><PERSON><PERSON>, it was the first reference track for the song. Leaked after a Joe<PERSON>y.", "length": "150.5", "fileDate": 16753824, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/9ed86dbe0b567e984a601bf95b8bd2f1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9ed86dbe0b567e984a601bf95b8bd2f1\", \"key\": \"Palm Springs\", \"title\": \"Palm Springs [V6]\", \"artists\": \"(ref. The-<PERSON>) (prod. BoogzDaBeast)\", \"description\": \"OG Filename: Palm Springs - Dream REF\\nThe-Dream reference track. From November 2019 - according to KayCyy, it was the first reference track for the song. Leaked after a Joebuy.\", \"date\": 16753824, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7db8f7cfdcfb2899b254919f8eab4b88\", \"url\": \"https://api.pillowcase.su/api/download/7db8f7cfdcfb2899b254919f8eab4b88\", \"size\": \"4.45 MB\", \"duration\": 150.5}", "aliases": [], "size": "4.45 MB"}, {"id": "palm-springs-57", "name": "Palm Springs [V11]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Palm Springs 11.21.19 <PERSON><PERSON><PERSON> ref\n<PERSON><PERSON><PERSON><PERSON> \"Palm Springs\" reference track. Metadata says KayCyy and Consequence, but it's been confirmed by sellers that Consequence didn't record, this was just an export copy/paste error.", "length": "207.31", "fileDate": 16577568, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/c524a9d480300611bf3a8dc3c45d4a91", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c524a9d480300611bf3a8dc3c45d4a91\", \"key\": \"Palm Springs\", \"title\": \"Palm Springs [V11]\", \"artists\": \"(ref. <PERSON>) (prod. BoogzDaBeast)\", \"description\": \"OG Filename: Palm Springs 11.21.19 Kacyy ref\\nKayCyy \\\"Palm Springs\\\" reference track. Metadata says Kay<PERSON>yy and Consequence, but it's been confirmed by sellers that Consequence didn't record, this was just an export copy/paste error.\", \"date\": 16577568, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ed939815c27651009de98967e6a01c8a\", \"url\": \"https://api.pillowcase.su/api/download/ed939815c27651009de98967e6a01c8a\", \"size\": \"5.36 MB\", \"duration\": 207.31}", "aliases": [], "size": "5.36 MB"}, {"id": "prayed-up", "name": "Prayed Up [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "Initial freestyle.", "length": "251.4", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/7b53519ba504e9a9e1d86dfbd5abb90c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7b53519ba504e9a9e1d86dfbd5abb90c\", \"key\": \"Prayed Up\", \"title\": \"Prayed Up [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>aBeas<PERSON>)\", \"aliases\": [\"Laid Up\"], \"description\": \"Initial freestyle.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6c6d2fd3d6929be5066cea0422b05c76\", \"url\": \"https://api.pillowcase.su/api/download/6c6d2fd3d6929be5066cea0422b05c76\", \"size\": \"6.07 MB\", \"duration\": 251.4}", "aliases": ["Laid Up"], "size": "6.07 MB"}, {"id": "prayed-up-59", "name": "Prayed Up [V2]", "artists": [], "producers": ["???", "BoogzDaBeast"], "notes": "<PERSON> God's Country-era throwaway, featured on a November 2019 tracklist. Was originally being sold by TheSource in 2020. Has drums from an unknown producer.", "length": "258.19", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/115bdc60fa57aa7a4888c47604d09afe", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/115bdc60fa57aa7a4888c47604d09afe\", \"key\": \"Prayed Up\", \"title\": \"Prayed Up [V2]\", \"artists\": \"(prod. ??? & BoogzDaBeast)\", \"aliases\": [\"Laid Up\"], \"description\": \"Rough God's Country-era throwaway, featured on a November 2019 tracklist. Was originally being sold by TheSource in 2020. Has drums from an unknown producer.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1ec664e09bc84f8908158bec7bcbbe97\", \"url\": \"https://api.pillowcase.su/api/download/1ec664e09bc84f8908158bec7bcbbe97\", \"size\": \"6.18 MB\", \"duration\": 258.19}", "aliases": ["Laid Up"], "size": "6.18 MB"}, {"id": "prayed-up-60", "name": "Prayed Up [V3]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: prayed up fs arrange\nShortened file, with new production and no drums.", "length": "111.88", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/779af23bbc0f32daec1b38095d9c5e69", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/779af23bbc0f32daec1b38095d9c5e69\", \"key\": \"Prayed Up\", \"title\": \"Prayed Up [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>aBeas<PERSON>)\", \"aliases\": [\"Laid Up\"], \"description\": \"OG Filename: prayed up fs arrange\\nShortened file, with new production and no drums.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2c8f7ecb90914b3a7fe97cf004b871a6\", \"url\": \"https://api.pillowcase.su/api/download/2c8f7ecb90914b3a7fe97cf004b871a6\", \"size\": \"3.83 MB\", \"duration\": 111.88}", "aliases": ["Laid Up"], "size": "3.83 MB"}, {"id": "rise-up", "name": "Rise Up", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG FIlename: 62 <PERSON> Up Ref\nFreestyle recorded on November 8th, along 100+ other freestyles. Was considered for the Nebuchadnezzar opera. Original snippet leaked exactly a year before the full thing.", "length": "71", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/5e8cfbf66ed482e08be903a9f4d45a9a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5e8cfbf66ed482e08be903a9f4d45a9a\", \"key\": \"Rise Up\", \"title\": \"Rise Up\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>aB<PERSON><PERSON>)\", \"description\": \"OG FIlename: 62 Rise Up Ref\\nFreestyle recorded on November 8th, along 100+ other freestyles. Was considered for the Nebuchadnezzar opera. Original snippet leaked exactly a year before the full thing.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f1368e9cfdab4882bd53a89a353e757d\", \"url\": \"https://api.pillowcase.su/api/download/f1368e9cfdab4882bd53a89a353e757d\", \"size\": \"3.18 MB\", \"duration\": 71}", "aliases": [], "size": "3.18 MB"}, {"id": "shotta", "name": "Shotta [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 11 <PERSON><PERSON>f\nFreestyle recorded on November 8th, along 100+ other freestyles. Likely the 11th freestyle recorded that day. Was given to <PERSON><PERSON><PERSON> for It's Almost Dry.", "length": "117.12", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/36248cd2ba8cb611029a98ad1847f353", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/36248cd2ba8cb611029a98ad1847f353\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON>B<PERSON><PERSON>)\", \"aliases\": [\"Hear Me Clearly\"], \"description\": \"OG Filename: 11 Shotta Ref\\nFreestyle recorded on November 8th, along 100+ other freestyles. Likely the 11th freestyle recorded that day. Was given to <PERSON><PERSON><PERSON> for It's Almost Dry.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"db7000b4023a044eb887b50d8a2d05ea\", \"url\": \"https://api.pillowcase.su/api/download/db7000b4023a044eb887b50d8a2d05ea\", \"size\": \"3.92 MB\", \"duration\": 117.12}", "aliases": ["Hear Me Clearly"], "size": "3.92 MB"}, {"id": "sinner", "name": "Sinner [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 57 Sinners - ref\nOriginal freestyle, with the vocals off-beat. Leaked on 12/17/22. OG lossless file leaked sometime later.", "length": "62.21", "fileDate": 16712352, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/cdf03a5c9f25abee360de9fcee37b3fe", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cdf03a5c9f25abee360de9fcee37b3fe\", \"key\": \"Sinner\", \"title\": \"Sinner [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>DaBeas<PERSON>)\", \"aliases\": [\"Sinners\"], \"description\": \"OG Filename: 57 Sinners - ref\\nOriginal freestyle, with the vocals off-beat. Leaked on 12/17/22. OG lossless file leaked sometime later.\", \"date\": 16712352, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f2fedcbf0f0ecf1df49ad186cdd3cffa\", \"url\": \"https://api.pillowcase.su/api/download/f2fedcbf0f0ecf1df49ad186cdd3cffa\", \"size\": \"3.04 MB\", \"duration\": 62.21}", "aliases": ["Sinners"], "size": "3.04 MB"}, {"id": "sinner-64", "name": "Sinner [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Sinners - 11.13.19 Kaycyy REF 1\nKayCyy reference track.", "length": "62.47", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/9622487c1c5041810c1e2b3a834558ad", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9622487c1c5041810c1e2b3a834558ad\", \"key\": \"Sinner\", \"title\": \"Sinner [V2]\", \"artists\": \"(ref. <PERSON>) (prod. BoogzDaBeast)\", \"aliases\": [\"Sinners\"], \"description\": \"OG Filename: Sinners - 11.13.19 Kaycyy REF 1\\nKayCyy reference track.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7df1dafc8ea016085ab19afb397b32ae\", \"url\": \"https://api.pillowcase.su/api/download/7df1dafc8ea016085ab19afb397b32ae\", \"size\": \"3.04 MB\", \"duration\": 62.47}", "aliases": ["Sinners"], "size": "3.04 MB"}, {"id": "sinner-65", "name": "Sinner [V3]", "artists": [], "producers": ["Wheezy", "BoogzDaBeast"], "notes": "OG Filename: SINNER_Master\nInitial Wheezy-produced version. Is slightly longer, with better mixing than the later Wheezy-produced version.", "length": "182.41", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/1ca2efae0495e01a8df2e6c1eab839ef", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1ca2efae0495e01a8df2e6c1eab839ef\", \"key\": \"Sinner\", \"title\": \"Sinner [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & BoogzDaBeast)\", \"aliases\": [\"Sinners\"], \"description\": \"OG Filename: SINNER_Master\\nInitial Wheezy-produced version. Is slightly longer, with better mixing than the later Wheezy-produced version.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6b34c8dc72e7f47f027eaf8c63f1163f\", \"url\": \"https://api.pillowcase.su/api/download/6b34c8dc72e7f47f027eaf8c63f1163f\", \"size\": \"4.96 MB\", \"duration\": 182.41}", "aliases": ["Sinners"], "size": "4.96 MB"}, {"id": "skuu<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "Initial freestyle. Recorded on alongside 100+ other freestyles, back to back with \"Future Sounds\".", "length": "73.3", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/87526d6e180bb37c2f5d4e76fa5b32a4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/87526d6e180bb37c2f5d4e76fa5b32a4\", \"key\": \"Skuuurrruurrr\", \"title\": \"Skuuurrruurrr [V1]\", \"artists\": \"(prod. BoogzDaBeast)\", \"aliases\": [\"Navy\", \"Pull Up Like Skuuurrr\", \"Skurr\", \"Skurrrr\", \"Skuuur\", \"Skuuurrr\"], \"description\": \"Initial freestyle. Recorded on alongside 100+ other freestyles, back to back with \\\"Future Sounds\\\".\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"16060cd81b5b29dc8bdac36cfb2b930d\", \"url\": \"https://api.pillowcase.su/api/download/16060cd81b5b29dc8bdac36cfb2b930d\", \"size\": \"3.22 MB\", \"duration\": 73.3}", "aliases": ["Navy", "Pull Up Like <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "size": "3.22 MB"}, {"id": "skuu<PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Skuuurrr - 11.12.19 Kaycyy REF\nKayCyy \"Skuuurrruurrr\" reference track. KayCyy played this in a VC. Full VC recording originally leaked October 2nd, 2022, with the song leaking in full HQ the next day.", "length": "92.59", "fileDate": 17040672, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/a58e45268e97405db27b9e7f092d1c09", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a58e45268e97405db27b9e7f092d1c09\", \"key\": \"Skuuurrr\", \"title\": \"<PERSON>kuuurrr [V2]\", \"artists\": \"(ref. <PERSON>) (prod. BoogzDaBeast)\", \"aliases\": [\"Navy\", \"Pull Up Like Skuuurrr\", \"<PERSON>kurr\", \"<PERSON>kurrrr\", \"<PERSON>kuuur\", \"Skuuurrruurrr\"], \"description\": \"OG Filename: Skuuurrr - 11.12.19 Kaycyy REF\\nKayCyy \\\"Skuuurrruurrr\\\" reference track. KayCyy played this in a VC. Full VC recording originally leaked October 2nd, 2022, with the song leaking in full HQ the next day.\", \"date\": 17040672, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7fe735a7a8de0c459813c7c5a5864a93\", \"url\": \"https://api.pillowcase.su/api/download/7fe735a7a8de0c459813c7c5a5864a93\", \"size\": \"3.53 MB\", \"duration\": 92.59}", "aliases": ["Navy", "Pull Up Like <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "size": "3.53 MB"}, {"id": "spotlight", "name": "✨ Spotlight [V10]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Spotlight - 11.20.19 KCwithint\nKayCyy reference track. KayCyy said in a leaked December 2022 text, that it \"could be [used on] Donda 2\". Instrumental snippet originally leaked in May 2022.", "length": "58.64", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/8fa05bcfa7b9dd9a4fe2522ee96b56eb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8fa05bcfa7b9dd9a4fe2522ee96b56eb\", \"key\": \"Spotlight\", \"title\": \"\\u2728 Spotlight [V10]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. BoogzDaBeast)\", \"description\": \"OG Filename: Spotlight - 11.20.19 KCwithint\\nKayCyy reference track. KayCyy said in a leaked December 2022 text, that it \\\"could be [used on] Donda 2\\\". Instrumental snippet originally leaked in May 2022.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"09cf1ea13d993719b16cc8ae56d0972d\", \"url\": \"https://api.pillowcase.su/api/download/09cf1ea13d993719b16cc8ae56d0972d\", \"size\": \"2.98 MB\", \"duration\": 58.64}", "aliases": [], "size": "2.98 MB"}, {"id": "jesus", "name": "<PERSON> (Speed Up) [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 51 <PERSON> (Speed Up)\nShort freestyle, recorded on November 4th, along 100+ other freestyles. Original snippet leaked November 21st, 2022.", "length": "27.53", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/441a79b4f345fd607037aecacd885b87", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/441a79b4f345fd607037aecacd885b87\", \"key\": \"<PERSON> (Speed Up)\", \"title\": \"<PERSON> (<PERSON> Up) [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>DaBeast)\", \"aliases\": [\"Jesus\"], \"description\": \"OG Filename: 51 Jesus (Speed Up)\\nShort freestyle, recorded on November 4th, along 100+ other freestyles. Original snippet leaked November 21st, 2022.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"b35b3b1dcc7a9fe3425e46c40e221095\", \"url\": \"https://api.pillowcase.su/api/download/b35b3b1dcc7a9fe3425e46c40e221095\", \"size\": \"2.48 MB\", \"duration\": 27.53}", "aliases": ["<PERSON>"], "size": "2.48 MB"}, {"id": "jesus-70", "name": "<PERSON> [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: <PERSON>\nSimilar to version above, but has a cut at 19 seconds of pure silence.", "length": "27.59", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/b1788aa0ad80cff560ebd2f5b7e92fc7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b1788aa0ad80cff560ebd2f5b7e92fc7\", \"key\": \"<PERSON>\", \"title\": \"<PERSON> [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>aB<PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON>\\nSimilar to version above, but has a cut at 19 seconds of pure silence.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5cc09942129469c5a00ae75e0a7efbf6\", \"url\": \"https://api.pillowcase.su/api/download/5cc09942129469c5a00ae75e0a7efbf6\", \"size\": \"2.49 MB\", \"duration\": 27.59}", "aliases": [], "size": "2.49 MB"}, {"id": "jesus-71", "name": "<PERSON> [V3]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Jesus - fs edit\nSimilar to version above, however at 19 seconds it is now just acapella.", "length": "27.59", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/55e4e2c7e18217d94ba9e69ffdc788e7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/55e4e2c7e18217d94ba9e69ffdc788e7\", \"key\": \"<PERSON>\", \"title\": \"<PERSON> [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>aB<PERSON><PERSON>)\", \"description\": \"OG Filename: Jesus - fs edit\\nSimilar to version above, however at 19 seconds it is now just acapella.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"acdb2bd9177a04da95a12cc9bb8ab139\", \"url\": \"https://api.pillowcase.su/api/download/acdb2bd9177a04da95a12cc9bb8ab139\", \"size\": \"2.49 MB\", \"duration\": 27.59}", "aliases": [], "size": "2.49 MB"}, {"id": "jesus-72", "name": "<PERSON> [V4]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: <PERSON> fsedit\nHas no change at 19 seconds and has different mixing compared to the first version.", "length": "27.59", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/e8a0e145280cc125d2b9459e3d8ada88", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e8a0e145280cc125d2b9459e3d8ada88\", \"key\": \"<PERSON>\", \"title\": \"<PERSON> [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>aB<PERSON><PERSON>)\", \"description\": \"OG Filename: Jesus fsedit\\nHas no change at 19 seconds and has different mixing compared to the first version.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6fdccf1fc616379afa05a5c43f9a9495\", \"url\": \"https://api.pillowcase.su/api/download/6fdccf1fc616379afa05a5c43f9a9495\", \"size\": \"2.49 MB\", \"duration\": 27.59}", "aliases": [], "size": "2.49 MB"}, {"id": "tulsa", "name": "Tulsa [V1]", "artists": [], "producers": ["BoogzDaBeast", "FnZ"], "notes": "OG Filename: 20 Tulsa\nOriginal freestyle, likely recorded in the November 4th session, as suggested by the \"Add Woo\" description on the filename for V2. Samples \"FNZ bell loop freaked 133 F#min\".", "length": "289.41", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/c82a50397c7f873f2e1122fa3a27e1f3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c82a50397c7f873f2e1122fa3a27e1f3\", \"key\": \"Tulsa\", \"title\": \"Tulsa [V1]\", \"artists\": \"(prod. BoogzDaBeast & FnZ)\", \"description\": \"OG Filename: 20 Tulsa\\nOriginal freestyle, likely recorded in the November 4th session, as suggested by the \\\"Add Woo\\\" description on the filename for V2. Sam<PERSON> \\\"FNZ bell loop freaked 133 F#min\\\".\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"b39401d81c5cc24a78fc8e93101857b1\", \"url\": \"https://api.pillowcase.su/api/download/b39401d81c5cc24a78fc8e93101857b1\", \"size\": \"6.67 MB\", \"duration\": 289.41}", "aliases": [], "size": "6.67 MB"}, {"id": "tulsa-74", "name": "✨ Tulsa [V2]", "artists": [], "producers": ["BoogzDaBeast", "FnZ"], "notes": "OG Filename: Tulsa - 11.09.19 Add Woo louder\nVersion on the DONDA New Years tracklist. Cut-down of the early freestyle, containing mostly finished lines but some mumble. Song is about death row inmate <PERSON>, who is still awaiting trial for a 26 year old crime.", "length": "139.94", "fileDate": 16330464, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/d46a4857551a8de54aae34903b571b5f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d46a4857551a8de54aae34903b571b5f\", \"key\": \"Tulsa\", \"title\": \"\\u2728 Tulsa [V2]\", \"artists\": \"(prod. BoogzDaBeast & FnZ)\", \"description\": \"OG Filename: Tulsa - 11.09.19 Add Woo louder\\nVersion on the DONDA New Years tracklist. Cut-down of the early freestyle, containing mostly finished lines but some mumble. Song is about death row inmate <PERSON>, who is still awaiting trial for a 26 year old crime.\", \"date\": 16330464, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"6c89d86a3693ef42c9c404207b10dfd8\", \"url\": \"https://api.pillowcase.su/api/download/6c89d86a3693ef42c9c404207b10dfd8\", \"size\": \"4.28 MB\", \"duration\": 139.94}", "aliases": [], "size": "4.28 MB"}, {"id": "tulsa-75", "name": "Tulsa [V3]", "artists": [], "producers": ["BoogzDaBeast", "FnZ"], "notes": "OG Filename: Tulsa - 11.14.19 Kaycyy REF\nVersion on God's Country tracklists. Kay<PERSON><PERSON>y \"Tulsa\" reference track. Leaked in KayCyy Hub on 5/29/22.", "length": "139.78", "fileDate": 16537824, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/f0652936b1ae675011bcc6ab0ea55fef", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f0652936b1ae675011bcc6ab0ea55fef\", \"key\": \"Tulsa\", \"title\": \"Tulsa [V3]\", \"artists\": \"(ref. <PERSON>) (prod. BoogzDaBeast & FnZ)\", \"description\": \"OG Filename: Tulsa - 11.14.19 Kaycyy REF\\nVersion on God's Country tracklists. KayCyy \\\"Tulsa\\\" reference track. Leaked in KayCyy Hub on 5/29/22.\", \"date\": 16537824, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4e015bb99ebb679964432b27f03d36a6\", \"url\": \"https://api.pillowcase.su/api/download/4e015bb99ebb679964432b27f03d36a6\", \"size\": \"4.28 MB\", \"duration\": 139.78}", "aliases": [], "size": "4.28 MB"}, {"id": "tulsa-76", "name": "Tulsa [V4]", "artists": [], "producers": ["BoogzDaBeast", "FnZ"], "notes": "OG Filename: Tulsa - 11.16.19 Kaycyy Comp\nFound 5/11/24. Seems to have just better mixing.", "length": "139.78", "fileDate": 16867008, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/3efbb3561cc6bd541a87c5f4d5cabea3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3efbb3561cc6bd541a87c5f4d5cabea3\", \"key\": \"Tulsa\", \"title\": \"Tulsa [V4]\", \"artists\": \"(ref. <PERSON>) (prod. BoogzDaBeast & FnZ)\", \"description\": \"OG Filename: Tulsa - 11.16.19 Kaycyy Comp\\nFound 5/11/24. Seems to have just better mixing.\", \"date\": 16867008, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"cee53a7505f6bbc31462a1d593dd0486\", \"url\": \"https://api.pillowcase.su/api/download/cee53a7505f6bbc31462a1d593dd0486\", \"size\": \"4.28 MB\", \"duration\": 139.78}", "aliases": [], "size": "4.28 MB"}, {"id": "aesop", "name": "Aesop [V1]", "artists": [], "producers": ["BoogzDaBeast", "FnZ"], "notes": "OG Filenames: 191108 Ye Free_12 Aesop - Ref & \nWash Us In The Blood - no drums\nInitial \"Wash Us In The Blood\" freestyle, said by <PERSON><PERSON><PERSON><PERSON>a<PERSON><PERSON><PERSON> to have been recorded alongside 100+ other freestyles. Initially reported in the GQ May 2020 issue interview with <PERSON><PERSON><PERSON> talking about his next album. A live version was performed at <PERSON><PERSON><PERSON>'s first opera. In an interview, A.G. said he sent the sample loop to Boogz a few months before the track was made.", "length": "179.86", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/e8ddcd6ad6e8b53fe3e5d37f8437c6e1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e8ddcd6ad6e8b53fe3e5d37f8437c6e1\", \"key\": \"Aesop\", \"title\": \"Aesop [V1]\", \"artists\": \"(prod. BoogzDaBeast & FnZ)\", \"aliases\": [\"Wash Us In The Blood\", \"Washed in the Blood\", \"Wash Us In The Blood Of Jesus\"], \"description\": \"OG Filenames: 191108 Ye Free_12 Aesop - Ref & \\nWash Us In The Blood - no drums\\nInitial \\\"Wash Us In The Blood\\\" freestyle, said by <PERSON><PERSON><PERSON><PERSON>a<PERSON><PERSON><PERSON> to have been recorded alongside 100+ other freestyles. Initially reported in the GQ May 2020 issue interview with <PERSON><PERSON><PERSON> talking about his next album. A live version was performed at <PERSON><PERSON><PERSON>'s first opera. In an interview, A<PERSON>G. said he sent the sample loop to Boogz a few months before the track was made.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7e1c1312508c1e44ce1153a5128efea2\", \"url\": \"https://api.pillowcase.su/api/download/7e1c1312508c1e44ce1153a5128efea2\", \"size\": \"4.92 MB\", \"duration\": 179.86}", "aliases": ["Wash Us In The Blood", "Washed in the Blood", "<PERSON>h Us In The Blood Of Jesus"], "size": "4.92 MB"}, {"id": "wash-us-in-the-blood", "name": "Wash Us In The Blood [V2]", "artists": [], "producers": ["BoogzDaBeast", "FnZ", "Israel Boyd"], "notes": "Version of the freestyle with production from <PERSON>. Still contains a lot of mumble.", "length": "196.3", "fileDate": 15927840, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/3446559ddfaa54655b2edf098b280c07", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3446559ddfaa54655b2edf098b280c07\", \"key\": \"Wash Us In The Blood\", \"title\": \"Wash Us In The Blood [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, FnZ & Israel Boyd)\", \"aliases\": [\"Aesop\", \"Washed in the Blood\", \"Wash Us In The Blood Of Jesus\"], \"description\": \"Version of the freestyle with production from <PERSON> <PERSON>. Still contains a lot of mumble.\", \"date\": 15927840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3f4806fec2a9c6d7366a40703beaf814\", \"url\": \"https://api.pillowcase.su/api/download/3f4806fec2a9c6d7366a40703beaf814\", \"size\": \"5.18 MB\", \"duration\": 196.3}", "aliases": ["<PERSON><PERSON><PERSON>", "Washed in the Blood", "<PERSON>h Us In The Blood Of Jesus"], "size": "5.18 MB"}, {"id": "wash-us-in-the-blood-79", "name": "Wash Us In The Blood [V3]", "artists": [], "producers": ["BoogzDaBeast", "FnZ"], "notes": "OG Filename: Wash Us In The Blood - 11.18.19 Kaycyy REF\nInitial reference track for \"Wash Us In The Blood\" done by <PERSON><PERSON><PERSON><PERSON>.", "length": "97.94", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/64a0e9091ecc75226ebe5f9ced0d336e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/64a0e9091ecc75226ebe5f9ced0d336e\", \"key\": \"Wash Us In The Blood\", \"title\": \"Wash Us In The Blood [V3]\", \"artists\": \"(ref. <PERSON>) (prod. BoogzDaBeast & FnZ)\", \"aliases\": [\"Aesop\", \"Washed in the Blood\", \"Wash Us In The Blood Of Jesus\"], \"description\": \"OG Filename: Wash Us In The Blood - 11.18.19 Kaycyy REF\\nInitial reference track for \\\"Wash Us In The Blood\\\" done by KayCyy.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4a665740ab8be8e1254c907e1647b824\", \"url\": \"https://api.pillowcase.su/api/download/4a665740ab8be8e1254c907e1647b824\", \"size\": \"3.61 MB\", \"duration\": 97.94}", "aliases": ["<PERSON><PERSON><PERSON>", "Washed in the Blood", "<PERSON>h Us In The Blood Of Jesus"], "size": "3.61 MB"}, {"id": "wash-us-in-the-blood-80", "name": "<PERSON>h Us In The Blood [V4]", "artists": [], "producers": ["BoogzDaBeast", "FnZ"], "notes": "A second reference track for \"Wash Us In The Blood\" done by <PERSON><PERSON><PERSON><PERSON>. Has new lyrics not seen in the previous reference track.", "length": "13.04", "fileDate": 16187904, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/65220df24fdf34af3b430ef2d520a50e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/65220df24fdf34af3b430ef2d520a50e\", \"key\": \"Wash Us In The Blood\", \"title\": \"Wash Us In The Blood [V4]\", \"artists\": \"(ref. <PERSON>) (prod. BoogzDaBeast & FnZ)\", \"aliases\": [\"Aesop\", \"Washed in the Blood\", \"Wash Us In The Blood Of Jesus\"], \"description\": \"A second reference track for \\\"Wash Us In The Blood\\\" done by <PERSON><PERSON><PERSON><PERSON>. Has new lyrics not seen in the previous reference track.\", \"date\": 16187904, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5752f07ccff3bfa12f28986197f24ef8\", \"url\": \"https://api.pillowcase.su/api/download/5752f07ccff3bfa12f28986197f24ef8\", \"size\": \"2.25 MB\", \"duration\": 13.04}", "aliases": ["<PERSON><PERSON><PERSON>", "Washed in the Blood", "<PERSON>h Us In The Blood Of Jesus"], "size": "2.25 MB"}, {"id": "wash-us-in-the-blood-81", "name": "Wash Us In The Blood [V5]", "artists": [], "producers": ["Wheezy", "BoogzDaBeast", "FnZ"], "notes": "OG Filename: WASH US IN BLOOD wheezy\nWheezy version of \"Wash Us In The Blood,\" different to the July 2020 version, which imported production from this version.", "length": "179.91", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/47aa618ddeb8d3d2d922a40eddb5be2f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/47aa618ddeb8d3d2d922a40eddb5be2f\", \"key\": \"Wash Us In The Blood\", \"title\": \"Wash Us In The Blood [V5]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, BoogzDaBeast & FnZ)\", \"aliases\": [\"Aesop\", \"Washed in the Blood\", \"Wash Us In The Blood Of Jesus\"], \"description\": \"OG Filename: WASH US IN BLOOD wheezy\\nWheezy version of \\\"Wash Us In The Blood,\\\" different to the July 2020 version, which imported production from this version.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"07183af8e74056834e1802eef1456752\", \"url\": \"https://api.pillowcase.su/api/download/07183af8e74056834e1802eef1456752\", \"size\": \"4.92 MB\", \"duration\": 179.91}", "aliases": ["<PERSON><PERSON><PERSON>", "Washed in the Blood", "<PERSON>h Us In The Blood Of Jesus"], "size": "4.92 MB"}, {"id": "wash-us-in-the-blood-82", "name": "<PERSON>h Us In The Blood [V6]", "artists": [], "producers": ["Wheezy", "BoogzDaBeast", "FnZ"], "notes": "OG Filename: WASH US IN BLOOD_Selected-1\nAnother Wheezy version of \"Wash Us In The Blood\" with slight differences.", "length": "179.93", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/f5e1e08894d8726cdce7c79c57610d1d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f5e1e08894d8726cdce7c79c57610d1d\", \"key\": \"Wash Us In The Blood\", \"title\": \"Wash Us In The Blood [V6]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, BoogzDaBeast & FnZ)\", \"aliases\": [\"Aesop\", \"Washed in the Blood\", \"Wash Us In The Blood Of Jesus\"], \"description\": \"OG Filename: WASH US IN BLOOD_Selected-1\\nAnother Wheezy version of \\\"Wash Us In The Blood\\\" with slight differences.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4ac84c01ba64c946eadf8a7071cbc98d\", \"url\": \"https://api.pillowcase.su/api/download/4ac84c01ba64c946eadf8a7071cbc98d\", \"size\": \"4.92 MB\", \"duration\": 179.93}", "aliases": ["<PERSON><PERSON><PERSON>", "Washed in the Blood", "<PERSON>h Us In The Blood Of Jesus"], "size": "4.92 MB"}, {"id": "xtc", "name": "XTC", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 191108 Ye Free_10 XTC - Ref\nFreestyle made on November 8th, 2019.", "length": "104.12", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/bb6500b21d5fca12a3b2254522f3628b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bb6500b21d5fca12a3b2254522f3628b\", \"key\": \"XTC\", \"title\": \"XTC\", \"artists\": \"(prod. BoogzDaBeast)\", \"description\": \"OG Filename: 191108 Ye Free_10 XTC - Ref\\nFreestyle made on November 8th, 2019.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"32746f799d2703af8e76cd9ab131dc8d\", \"url\": \"https://api.pillowcase.su/api/download/32746f799d2703af8e76cd9ab131dc8d\", \"size\": \"3.71 MB\", \"duration\": 104.12}", "aliases": [], "size": "3.71 MB"}, {"id": "", "name": "???", "artists": [], "producers": [], "notes": "72nd freestyle made on November 4th, 2019. Snippet of the instrumental included in a January 2020 copy of <PERSON> Is Lord.", "length": "5.24", "fileDate": 16985376, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/f58b3e4517689d4c778abef2ac93f2d1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f58b3e4517689d4c778abef2ac93f2d1\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"72\"], \"description\": \"72nd freestyle made on November 4th, 2019. Snippet of the instrumental included in a January 2020 copy of Jesus Is Lord.\", \"date\": 16985376, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3e12cb3dd93c55561d01f9f2d9d312ef\", \"url\": \"https://api.pillowcase.su/api/download/3e12cb3dd93c55561d01f9f2d9d312ef\", \"size\": \"2.13 MB\", \"duration\": 5.24}", "aliases": ["72"], "size": "2.13 MB"}, {"id": "12-000-acres-85", "name": "12,000 Acres [V8]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 12000 Acres - 01.09.20 <PERSON><PERSON><PERSON>\n\"12,000 Acres\" reference done by <PERSON><PERSON><PERSON>, with him recording a spoken word interlude/skit, most likely to be overlayed onto the song. Someone else can be heard talking within the bleed, but the identity of who's talking is unknown.", "length": "90.38", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/8665ed7402fea4ceeef643068a4d9b68", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8665ed7402fea4ceeef643068a4d9b68\", \"key\": \"12,000 Acres\", \"title\": \"12,000 Acres [V8]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. BoogzDaBeast)\", \"description\": \"OG Filename: 12000 Acres - 01.09.20 <PERSON><PERSON><PERSON>\\n\\\"12,000 Acres\\\" reference done by <PERSON><PERSON><PERSON>, with him recording a spoken word interlude/skit, most likely to be overlayed onto the song. Someone else can be heard talking within the bleed, but the identity of who's talking is unknown.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3919a19944ab66f1c808531023070fb5\", \"url\": \"https://api.pillowcase.su/api/download/3919a19944ab66f1c808531023070fb5\", \"size\": \"3.49 MB\", \"duration\": 90.38}", "aliases": [], "size": "3.49 MB"}, {"id": "12-000-acres-86", "name": "⭐ 12,000 Acres [V9]", "artists": [], "producers": ["BoogzDaBeast", "FXXXXY"], "notes": "OG Filename: 12000 ACRES ROUGH MIX\nReference done by Future associate and former-Freebandz Gang signee FXXXXY. Contains wildly different production compared to other versions, alongside some of <PERSON>'s original freestyle vocals. Exact date of this version is unknown.", "length": "174.29", "fileDate": 16943904, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/dd7220c0664ae8c2b6a85315ce36280c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dd7220c0664ae8c2b6a85315ce36280c\", \"key\": \"12,000 Acres\", \"title\": \"\\u2b50 12,000 Acres [V9]\", \"artists\": \"(ref. FXXXXY) (prod. BoogzDaBeast & FXXXXY)\", \"description\": \"OG Filename: 12000 ACRES ROUGH MIX\\nReference done by Future associate and former-Freebandz Gang signee FXXXXY. Contains wildly different production compared to other versions, alongside some of <PERSON>'s original freestyle vocals. Exact date of this version is unknown.\", \"date\": 16943904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0944e0ceadc0f1a565a1389ff2b48b41\", \"url\": \"https://api.pillowcase.su/api/download/0944e0ceadc0f1a565a1389ff2b48b41\", \"size\": \"4.83 MB\", \"duration\": 174.29}", "aliases": [], "size": "4.83 MB"}, {"id": "24-hours", "name": "24 Hours [V2]", "artists": ["Kay<PERSON>y<PERSON>"], "producers": [], "notes": "OG Filename: 24 Hours - 02.12.20 <PERSON><PERSON>y Ref\nVersion of \"24\" with a more developed instrumental and better <PERSON><PERSON><PERSON><PERSON> vocals. Does not include any of the drums featured on later versions. Original snippet leaked August 23, 2021. On Christmas 2021, <PERSON><PERSON><PERSON><PERSON> posted a snippet of this song, saying he would leak it if he sold 100 hats - though it later leaked after a groupbuy. Encoded with same filename as 1.29.20 ref, so it might be very similar.", "length": "174.22", "fileDate": 16496352, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/392daed9f0e2ff39cf19167df4b87d6f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/392daed9f0e2ff39cf19167df4b87d6f\", \"key\": \"24 Hours\", \"title\": \"24 Hours [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"24 Candles\", \"24\"], \"description\": \"OG Filename: 24 Hours - 02.12.20 Kay<PERSON>y Ref\\nVersion of \\\"24\\\" with a more developed instrumental and better <PERSON><PERSON><PERSON><PERSON> vocals. Does not include any of the drums featured on later versions. Original snippet leaked August 23, 2021. On Christmas 2021, <PERSON><PERSON><PERSON><PERSON> posted a snippet of this song, saying he would leak it if he sold 100 hats - though it later leaked after a groupbuy. Encoded with same filename as 1.29.20 ref, so it might be very similar.\", \"date\": 16496352, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"40ad88ef8bf4b947b626b40942096978\", \"url\": \"https://api.pillowcase.su/api/download/40ad88ef8bf4b947b626b40942096978\", \"size\": \"4.83 MB\", \"duration\": 174.22}", "aliases": ["24 Candles", "24"], "size": "4.83 MB"}, {"id": "24-hours-88", "name": "24 Hours [V4]", "artists": ["Kay<PERSON>y<PERSON>"], "producers": ["808 Melo"], "notes": "808 Melo version of \"24\". Has different drums compared to the later versions. Snippet posted on 808 Melo's Twitter on September 9 after saying \"Bring back my drums 😒\" in response to a dondas<PERSON> post. Leaked after a groupbuy.", "length": "174.29", "fileDate": 16502400, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/4ee8faf791f9d40541cf8535e2692072", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4ee8faf791f9d40541cf8535e2692072\", \"key\": \"24 Hours\", \"title\": \"24 Hours [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. 808 Melo)\", \"aliases\": [\"24 Candles\", \"24\"], \"description\": \"808 Melo version of \\\"24\\\". Has different drums compared to the later versions. <PERSON>nippe<PERSON> posted on 808 Melo's Twitter on September 9 after saying \\\"Bring back my drums \\ud83d\\ude12\\\" in response to a dondasplace post. Leaked after a groupbuy.\", \"date\": 16502400, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c93ced38d01a3da8f40926ceec23fae3\", \"url\": \"https://api.pillowcase.su/api/download/c93ced38d01a3da8f40926ceec23fae3\", \"size\": \"4.83 MB\", \"duration\": 174.29}", "aliases": ["24 Candles", "24"], "size": "4.83 MB"}, {"id": "all-of-me", "name": "All Of Me [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 191218 <PERSON><PERSON><PERSON> Free_15 - All Of Me 83.5\nInitial freestyle for \"All Of Me\" made in December 2019.", "length": "52.95", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/1759cf03da2c559e9c4197ed55b04234", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1759cf03da2c559e9c4197ed55b04234\", \"key\": \"All Of Me\", \"title\": \"All Of Me [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>aBeas<PERSON>)\", \"description\": \"OG Filename: 191218 Cabin Free_15 - All Of Me 83.5\\nInitial freestyle for \\\"All Of Me\\\" made in December 2019.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6e75bc428d47884ad7839843562af265\", \"url\": \"https://api.pillowcase.su/api/download/6e75bc428d47884ad7839843562af265\", \"size\": \"2.89 MB\", \"duration\": 52.95}", "aliases": [], "size": "2.89 MB"}, {"id": "all-of-me-90", "name": "All Of Me [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: All Of Me\nLater version of \"All Of Me\", with an added minute of open verse on the end of the song, and additional production, alongside better mixing.", "length": "114.97", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/8fa209301f3e9db9a0af3ce0c9c6004f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8fa209301f3e9db9a0af3ce0c9c6004f\", \"key\": \"All Of Me\", \"title\": \"All Of Me [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>aBeas<PERSON>)\", \"description\": \"OG Filename: All Of Me\\nLater version of \\\"All Of Me\\\", with an added minute of open verse on the end of the song, and additional production, alongside better mixing.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"57317d280080bdb272a942ea3d0f8ef9\", \"url\": \"https://api.pillowcase.su/api/download/57317d280080bdb272a942ea3d0f8ef9\", \"size\": \"3.88 MB\", \"duration\": 114.97}", "aliases": [], "size": "3.88 MB"}, {"id": "cecilia-lost-little-things", "name": "<PERSON> Lost Little Things [V2]", "artists": ["Abstract Mindstate"], "producers": ["BoogzDaBeast"], "notes": "OG Filename: <PERSON> Lost Little Thing - 02.20.20 - New Gre Verse\nVersion of \"Cecilia Lost Little Things\" with added Abstract Mindstate verses. Unknown if this version was their song or not.", "length": "199.13", "fileDate": 16954272, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/2bd2d87560c393acfa073856e0c29990", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2bd2d87560c393acfa073856e0c29990\", \"key\": \"<PERSON> Lost Little Things\", \"title\": \"<PERSON> Lost Little Things [V2]\", \"artists\": \"(feat. Abstract Mindstate) (prod. <PERSON><PERSON><PERSON><PERSON>aB<PERSON><PERSON>)\", \"aliases\": [\"The Brenda Song\"], \"description\": \"OG Filename: Cecilia Lost Little Thing - 02.20.20 - New Gre Verse\\nVersion of \\\"Cecilia Lost Little Things\\\" with added Abstract Mindstate verses. Unknown if this version was their song or not.\", \"date\": 16954272, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"51c10b0a849071fb23cdd5083fce5e53\", \"url\": \"https://api.pillowcase.su/api/download/51c10b0a849071fb23cdd5083fce5e53\", \"size\": \"5.23 MB\", \"duration\": 199.13}", "aliases": ["The <PERSON>"], "size": "5.23 MB"}, {"id": "codeine", "name": "Codeine [V1]", "artists": [], "producers": [], "notes": "OG Filename: Codeine - Ye Freestyle 02.08.20\nFreestyle for \"Codeine\" from February 2020.", "length": "186.24", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/aa1f6c1adbff4dd517087a723369a1ea", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/aa1f6c1adbff4dd517087a723369a1ea\", \"key\": \"Codeine\", \"title\": \"Codeine [V1]\", \"description\": \"OG Filename: Codeine - Ye Freestyle 02.08.20\\nFreestyle for \\\"Codeine\\\" from February 2020.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d1fa989e4798eaf17b6488d33d655f4d\", \"url\": \"https://api.pillowcase.su/api/download/d1fa989e4798eaf17b6488d33d655f4d\", \"size\": \"5.02 MB\", \"duration\": 186.24}", "aliases": [], "size": "5.02 MB"}, {"id": "cops-flash-put-em-up", "name": "✨ Cops Flash Put Em Up", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "OG Filename: <PERSON><PERSON> Flash Put Em Up\nUsed on a July tracklist, and likely \"RN\" on early DONDA tracklists. Mostly mumble. Was referred to by fans as \"Boomers Search for Moonrocks\" when it was first heard in the YZYTV documentary. Leaked after a groupbuy.", "length": "250.94", "fileDate": 16543008, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/026eb76ec798fdcdd6512d4246fb1df8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/026eb76ec798fdcdd6512d4246fb1df8\", \"key\": \"Cops Flash Put Em Up\", \"title\": \"\\u2728 Cops Flash Put Em Up\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Run\"], \"description\": \"OG Filename: Cops Flash Put Em Up\\nUsed on a July tracklist, and likely \\\"RN\\\" on early DONDA tracklists. Mostly mumble. Was referred to by fans as \\\"Boomers Search for Moonrocks\\\" when it was first heard in the YZYTV documentary. Leaked after a groupbuy.\", \"date\": 16543008, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8b206aa41b816c39b4f83feab332d1cb\", \"url\": \"https://api.pillowcase.su/api/download/8b206aa41b816c39b4f83feab332d1cb\", \"size\": \"6.06 MB\", \"duration\": 250.94}", "aliases": ["Run"], "size": "6.06 MB"}, {"id": "devils-ivy", "name": "Devils Ivy [V2]", "artists": [], "producers": ["BoogzDaBeast", "FnZ"], "notes": "OG Filename: Devils Ivy 137 -01.28.20 <PERSON><PERSON><PERSON> Ref\n<PERSON> reference for \"Thank God I Breathe\", later given to <PERSON> for UTOPIA. <PERSON> re-recorded the first verse and used the flow from the second on the version released on UTOPIA. Lacks the drop on the released version. Leaked on Twitter after being sent to multiple Ye-related accounts by <PERSON><PERSON><PERSON><PERSON> himself before the OG file dropped. Samples \"looperman-l-2379402-0342384-creeper-bellz\".", "length": "105.2", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/8d9cf35f683903db85e6c77e89167bed", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8d9cf35f683903db85e6c77e89167bed\", \"key\": \"Devils Ivy\", \"title\": \"Devils Ivy [V2]\", \"artists\": \"(ref. <PERSON>) (prod. BoogzDaBeast & FnZ)\", \"aliases\": [\"Thank God I Breathe\", \"THANK GOD\"], \"description\": \"OG Filename: Devils Ivy 137 -01.28.20 Kaycyy Ref\\nKayCyy reference for \\\"Thank God I Breathe\\\", later given to <PERSON> for UTOPIA. <PERSON> re-recorded the first verse and used the flow from the second on the version released on UTOPIA. Lacks the drop on the released version. Leaked on Twitter after being sent to multiple Ye-related accounts by <PERSON><PERSON><PERSON><PERSON> himself before the OG file dropped. Samples \\\"looperman-l-2379402-0342384-creeper-bellz\\\".\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4485daeea6aefa8c13cf2705c4d47c99\", \"url\": \"https://api.pillowcase.su/api/download/4485daeea6aefa8c13cf2705c4d47c99\", \"size\": \"3.73 MB\", \"duration\": 105.2}", "aliases": ["Thank God I Breathe", "THANK GOD"], "size": "3.73 MB"}, {"id": "eternal-life-95", "name": "Eternal Life [V9]", "artists": [], "producers": ["BoogzDaBeast", "808 Melo", "Axl Beats"], "notes": "Axl Beats drill version of \"Eternal Life\". Originally believed to be called \"Ambitious\", due to confusion over perceived lyrics mentioned in the GQ interview. Snippet was by previewed by Axl Beats in February 2020. Beat leaked as a bonus to the Donda 2 bundle Joebuy.", "length": "10.71", "fileDate": 16954272, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/e0ec3db84510bffbb01bc788d6969985", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e0ec3db84510bffbb01bc788d6969985\", \"key\": \"Eternal Life\", \"title\": \"Eternal Life [V9]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 808 <PERSON>o & Axl Beats)\", \"aliases\": [\"Ambitious\", \"Devil Weak\", \"The Devil Is Weak\", \"We Got Eternal Life\"], \"description\": \"Axl Beats drill version of \\\"Eternal Life\\\". Originally believed to be called \\\"Ambitious\\\", due to confusion over perceived lyrics mentioned in the GQ interview. Snippet was by previewed by Axl Beats in February 2020. Beat leaked as a bonus to the Donda 2 bundle Joebuy.\", \"date\": 16954272, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"35665172c945ee850f37bb41aa7a5d44\", \"url\": \"https://api.pillowcase.su/api/download/35665172c945ee850f37bb41aa7a5d44\", \"size\": \"2.21 MB\", \"duration\": 10.71}", "aliases": ["Ambitious", "Devil Weak", "The Devil Is Weak", "We Got Eternal Life"], "size": "2.21 MB"}, {"id": "eternal-life-96", "name": "Eternal Life [V9]", "artists": [], "producers": ["BoogzDaBeast", "808 Melo", "Axl Beats"], "notes": "Axl Beats drill version of \"Eternal Life\". Originally believed to be called \"Ambitious\", due to confusion over perceived lyrics mentioned in the GQ interview. Snippet was by previewed by Axl Beats in February 2020. Beat leaked as a bonus to the Donda 2 bundle Joebuy.", "length": "158.93", "fileDate": 16954272, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/f071a1460a3ae81094c15c8fd799867b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f071a1460a3ae81094c15c8fd799867b\", \"key\": \"Eternal Life\", \"title\": \"Eternal Life [V9]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 808 <PERSON>o & Axl Beats)\", \"aliases\": [\"Ambitious\", \"Devil Weak\", \"The Devil Is Weak\", \"We Got Eternal Life\"], \"description\": \"Axl Beats drill version of \\\"Eternal Life\\\". Originally believed to be called \\\"Ambitious\\\", due to confusion over perceived lyrics mentioned in the GQ interview. Snippet was by previewed by Axl Beats in February 2020. Beat leaked as a bonus to the Donda 2 bundle Joebuy.\", \"date\": 16954272, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3bb75c9887ee76f957b371bda5bd794d\", \"url\": \"https://api.pillowcase.su/api/download/3bb75c9887ee76f957b371bda5bd794d\", \"size\": \"4.59 MB\", \"duration\": 158.93}", "aliases": ["Ambitious", "Devil Weak", "The Devil Is Weak", "We Got Eternal Life"], "size": "4.59 MB"}, {"id": "eternal-life-97", "name": "Eternal Life [V10]", "artists": ["Big Sean", "The-Dream"], "producers": ["BoogzDaBeast", "FnZ", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: Eternal Life - 04.12.20 Ye Edit - <PERSON><PERSON><PERSON> Drums w Dream\nVersion of \"Eternal Life\" with <PERSON> and <PERSON>-<PERSON>. <PERSON><PERSON><PERSON>'s production likely came from earlier in 2020, when he was in the studio with <PERSON><PERSON><PERSON>. Leaked after it was played in voice chat several times with various different effects.", "length": "175.51", "fileDate": 16693344, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/03766fd15bac223fc78c9c06ca159870", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/03766fd15bac223fc78c9c06ca159870\", \"key\": \"Eternal Life\", \"title\": \"Eternal Life [V10]\", \"artists\": \"(feat. <PERSON> Sean & The-Dream) (prod. <PERSON><PERSON><PERSON><PERSON>, Fn<PERSON> & S<PERSON><PERSON>)\", \"aliases\": [\"Ambitious\", \"Devil Weak\", \"The Devil Is Weak\", \"We Got Eternal Life\"], \"description\": \"OG Filename: Eternal Life - 04.12.20 Ye Edit - <PERSON>wae Drums w Dream\\nVersion of \\\"Eternal Life\\\" with <PERSON> and The-<PERSON>. <PERSON><PERSON><PERSON>'s production likely came from earlier in 2020, when he was in the studio with <PERSON><PERSON><PERSON>. Leaked after it was played in voice chat several times with various different effects.\", \"date\": 16693344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3165878e9732817abe9fab4869e05af4\", \"url\": \"https://api.pillowcase.su/api/download/3165878e9732817abe9fab4869e05af4\", \"size\": \"4.85 MB\", \"duration\": 175.51}", "aliases": ["Ambitious", "Devil Weak", "The Devil Is Weak", "We Got Eternal Life"], "size": "4.85 MB"}, {"id": "eternal-life-98", "name": "Eternal Life [V12]", "artists": ["Big Sean"], "producers": ["BoogzDaBeast", "RONNY J"], "notes": "OG Filename: We Got Eternal Life X RJ V2\nRONNY J produced version of \"Eternal Life\". Unknown when it was made.", "length": "158.96", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/82c314968fb478211da7acfc3443e2df", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/82c314968fb478211da7acfc3443e2df\", \"key\": \"Eternal Life\", \"title\": \"Eternal Life [V12]\", \"artists\": \"(feat. <PERSON>) (prod. BoogzDaBeast & RONNY J)\", \"aliases\": [\"Ambitious\", \"Devil Weak\", \"The Devil Is Weak\", \"We Got Eternal Life\"], \"description\": \"OG Filename: We Got Eternal Life X RJ V2\\nRONNY J produced version of \\\"Eternal Life\\\". Unknown when it was made.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6089496f2b5d53f0c711e252afb3a977\", \"url\": \"https://api.pillowcase.su/api/download/6089496f2b5d53f0c711e252afb3a977\", \"size\": \"4.59 MB\", \"duration\": 158.96}", "aliases": ["Ambitious", "Devil Weak", "The Devil Is Weak", "We Got Eternal Life"], "size": "4.59 MB"}, {"id": "everybody-goes", "name": "🗑️ Everybody Goes [V1]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 191218 <PERSON><PERSON><PERSON>_23_<PERSON><PERSON><PERSON> - <PERSON> Goes 73\nInitial freestyle. Samples \"Dziwny Jest Ten Świat\" by <PERSON><PERSON><PERSON> and interpolates \"Burning Down the House\" by Talking Heads.", "length": "134.98", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/b0936f9f337a7bc3ae00f4e8d516fe81", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b0936f9f337a7bc3ae00f4e8d516fe81\", \"key\": \"Everybody Goes\", \"title\": \"\\ud83d\\uddd1\\ufe0f Everybody Goes [V1]\", \"artists\": \"(prod. BoogzDaBeast)\", \"description\": \"OG Filename: 191218 Cabin Free_23_Bo<PERSON>z - Everybody Goes 73\\nInitial freestyle. Samples \\\"Dziwny Jest Ten \\u015awiat\\\" by <PERSON><PERSON>\\u0142aw Niemen and interpolates \\\"Burning Down the House\\\" by Talking Heads.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"4c56a1acb0dbb6969b6b8cc2ece8855f\", \"url\": \"https://api.pillowcase.su/api/download/4c56a1acb0dbb6969b6b8cc2ece8855f\", \"size\": \"4.2 MB\", \"duration\": 134.98}", "aliases": [], "size": "4.2 MB"}, {"id": "everybody-goes-100", "name": "Everybody Goes [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 191218 <PERSON><PERSON><PERSON>_23_<PERSON><PERSON><PERSON> - <PERSON> Goes 73 <PERSON> x <PERSON><PERSON> 01.10.20 &\n<PERSON> Goes 73 <PERSON> x <PERSON><PERSON> 01.10.20\nLater version of \"Everybody Goes\" with vocals from <PERSON><PERSON> & Tate, likely meant to be a reference for <PERSON>.", "length": "138.08", "fileDate": 16985376, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/dc251a4d4df532563a980d64fc7f11af", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dc251a4d4df532563a980d64fc7f11af\", \"key\": \"Everybody Goes\", \"title\": \"Everybody Goes [V2]\", \"artists\": \"(ref. <PERSON><PERSON> & <PERSON>) (prod. <PERSON>ogzDaBeast)\", \"description\": \"OG Filename: 191218 Cabin Free_23_<PERSON><PERSON>z - Everybody Goes 73 Tate x <PERSON>zzy 01.10.20 &\\nEverybody Goes 73 Tate x Jozzy 01.10.20\\nLater version of \\\"Everybody Goes\\\" with vocals from <PERSON><PERSON> & <PERSON>, likely meant to be a reference for <PERSON>.\", \"date\": 16985376, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"66ffa099a1a55c9a59d6b0789cd215f8\", \"url\": \"https://api.pillowcase.su/api/download/66ffa099a1a55c9a59d6b0789cd215f8\", \"size\": \"4.25 MB\", \"duration\": 138.08}", "aliases": [], "size": "4.25 MB"}, {"id": "everything", "name": "Everything [V1]", "artists": [], "producers": [], "notes": "Solo mumble <PERSON><PERSON><PERSON> version with vocal effects. Samples \"What A Friend\" by St. Luke C.O.G.I.C. Youth Choir.", "length": "217.94", "fileDate": 16624224, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/d656814f37e86c164779fb75fc7e6ecf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d656814f37e86c164779fb75fc7e6ecf\", \"key\": \"Everything\", \"title\": \"Everything [V1]\", \"description\": \"Solo mumble Kanye version with vocal effects. Samples \\\"What A Friend\\\" by St. Luke C.O.G.I.C. Youth Choir.\", \"date\": 16624224, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"46f08021167eba2ff4a8e56f61d9c5ac\", \"url\": \"https://api.pillowcase.su/api/download/46f08021167eba2ff4a8e56f61d9c5ac\", \"size\": \"5.53 MB\", \"duration\": 217.94}", "aliases": [], "size": "5.53 MB"}, {"id": "everything-102", "name": "Everything [V2]", "artists": [], "producers": [], "notes": "Version used on God's Country and DONDA tracklists. <PERSON> vocals (with no effects) and <PERSON><PERSON> reference vocals. Possibly from the same session as \"Better Move\", which took place on April 13th, 2020.", "length": "224.3", "fileDate": 16624224, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/08fc0e8b45d2bca8df3fd15e59316891", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/08fc0e8b45d2bca8df3fd15e59316891\", \"key\": \"Everything\", \"title\": \"Everything [V2]\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"description\": \"Version used on God's Country and DONDA tracklists. <PERSON> vocals (with no effects) and <PERSON><PERSON> reference vocals. Possibly from the same session as \\\"Better Move\\\", which took place on April 13th, 2020.\", \"date\": 16624224, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7c8bed2165acf65e9c7d9ee4731251f7\", \"url\": \"https://api.pillowcase.su/api/download/7c8bed2165acf65e9c7d9ee4731251f7\", \"size\": \"5.63 MB\", \"duration\": 224.3}", "aliases": [], "size": "5.63 MB"}, {"id": "everything-103", "name": "Everything [V3]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "OG Filename: Everything 76 - 02.25.20 <PERSON>, 3. Everything 76 - <PERSON><PERSON> Ref - 02.25.20 Ye <PERSON> and <PERSON><PERSON> \"Everything\" reference track, used on a February 27th playlist. <PERSON><PERSON> remains a feature on the hook.", "length": "224.3", "fileDate": 16624224, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/cf30691f8f91d0e77fab63af2b950ece", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cf30691f8f91d0e77fab63af2b950ece\", \"key\": \"Everything\", \"title\": \"Everything [V3]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON>)\", \"description\": \"OG Filename: Everything 76 - 02.25.20 Ye Edit, 3. Everything 76 - <PERSON><PERSON> Ref - 02.25.20 Ye Edit\\nKayCyy and <PERSON><PERSON> \\\"Everything\\\" reference track, used on a February 27th playlist. <PERSON><PERSON> remains a feature on the hook.\", \"date\": 16624224, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e80abaa9712bd33a9f568b480c97e11d\", \"url\": \"https://api.pillowcase.su/api/download/e80abaa9712bd33a9f568b480c97e11d\", \"size\": \"5.63 MB\", \"duration\": 224.3}", "aliases": [], "size": "5.63 MB"}, {"id": "everything-104", "name": "Everything [V4]", "artists": [], "producers": [], "notes": "Sunday Service Choir cover of \"Everything\".", "length": "58.84", "fileDate": 16624224, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/033068fba2b018d43dc6612233349df4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/033068fba2b018d43dc6612233349df4\", \"key\": \"Everything\", \"title\": \"Everything [V4]\", \"artists\": \"(ref. Sunday Service Choir)\", \"description\": \"Sunday Service Choir cover of \\\"Everything\\\".\", \"date\": 16624224, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"93d764f8f040a2a1843b830dd3e90590\", \"url\": \"https://api.pillowcase.su/api/download/93d764f8f040a2a1843b830dd3e90590\", \"size\": \"2.52 MB\", \"duration\": 58.84}", "aliases": [], "size": "2.52 MB"}, {"id": "fighting-fires-105", "name": "Fighting Fires [V7]", "artists": ["Sunday Service Choir"], "producers": ["BoogzDaBeast", "FnZ"], "notes": "OG Filename: Fighting Fires - 01.09.20 <PERSON>wae Re<PERSON> Lee \"Fighting Fires\" reference track. A FLAC snippet leaked originally, but the full version only leaked in CDQ.", "length": "65.66", "fileDate": 16383168, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/00401efaa08734a596ac98ae13dd1ab3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/00401efaa08734a596ac98ae13dd1ab3\", \"key\": \"Fighting Fires\", \"title\": \"Fighting Fires [V7]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. Sunday Service Choir) (prod. BoogzDaBeast & FnZ)\", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"My Soul\"], \"description\": \"OG Filename: Fighting Fires - 01.09.20 Swae Ref\\nSwae Lee \\\"Fighting Fires\\\" reference track. A FLAC snippet leaked originally, but the full version only leaked in CDQ.\", \"date\": 16383168, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"424525a9f8b9dd05f5326794364ccdb9\", \"url\": \"https://api.pillowcase.su/api/download/424525a9f8b9dd05f5326794364ccdb9\", \"size\": \"3.09 MB\", \"duration\": 65.66}", "aliases": ["Faithful", "Fightin Fire", "My Soul"], "size": "3.09 MB"}, {"id": "fighting-fires-106", "name": "Fighting Fires [V8]", "artists": ["Sunday Service Choir"], "producers": ["BoogzDaBeast", "FnZ"], "notes": "OG Filename: Fighting Fires - Israel x Victory\nIsrael Boyd & Victory Boyd reference track for \"Fighting Fires\". Leaked as a fake WAV file.", "length": "153.5", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/2da9595acc737d9d608de30723517a4e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2da9595acc737d9d608de30723517a4e\", \"key\": \"Fighting Fires\", \"title\": \"Fighting Fires [V8]\", \"artists\": \"(ref. <PERSON> & <PERSON> Boyd) (feat. Sunday Service Choir) (prod. BoogzDaBeast & FnZ)\", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"My Soul\"], \"description\": \"OG Filename: Fighting Fires - Israel x Victory\\nIsrael Boyd & Victory Boyd reference track for \\\"Fighting Fires\\\". Leaked as a fake WAV file.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3fc171291797a58d7107b0abaef247b4\", \"url\": \"https://api.pillowcase.su/api/download/3fc171291797a58d7107b0abaef247b4\", \"size\": \"4.5 MB\", \"duration\": 153.5}", "aliases": ["Faithful", "Fightin Fire", "My Soul"], "size": "4.5 MB"}, {"id": "freedem", "name": "FreeDem [V5]", "artists": [], "producers": ["<PERSON>", "Edscusive"], "notes": "OG Filename: FreeDem - BIIM Ref\nBoyz II Men reference track for \"Freedom\" / \"FreeDem.\"", "length": "169.8", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/9d7e6c345efa691cb407c0d17b6f0190", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9d7e6c345efa691cb407c0d17b6f0190\", \"key\": \"FreeDem\", \"title\": \"FreeDem [V5]\", \"artists\": \"(ref. <PERSON>z II Men) (prod. <PERSON> & Edscusive)\", \"aliases\": [\"Heaven Calls\", \"When Heaven Calls\", \"Faith\", \"Freedom\"], \"description\": \"OG Filename: FreeDem - BIIM Ref\\nBoyz II Men reference track for \\\"Freedom\\\" / \\\"FreeDem.\\\"\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b08ae7a8be28a10cae33d0e0257bea93\", \"url\": \"https://api.pillowcase.su/api/download/b08ae7a8be28a10cae33d0e0257bea93\", \"size\": \"4.76 MB\", \"duration\": 169.8}", "aliases": ["Heaven Calls", "When Heaven Calls", "Faith", "Freedom"], "size": "4.76 MB"}, {"id": "freedem-108", "name": "FreeDem [V6]", "artists": ["Boyz II Men"], "producers": ["<PERSON>", "<PERSON>"], "notes": "OG Filename: FreeDem - 12.17.19 Kaycyy BIIM CyHi FS Edit\nCyHi reference track combined with the KayCyy and BIIM reference tracks. Slightly shortened.", "length": "122.98", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/0de532151b7496a4d7e0d739cefcd33e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0de532151b7496a4d7e0d739cefcd33e\", \"key\": \"FreeDem\", \"title\": \"FreeDem [V6]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON> & <PERSON>) (feat. <PERSON>z II Men) (prod. <PERSON> & <PERSON>)\", \"aliases\": [\"Heaven Calls\", \"When Heaven Calls\", \"Faith\", \"Freedom\"], \"description\": \"OG Filename: FreeDem - 12.17.19 Kaycyy BIIM CyHi FS Edit\\nCyHi reference track combined with the KayCyy and BIIM reference tracks. Slightly shortened.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e57e139ea31c8fd2be265e67f5dcde40\", \"url\": \"https://api.pillowcase.su/api/download/e57e139ea31c8fd2be265e67f5dcde40\", \"size\": \"4.01 MB\", \"duration\": 122.98}", "aliases": ["Heaven Calls", "When Heaven Calls", "Faith", "Freedom"], "size": "4.01 MB"}, {"id": "future-bounce-109", "name": "Future Bounce [V10]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "<PERSON><PERSON> reference track, recorded in February 2020 after he and <PERSON><PERSON><PERSON> reconnected. Snippet leaked by <PERSON><PERSON> himself on Discord on March 1st, 2022.", "length": "44.75", "fileDate": 16460928, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/d5c290ce0474f6a3956df58e723365b2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d5c290ce0474f6a3956df58e723365b2\", \"key\": \"Future Bounce\", \"title\": \"Future Bounce [V10]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. BoogzDaBeast)\", \"aliases\": [\"Future Sounds\", \"TELEKINESIS\", \"Ultrasounds\"], \"description\": \"<PERSON><PERSON> reference track, recorded in February 2020 after he and <PERSON><PERSON><PERSON> reconnected. Snippet leaked by <PERSON><PERSON> himself on Discord on March 1st, 2022.\", \"date\": 16460928, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3b80c5e8025696d489717070980b987b\", \"url\": \"https://api.pillowcase.su/api/download/3b80c5e8025696d489717070980b987b\", \"size\": \"2.76 MB\", \"duration\": 44.75}", "aliases": ["Future Sounds", "TELEKINESIS", "Ultrasounds"], "size": "2.76 MB"}, {"id": "glory", "name": "✨ Glory [V1]", "artists": ["Sunday Service Choir"], "producers": [], "notes": "OG Filename: Glory - 01.09.20 <PERSON><PERSON><PERSON> Ref\n<PERSON> reference track. Has unheard Sunday Service Choir vocals. Interpolates the Protestant version of \"Our Father\". Snippet originally leaked February 9th, 2025, with the full thing leaking March 16th, 2025.", "length": "135.33", "fileDate": 17420832, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/3edacd2e90cd2741384d2fbe97d32c6a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3edacd2e90cd2741384d2fbe97d32c6a\", \"key\": \"Glory\", \"title\": \"\\u2728 Glory [V1]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. Sunday Service Choir)\", \"aliases\": [\"<PERSON><PERSON>'s Glory\", \"<PERSON><PERSON>\", \"South Carolina\"], \"description\": \"OG Filename: Glory - 01.09.20 Kaycyy Ref\\nKayCyy reference track. Has unheard Sunday Service Choir vocals. Interpolates the Protestant version of \\\"Our Father\\\". Snippet originally leaked February 9th, 2025, with the full thing leaking March 16th, 2025.\", \"date\": 17420832, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"77327dde55aa94631de69fa446eb9255\", \"url\": \"https://api.pillowcase.su/api/download/77327dde55aa94631de69fa446eb9255\", \"size\": \"4.21 MB\", \"duration\": 135.33}", "aliases": ["Donda's Glory", "<PERSON><PERSON>", "South Carolina"], "size": "4.21 MB"}, {"id": "go-legendary", "name": "Go Legendary [V2]", "artists": ["Kay<PERSON>y<PERSON>"], "producers": [], "notes": "OG Filename: Go Legendary 125 - 01.30.20 <PERSON><PERSON><PERSON> Ref\nLater version of \"Go Legendary\", featuring <PERSON><PERSON><PERSON><PERSON> reference vocals alongside and over the mumble <PERSON><PERSON><PERSON> vocals. Leaked on September 11th, 2023 after a snippet was leaked more than a year before.", "length": "245.81", "fileDate": 16943904, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/550275896cabdd7719e71b4ebd43a1ae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/550275896cabdd7719e71b4ebd43a1ae\", \"key\": \"Go Legendary\", \"title\": \"Go Legendary [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Go Legendary 125 - 01.30.20 Kaycyy Ref\\nLater version of \\\"Go Legendary\\\", featuring <PERSON><PERSON><PERSON><PERSON> reference vocals alongside and over the mumble <PERSON><PERSON><PERSON> vocals. Leaked on September 11th, 2023 after a snippet was leaked more than a year before.\", \"date\": 16943904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"de28936feadfff04d62cbce0995fb158\", \"url\": \"https://api.pillowcase.su/api/download/de28936feadfff04d62cbce0995fb158\", \"size\": \"5.98 MB\", \"duration\": 245.81}", "aliases": [], "size": "5.98 MB"}, {"id": "how-ya-feel", "name": "How Ya Feel [V2]", "artists": [], "producers": ["Kanye West", "<PERSON>", "<PERSON>"], "notes": "OG Filename: 200309 How Ya Feel Jam\nLater version of the Yandhi era song with drums from <PERSON> and <PERSON>. Leaked in full as a bonus to the Glory / Throw Money Everywhere groupbuy with stems.", "length": "230.74", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/4e1fa3a9bb16e7db7a4c2792b18d32a6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4e1fa3a9bb16e7db7a4c2792b18d32a6\", \"key\": \"How Ya Feel\", \"title\": \"How Ya Feel [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"description\": \"OG Filename: 200309 How Ya Feel Jam\\nLater version of the Yandhi era song with drums from <PERSON> and <PERSON>. Leaked in full as a bonus to the Glory / Throw Money Everywhere groupbuy with stems.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4ae0f25876e5b2ce27b3643865732b22\", \"url\": \"https://api.pillowcase.su/api/download/4ae0f25876e5b2ce27b3643865732b22\", \"size\": \"5.74 MB\", \"duration\": 230.74}", "aliases": [], "size": "5.74 MB"}, {"id": "i-feel-terrific", "name": "I Feel Terrific [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: I Feel Terrific - 02.03.20 Kay<PERSON>y Ref\nReference track for \"I Feel Terrific\" done by <PERSON><PERSON><PERSON><PERSON>.", "length": "124.51", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/272f9ede4ddd03a67d2b658f7cd53f97", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/272f9ede4ddd03a67d2b658f7cd53f97\", \"key\": \"I Feel Terrific\", \"title\": \"I Feel Terrific [V2]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: I Feel Terrific - 02.03.20 Kaycyy Ref\\nReference track for \\\"I Feel Terrific\\\" done by <PERSON><PERSON><PERSON><PERSON>.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d19a8d4238a48af718da8329b9336d31\", \"url\": \"https://api.pillowcase.su/api/download/d19a8d4238a48af718da8329b9336d31\", \"size\": \"4.04 MB\", \"duration\": 124.51}", "aliases": [], "size": "4.04 MB"}, {"id": "i-feel-terrific-114", "name": "I Feel Terrific [V4]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: I Feel Terrific - 02.27.20 New Ending\nLate February 2020 version of \"I Feel Terrific\". Features <PERSON>'s producer tag alongside an outro not seen in any other version.", "length": "124.51", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/b2d5a5ddfd61975e89728b8f141f9db9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b2d5a5ddfd61975e89728b8f141f9db9\", \"key\": \"I Feel Terrific\", \"title\": \"I Feel Terrific [V4]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: I Feel Terrific - 02.27.20 New Ending\\nLate February 2020 version of \\\"I Feel Terrific\\\". Features <PERSON>'s producer tag alongside an outro not seen in any other version.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c3ca7b2a88c8d0c00c62e2e2e0241130\", \"url\": \"https://api.pillowcase.su/api/download/c3ca7b2a88c8d0c00c62e2e2e0241130\", \"size\": \"4.04 MB\", \"duration\": 124.51}", "aliases": [], "size": "4.04 MB"}, {"id": "i-feel-terrific-115", "name": "I Feel Terrific [V8]", "artists": [], "producers": ["<PERSON>"], "notes": "Consequence \"I Feel Terrific\" reference track. Was played on a July 2023 Instagram Live.", "length": "", "fileDate": 16897248, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/48b406e9d2978fe2834297cc5e7e145b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/48b406e9d2978fe2834297cc5e7e145b\", \"key\": \"I Feel Terrific\", \"title\": \"I Feel Terrific [V8]\", \"artists\": \"(ref. Consequence) (prod. <PERSON>)\", \"description\": \"Consequence \\\"I Feel Terrific\\\" reference track. Was played on a July 2023 Instagram Live.\", \"date\": 16897248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "idea-2", "name": "Idea 2", "artists": [], "producers": ["Kanye West", "<PERSON>", "<PERSON>"], "notes": "OG Filename: 200309 Cabin Drums - Idea 2_ASR Break, Drums, <PERSON><PERSON><PERSON>_Ye Freestyle 4.3.20\nMumble freestyle from April 2020.", "length": "144.94", "fileDate": 16949952, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/b312e7e32450592333871e43400e320a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b312e7e32450592333871e43400e320a\", \"key\": \"Idea 2\", \"title\": \"Idea 2\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"aliases\": [\"Jam 2\"], \"description\": \"OG Filename: 200309 Cabin Drums - Idea 2_ASR Break, Drums, Melotron_Ye Freestyle 4.3.20\\nMumble freestyle from April 2020.\", \"date\": 16949952, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c5a0655aa332e1d2ad39c770b2e5e449\", \"url\": \"https://api.pillowcase.su/api/download/c5a0655aa332e1d2ad39c770b2e5e449\", \"size\": \"4.36 MB\", \"duration\": 144.94}", "aliases": ["Jam 2"], "size": "4.36 MB"}, {"id": "keep-my-spirit-alive", "name": "Keep My Spirit Alive [V4]", "artists": ["Kay<PERSON>y<PERSON>"], "producers": [], "notes": "One of the (likely) many freestyles recorded for the finished \"Keep My Spirit Alive\" verse. Date unknown, but some lines from this take are seemingly used in the following versions. Has alternate production.", "length": "94.18", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/36e9bd0942764c4471d81e9ef7cd6e43", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/36e9bd0942764c4471d81e9ef7cd6e43\", \"key\": \"Keep My Spirit Alive\", \"title\": \"Keep My Spirit Alive [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Keep Our Spirit Alive\"], \"description\": \"One of the (likely) many freestyles recorded for the finished \\\"Keep My Spirit Alive\\\" verse. Date unknown, but some lines from this take are seemingly used in the following versions. Has alternate production.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"505bdcf682e795643b35c4349c31c9b2\", \"url\": \"https://api.pillowcase.su/api/download/505bdcf682e795643b35c4349c31c9b2\", \"size\": \"3.55 MB\", \"duration\": 94.18}", "aliases": ["Keep Our Spirit Alive"], "size": "3.55 MB"}, {"id": "life-of-the-party", "name": "Life Of The Party [V2]", "artists": [], "producers": ["AllDay"], "notes": "OG Filename: Life Of The Party - 02.27.20 Clean\nMumble version of \"Life Of The Party\" from February 2020, basically just a clean version of the original April 2019 freestyle.", "length": "217.8", "fileDate": 16954272, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/4cb9009b90c7094ca25e0cfe93cb5a4e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4cb9009b90c7094ca25e0cfe93cb5a4e\", \"key\": \"Life Of The Party\", \"title\": \"Life Of The Party [V2]\", \"artists\": \"(prod. AllDay)\", \"description\": \"OG Filename: Life Of The Party - 02.27.20 Clean\\nMumble version of \\\"Life Of The Party\\\" from February 2020, basically just a clean version of the original April 2019 freestyle.\", \"date\": 16954272, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8ce9a8736139a29686a5f8958899f32d\", \"url\": \"https://api.pillowcase.su/api/download/8ce9a8736139a29686a5f8958899f32d\", \"size\": \"5.53 MB\", \"duration\": 217.8}", "aliases": [], "size": "5.53 MB"}, {"id": "lord-i-need-you-to-wrap-your-arms-around-me-119", "name": "Lord I Need You To Wrap Your Arms Around Me [V9]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "<PERSON><PERSON> reference track recorded in February 2020 after he and <PERSON><PERSON><PERSON> reconnected. <PERSON><PERSON> mentions the Pandemic hitting, so it was probably recorded in March or April. Contains a Donda West speech. The beat was made by his producer friend, but the vocals are real and were recorded for <PERSON><PERSON><PERSON>. Leaked as a bonus for the July \"LINY\" groupbuy.", "length": "234.32", "fileDate": 16411680, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/a164c7b63d011eeea9c57cfa3b74552e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a164c7b63d011eeea9c57cfa3b74552e\", \"key\": \"Lord I Need You To Wrap Your Arms Around Me\", \"title\": \"Lord I Need You To Wrap Your Arms Around Me [V9]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Lord\", \"Lord I Need You\", \"Lord I Need You Right Now\", \"Wrap Your Arms Around Me\"], \"description\": \"<PERSON><PERSON> reference track recorded in February 2020 after he and <PERSON><PERSON><PERSON> reconnected. <PERSON><PERSON> mentions the Pandemic hitting, so it was probably recorded in March or April. Contains a <PERSON><PERSON> speech. The beat was made by his producer friend, but the vocals are real and were recorded for <PERSON><PERSON><PERSON>. Leaked as a bonus for the July \\\"LINY\\\" groupbuy.\", \"date\": 16411680, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"59db85d253ef0af40facd844f2d95630\", \"url\": \"https://api.pillowcase.su/api/download/59db85d253ef0af40facd844f2d95630\", \"size\": \"5.79 MB\", \"duration\": 234.32}", "aliases": ["Lord", "Lord I Need You", "Lord I Need You Right Now", "Wrap Your Arms Around Me"], "size": "5.79 MB"}, {"id": "lord-i-need-you-to-wrap-your-arms-around-me-120", "name": "Lord I Need You To Wrap Your Arms Around Me [V10]", "artists": [], "producers": ["<PERSON>", "BoogzDaBeast"], "notes": "<PERSON> version, from March of 2020. Was rediscovered soon after <PERSON><PERSON> was released.", "length": "46.13", "fileDate": 15839712, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/97f237cffb7fa9d2f695da5df92af72c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/97f237cffb7fa9d2f695da5df92af72c\", \"key\": \"Lord I Need You To Wrap Your Arms Around Me\", \"title\": \"Lord I Need You To Wrap Your Arms Around Me [V10]\", \"artists\": \"(prod. <PERSON> & BoogzDaBeast)\", \"aliases\": [\"Lord\", \"Lord I Need You\", \"Lord I Need You Right Now\", \"Wrap Your Arms Around Me\"], \"description\": \"<PERSON>z version, from March of 2020. Was rediscovered soon after <PERSON><PERSON> was released.\", \"date\": 15839712, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"37494e7540a245efbf3123c74423720c\", \"url\": \"https://api.pillowcase.su/api/download/37494e7540a245efbf3123c74423720c\", \"size\": \"2.78 MB\", \"duration\": 46.13}", "aliases": ["Lord", "Lord I Need You", "Lord I Need You Right Now", "Wrap Your Arms Around Me"], "size": "2.78 MB"}, {"id": "oh-lord", "name": "Oh Lord [V1]", "artists": [], "producers": [], "notes": "OG Filename: Oh Lord - Ye Ref 1\nOriginal Kanye freestyle for \"Oh Lord.\" From December 2019. Leaked alongside the Pro Tools session.", "length": "145.65", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/d24cdd775fff0d4a4fcd3afd111d03f3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d24cdd775fff0d4a4fcd3afd111d03f3\", \"key\": \"Oh Lord\", \"title\": \"Oh Lord [V1]\", \"description\": \"OG Filename: Oh <PERSON> - Ye Ref 1\\nOriginal Kanye freestyle for \\\"Oh Lord.\\\" From December 2019. Leaked alongside the Pro Tools session.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"40d28852b0e8f87011eb68dc451dd702\", \"url\": \"https://api.pillowcase.su/api/download/40d28852b0e8f87011eb68dc451dd702\", \"size\": \"4.37 MB\", \"duration\": 145.65}", "aliases": [], "size": "4.37 MB"}, {"id": "oh-lord-122", "name": "Oh Lord [V2]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON> <PERSON> <PERSON> <PERSON> 2\nSame freestyle as Ref 1 with a slightly different vocal and mixing arrangement. Leaked alongside the Pro Tools session.", "length": "145.65", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/0eb25e11ba81c1021ad4bd5ca9252a44", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0eb25e11ba81c1021ad4bd5ca9252a44\", \"key\": \"Oh <PERSON>\", \"title\": \"Oh Lord [V2]\", \"description\": \"OG Filename: Oh Lord - <PERSON> Ref 2\\nSame freestyle as Ref 1 with a slightly different vocal and mixing arrangement. Leaked alongside the Pro Tools session.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"465f68f5ec831303fd47fb9061b2d7da\", \"url\": \"https://api.pillowcase.su/api/download/465f68f5ec831303fd47fb9061b2d7da\", \"size\": \"4.37 MB\", \"duration\": 145.65}", "aliases": [], "size": "4.37 MB"}, {"id": "oh-lord-123", "name": "Oh Lord [V3]", "artists": [], "producers": [], "notes": "OG Filename: Oh Lord - Fest Ref\nShort Rhymefest reference of \"Oh Lord\", which has an alternate vocal take in the Pro Tools session files. Leaked alongside the Pro Tools session on.", "length": "43.76", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/e1fe7039058df990bcfcd601e9113bd6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e1fe7039058df990bcfcd601e9113bd6\", \"key\": \"Oh Lord\", \"title\": \"Oh Lord [V3]\", \"artists\": \"(ref. Rhymefest)\", \"description\": \"OG Filename: Oh Lord - Fest Ref\\nShort Rhymefest reference of \\\"Oh Lord\\\", which has an alternate vocal take in the Pro Tools session files. Leaked alongside the Pro Tools session on.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"04b062ae1abb1c8068f3b51f32e683c1\", \"url\": \"https://api.pillowcase.su/api/download/04b062ae1abb1c8068f3b51f32e683c1\", \"size\": \"2.74 MB\", \"duration\": 43.76}", "aliases": [], "size": "2.74 MB"}, {"id": "oh-lord-124", "name": "Oh Lord [V4]", "artists": [], "producers": [], "notes": "OG Filename: Oh Lord - 12.04.19 Fest Ye Ref 1\nFirst Rhymefest reference version. Uses his \"Follow God\" reference vocals. Also has some Ye vocals.", "length": "157.27", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/df2f4923999b00fad578d14fd367c56f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/df2f4923999b00fad578d14fd367c56f\", \"key\": \"Oh Lord\", \"title\": \"Oh Lord [V4]\", \"artists\": \"(ref. Rhymefest)\", \"description\": \"OG Filename: Oh Lord - 12.04.19 Fest Ye Ref 1\\nFirst Rhymefest reference version. Uses his \\\"Follow God\\\" reference vocals. Also has some Ye vocals.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"22fa44542c9533967066ec7e6bb33e82\", \"url\": \"https://api.pillowcase.su/api/download/22fa44542c9533967066ec7e6bb33e82\", \"size\": \"4.56 MB\", \"duration\": 157.27}", "aliases": [], "size": "4.56 MB"}, {"id": "oh-lord-125", "name": "✨ Oh Lord [V5]", "artists": [], "producers": [], "notes": "OG Filename: Oh Lord - 12.04.19 Fest Ye Ref 2\nSecond Rhymefest reference version. Reuses vocals from his \"Let It Go\" reference. Also has some Ye vocals.", "length": "146.33", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/1b3afa84054626cfd0f215886ce720c3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1b3afa84054626cfd0f215886ce720c3\", \"key\": \"Oh Lord\", \"title\": \"\\u2728 Oh Lord [V5]\", \"artists\": \"(ref. Rhymefest)\", \"description\": \"OG Filename: Oh Lord - 12.04.19 Fest Ye Ref 2\\nSecond Rhymefest reference version. Reuses vocals from his \\\"Let It Go\\\" reference. Also has some Ye vocals.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8afc29dcc16f8b7cc07cda083e85db89\", \"url\": \"https://api.pillowcase.su/api/download/8afc29dcc16f8b7cc07cda083e85db89\", \"size\": \"4.39 MB\", \"duration\": 146.33}", "aliases": [], "size": "4.39 MB"}, {"id": "oh-lord-126", "name": "Oh Lord [V6]", "artists": [], "producers": [], "notes": "OG Filename: Oh Lord - 12.04.19 Fest Ye Ref 3\nVersion of \"Oh Lord\" featuring a Rhymefest reference verse that reuses vocals from his \"Let It Go\" reference track. OG WAV file leaked on October 10th, 2023 alongside the Pro Tools session of \"Oh Lord\". Has a little bit of instrumental at the end attached not seen in the previous version. Also has some Ye vocals.", "length": "157.27", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/996ca7e2fd347c8f13848f95569cbc80", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/996ca7e2fd347c8f13848f95569cbc80\", \"key\": \"Oh Lord\", \"title\": \"Oh Lord [V6]\", \"artists\": \"(ref. Rhymefest)\", \"description\": \"OG Filename: Oh Lord - 12.04.19 Fest Ye Ref 3\\nVersion of \\\"Oh Lord\\\" featuring a Rhymefest reference verse that reuses vocals from his \\\"Let It Go\\\" reference track. OG WAV file leaked on October 10th, 2023 alongside the Pro Tools session of \\\"Oh Lord\\\". Has a little bit of instrumental at the end attached not seen in the previous version. Also has some Ye vocals.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d8c78b240988fa228259a32f10f9bd96\", \"url\": \"https://api.pillowcase.su/api/download/d8c78b240988fa228259a32f10f9bd96\", \"size\": \"4.56 MB\", \"duration\": 157.27}", "aliases": [], "size": "4.56 MB"}, {"id": "open-on-monday", "name": "Open On Monday", "artists": [], "producers": [], "notes": "OG Filename: OpenOnMonday 70 - 02.03.20 - <PERSON><PERSON><PERSON> Ref\nReference track for \"Open On Monday\" done by <PERSON><PERSON><PERSON><PERSON>. <PERSON> vocals.", "length": "124.15", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/b8cbe9152d2e6facaa4e4a0e969cbe70", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b8cbe9152d2e6facaa4e4a0e969cbe70\", \"key\": \"Open On Monday\", \"title\": \"Open On Monday\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"description\": \"OG Filename: OpenOnMonday 70 - 02.03.20 - Kaycyy Ref\\nReference track for \\\"Open On Monday\\\" done by KayCyy. <PERSON> vocals.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9732df098739961dd4bfdeeb6b225e42\", \"url\": \"https://api.pillowcase.su/api/download/9732df098739961dd4bfdeeb6b225e42\", \"size\": \"4.03 MB\", \"duration\": 124.15}", "aliases": [], "size": "4.03 MB"}, {"id": "-128", "name": "??? [V1]", "artists": [], "producers": ["30 Roc", "Zentachi"], "notes": "OG Filename: 30 x Zentachi 3 - 01.28.20 <PERSON><PERSON><PERSON> Ref\n<PERSON> reference for \"Praise God\". Samples \"Dream Of You\" by <PERSON><PERSON>. Leaked in a huge pile of KayCyy leaks from December 2020", "length": "193.27", "fileDate": 16080768, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/cb075606c2f2e4bb3add856fa58a2322", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cb075606c2f2e4bb3add856fa58a2322\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. 30 Roc & Zentachi)\", \"aliases\": [\"<PERSON>rai<PERSON>\", \"<PERSON>raise God\"], \"description\": \"OG Filename: 30 x Zentachi 3 - 01.28.20 Kaycyy Ref\\nKayCyy reference for \\\"Praise God\\\". <PERSON><PERSON> \\\"Dream Of You\\\" by <PERSON><PERSON>. Leaked in a huge pile of KayCyy leaks from December 2020\", \"date\": 16080768, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dff7d29fa226acd3308d7ce8399c102c\", \"url\": \"https://api.pillowcase.su/api/download/dff7d29fa226acd3308d7ce8399c102c\", \"size\": \"5.14 MB\", \"duration\": 193.27}", "aliases": ["<PERSON>raise", "Praise God"], "size": "5.14 MB"}, {"id": "run-home", "name": "Run Home", "artists": [], "producers": [], "notes": "OG Filename: Run Home - 05.05.20 Chop 1.1\nRough mumble freestyle from May 2020. Original snippet leaked July 30th, 2023.", "length": "142.01", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/250f7d5a2958ed26224fdd051f28c1e9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/250f7d5a2958ed26224fdd051f28c1e9\", \"key\": \"Run Home\", \"title\": \"Run Home\", \"description\": \"OG Filename: Run Home - 05.05.20 Chop 1.1\\nRough mumble freestyle from May 2020. Original snippet leaked July 30th, 2023.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"305f3ada3245690bf3cd4e1a8927db07\", \"url\": \"https://api.pillowcase.su/api/download/305f3ada3245690bf3cd4e1a8927db07\", \"size\": \"4.32 MB\", \"duration\": 142.01}", "aliases": [], "size": "4.32 MB"}, {"id": "sinner-130", "name": "Sinner [V5]", "artists": [], "producers": ["Wheezy", "BoogzDaBeast"], "notes": "OG Filename: Sinner - 02.25.20 Wheezy Half Intro\nVersion of Wheezy \"Sinner\". Original snippet leaked August 12th, 2022.", "length": "173.04", "fileDate": 16648416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/ad4eab3b2cc6d5fe3619b8c922c62401", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ad4eab3b2cc6d5fe3619b8c922c62401\", \"key\": \"Sinner\", \"title\": \"Sinner [V5]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & BoogzDaBeast)\", \"aliases\": [\"Sinners\"], \"description\": \"OG Filename: Sinner - 02.25.20 Wheezy Half Intro\\nVersion of Wheezy \\\"Sinner\\\". Original snippet leaked August 12th, 2022.\", \"date\": 16648416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"8d42345ee9b9ac2b0a2ff023185b500a\", \"url\": \"https://api.pillowcase.su/api/download/8d42345ee9b9ac2b0a2ff023185b500a\", \"size\": \"4.81 MB\", \"duration\": 173.04}", "aliases": ["Sinners"], "size": "4.81 MB"}, {"id": "south-florida", "name": "South Florida [V1]", "artists": [], "producers": [], "notes": "OG Filename: South Florida 81 - 01.24.20 <PERSON><PERSON>y Ref\n<PERSON> reference track for \"South Florida\" likely made during the JESUS IS LORD sessions. Leaked after it was put up for GB on October 27th, 2023.", "length": "154.13", "fileDate": 16983648, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/dcc8f66704f3c47ca8c37772da0339ab", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dcc8f66704f3c47ca8c37772da0339ab\", \"key\": \"South Florida\", \"title\": \"South Florida [V1]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: South Florida 81 - 01.24.20 Kaycyy Ref\\nKayCyy reference track for \\\"South Florida\\\" likely made during the JESUS IS LORD sessions. Leaked after it was put up for GB on October 27th, 2023.\", \"date\": 16983648, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"aa086684021aa9427742f003e8d96f70\", \"url\": \"https://api.pillowcase.su/api/download/aa086684021aa9427742f003e8d96f70\", \"size\": \"4.51 MB\", \"duration\": 154.13}", "aliases": [], "size": "4.51 MB"}, {"id": "spiritual-vibrations", "name": "Spiritual Vibrations", "artists": [], "producers": [], "notes": "OG Filename: 13 Spiritual VIbrations - 01.21.20 Kaycee REF - Bb - 144.44\nKayCyy \"Spiritual Vibrations\" reference track.", "length": "124.25", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/ddc676676f45dc3ba2b755db983f1f4b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ddc676676f45dc3ba2b755db983f1f4b\", \"key\": \"Spiritual Vibrations\", \"title\": \"Spiritual Vibrations\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: 13 Spiritual VIbrations - 01.21.20 Kaycee REF - Bb - 144.44\\nKayCyy \\\"Spiritual Vibrations\\\" reference track.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6c221fa9720d9244d63e4c8505cbfad2\", \"url\": \"https://api.pillowcase.su/api/download/6c221fa9720d9244d63e4c8505cbfad2\", \"size\": \"4.03 MB\", \"duration\": 124.25}", "aliases": [], "size": "4.03 MB"}, {"id": "they-were-overcome", "name": "They Were Overcome [V3]", "artists": ["Sunday Service Choir"], "producers": ["FnZ"], "notes": "<PERSON><PERSON><PERSON>y \"They Were Overcome\" reference track. Snippet leaked December 9th, 2022.", "length": "10.08", "fileDate": 16705440, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/d55e3830abefad420ef39bb22ec2073c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d55e3830abefad420ef39bb22ec2073c\", \"key\": \"They Were Overcome\", \"title\": \"They Were Overcome [V3]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON><PERSON>) (feat. Sunday Service Choir) (prod. FnZ)\", \"aliases\": [\"Blood Of The Lamb\", \"Donda\"], \"description\": \"<PERSON><PERSON><PERSON><PERSON> \\\"They Were Overcome\\\" reference track. Snippet leaked December 9th, 2022.\", \"date\": 16705440, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9fb8498d02ae1932f35aaa1ff149f3bd\", \"url\": \"https://api.pillowcase.su/api/download/9fb8498d02ae1932f35aaa1ff149f3bd\", \"size\": \"2.21 MB\", \"duration\": 10.08}", "aliases": ["Blood Of The Lamb", "<PERSON><PERSON>"], "size": "2.21 MB"}, {"id": "vibrations", "name": "Vibrations [V1]", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "March 2020 freestyle from the same session as <PERSON><PERSON> \"Welcome To My Life\". Snippet leaked June 13th 2024.", "length": "15.46", "fileDate": 17182368, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/b506a1e91490f6c4664d2cde9172baf5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b506a1e91490f6c4664d2cde9172baf5\", \"key\": \"Vibrations\", \"title\": \"Vibrations [V1]\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"description\": \"March 2020 freestyle from the same session as <PERSON><PERSON> \\\"Welcome To My Life\\\". Snippet leaked June 13th 2024.\", \"date\": 17182368, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8a695c50257f907a286b9e24ddba3d88\", \"url\": \"https://api.pillowcase.su/api/download/8a695c50257f907a286b9e24ddba3d88\", \"size\": \"2.29 MB\", \"duration\": 15.46}", "aliases": [], "size": "2.29 MB"}, {"id": "vibrations-135", "name": "Vibrations [V2]", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "April 2020 version of \"Vibrations\" with a 5 minute freestyle and a different beat. Snippet leaked June 13th 2024.", "length": "20.35", "fileDate": 17182368, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/242579b8eecc5986090aba1172006237", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/242579b8eecc5986090aba1172006237\", \"key\": \"Vibrations\", \"title\": \"Vibrations [V2]\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"description\": \"April 2020 version of \\\"Vibrations\\\" with a 5 minute freestyle and a different beat. Snippet leaked June 13th 2024.\", \"date\": 17182368, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7cb9e60726f3b019768c9205f0ef599b\", \"url\": \"https://api.pillowcase.su/api/download/7cb9e60726f3b019768c9205f0ef599b\", \"size\": \"2.37 MB\", \"duration\": 20.35}", "aliases": [], "size": "2.37 MB"}, {"id": "want-you", "name": "Want You [V1]", "artists": [], "producers": [], "notes": "OG Filename: Want You 92 - 01.26.20 <PERSON><PERSON><PERSON> Ref\n<PERSON> \"Want You\" reference track. Was mentioned by <PERSON><PERSON><PERSON><PERSON> on a Discord call. Original snippet leaked April 2022.", "length": "167.05", "fileDate": ********, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/9d059370e038822d38bdfacddb781e33", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9d059370e038822d38bdfacddb781e33\", \"key\": \"Want You\", \"title\": \"Want You [V1]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"How Can I Complain\", \"I Can't Complain\"], \"description\": \"OG Filename: Want You 92 - 01.26.20 Kaycyy Ref\\nKayCyy \\\"Want You\\\" reference track. Was mentioned by <PERSON><PERSON><PERSON><PERSON> on a Discord call. Original snippet leaked April 2022.\", \"date\": ********, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a733db93a1bec3aa666625c7852587f9\", \"url\": \"https://api.pillowcase.su/api/download/a733db93a1bec3aa666625c7852587f9\", \"size\": \"4.72 MB\", \"duration\": 167.05}", "aliases": ["How Can I Complain", "I Can't Co<PERSON>lain"], "size": "4.72 MB"}, {"id": "welcome-to-my-life", "name": "Welcome To My Life [V1]", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "\"Welcome To My Life\" freestyle. Has mumble, is said to be long, has <PERSON> mumbling the Ty Dolla $ign parts of the song, \"attempting to rap\" for \"like two lines\" and has reference singing vocals from an unknown artist. Can faintly be heard in the drum stems of later versions of the song.", "length": "239.46", "fileDate": 16949088, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/e942ed7a4aec9c72491b2450c822a2fa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e942ed7a4aec9c72491b2450c822a2fa\", \"key\": \"Welcome To My Life\", \"title\": \"Welcome To My Life [V1]\", \"artists\": \"(ref. ???) (prod. <PERSON> & <PERSON>)\", \"description\": \"\\\"Welcome To My Life\\\" freestyle. Has mumble, is said to be long, has <PERSON> mumbling the Ty Dolla $ign parts of the song, \\\"attempting to rap\\\" for \\\"like two lines\\\" and has reference singing vocals from an unknown artist. Can faintly be heard in the drum stems of later versions of the song.\", \"date\": 16949088, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"3d9b593955e52960360174964d336932\", \"url\": \"https://api.pillowcase.su/api/download/3d9b593955e52960360174964d336932\", \"size\": \"5.88 MB\", \"duration\": 239.46}", "aliases": [], "size": "5.88 MB"}, {"id": "welcome-to-my-life-138", "name": "Welcome To My Life [V1]", "artists": [], "producers": ["<PERSON>", "<PERSON>"], "notes": "\"Welcome To My Life\" freestyle. Has mumble, is said to be long, has <PERSON> mumbling the Ty Dolla $ign parts of the song, \"attempting to rap\" for \"like two lines\" and has reference singing vocals from an unknown artist. Can faintly be heard in the drum stems of later versions of the song.", "length": "", "fileDate": 16949088, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/00037a63f63f62d60aa7117c32a867d2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/00037a63f63f62d60aa7117c32a867d2\", \"key\": \"Welcome To My Life\", \"title\": \"Welcome To My Life [V1]\", \"artists\": \"(ref. ???) (prod. <PERSON> & <PERSON>)\", \"description\": \"\\\"Welcome To My Life\\\" freestyle. Has mumble, is said to be long, has <PERSON> mumbling the Ty Dolla $ign parts of the song, \\\"attempting to rap\\\" for \\\"like two lines\\\" and has reference singing vocals from an unknown artist. Can faintly be heard in the drum stems of later versions of the song.\", \"date\": 16949088, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "work-it-out", "name": "Work It Out [V2]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Work It Out - 01.21.20 <PERSON><PERSON><PERSON>, mumble <PERSON> reference for the hook of \"In God's Country\", featuring rough singing during the open verses. The take of the hook from this would be later used on other versions of the song, before being replaced with <PERSON>.", "length": "138.86", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/071e8a2805d2ee7237da7946a8ba0900", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/071e8a2805d2ee7237da7946a8ba0900\", \"key\": \"Work It Out\", \"title\": \"Work It Out [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>aB<PERSON><PERSON>) \", \"aliases\": [\"God's Country\", \"In God's Country\"], \"description\": \"OG Filename: Work It Out - 01.21.20 Kaycee REF\\nRough, mumble KayCyy reference for the hook of \\\"In God's Country\\\", featuring rough singing during the open verses. The take of the hook from this would be later used on other versions of the song, before being replaced with <PERSON>.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0ab5fa1ae3c0708a7688c5a319104969\", \"url\": \"https://api.pillowcase.su/api/download/0ab5fa1ae3c0708a7688c5a319104969\", \"size\": \"4.27 MB\", \"duration\": 138.86}", "aliases": ["God's Country", "In God's Country"], "size": "4.27 MB"}, {"id": "work-it-out-140", "name": "Work It Out [V3]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: Work It Out - 01.24.20 Kaycee REF\nFurther along version of Kay<PERSON>yy's \"In God's Country\" reference, cutting out most of the mumble present within the previous version and arranging the track's structure.", "length": "138.86", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/c1dab79df722cb73feddaea230dd2852", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c1dab79df722cb73feddaea230dd2852\", \"key\": \"Work It Out\", \"title\": \"Work It Out [V3]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON>ogzDaBeast) \", \"aliases\": [\"God's Country\", \"In God's Country\"], \"description\": \"OG Filename: Work It Out - 01.24.20 Kaycee REF\\nFurther along version of Kay<PERSON>yy's \\\"In God's Country\\\" reference, cutting out most of the mumble present within the previous version and arranging the track's structure.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"042498d5e7b7892dc2e97cb74ba28b61\", \"url\": \"https://api.pillowcase.su/api/download/042498d5e7b7892dc2e97cb74ba28b61\", \"size\": \"4.27 MB\", \"duration\": 138.86}", "aliases": ["God's Country", "In God's Country"], "size": "4.27 MB"}, {"id": "-141", "name": "???", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "producers": [], "notes": "Unknown song played in videos said to be recorded January 12, 2020. First snippet was posted by <PERSON><PERSON> in 2020, a longer snippet was posted by <PERSON> in 2023. <PERSON> is the source for the snippet being a Kanye song with <PERSON><PERSON>, however since he also stated the snippet was \"new\" three years after it was recorded, info may not be fully accurate.", "length": "", "fileDate": 16782336, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/174d6660ad387650e71331588c8bcb39", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/174d6660ad387650e71331588c8bcb39\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Unknown song played in videos said to be recorded January 12, 2020. First snippet was posted by Bu in 2020, a longer snippet was posted by <PERSON> in 2023. <PERSON> is the source for the snippet being a Kanye song with <PERSON><PERSON>, however since he also stated the snippet was \\\"new\\\" three years after it was recorded, info may not be fully accurate.\", \"date\": 16782336, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "star-time", "name": "88-<PERSON> - Star Time [V9]", "artists": ["Kanye West", "IDK", "<PERSON> <PERSON>"], "producers": ["88-<PERSON>"], "notes": "OG Filename: <PERSON><PERSON><PERSON> X <PERSON> Paak X IDK X 88 Keys 1\nVersion of \"Start Time\" from 2020. Filename first posted partially on IDK's private instagram with eventually the full file name being posted by Alek. Original snippet leaked January 22nd, 2024.", "length": "172.15", "fileDate": 17063136, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/b016ba99059bca9eee01f27a2d35e2ac", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b016ba99059bca9eee01f27a2d35e2ac\", \"key\": \"Star Time\", \"title\": \"88-Keys - Star Time [V9]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, IDK & Anderson .<PERSON>ak) (prod. 88-<PERSON>)\", \"aliases\": [\"The Mind Is Powerful\", \"Start Time\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON> X Anderson Paak X IDK X 88 Keys 1\\nVersion of \\\"Start Time\\\" from 2020. Filename first posted partially on IDK's private instagram with eventually the full file name being posted by Alek. Original snippet leaked January 22nd, 2024.\", \"date\": 17063136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"60c9a2b4777a5ac90e23925fedb96563\", \"url\": \"https://api.pillowcase.su/api/download/60c9a2b4777a5ac90e23925fedb96563\", \"size\": \"4.8 MB\", \"duration\": 172.15}", "aliases": ["The Mind Is Powerful", "Start Time"], "size": "4.8 MB"}, {"id": "i-know-you", "name": "Abstract Mindstate - I Know You", "artists": ["Consequence"], "producers": ["Kanye West"], "notes": "OG Filename: I Know You - Cons HK 01.16.20\nVersion of \"I Know You,\" with a Consequence feature that is possibly reference vocals for <PERSON><PERSON><PERSON>.", "length": "145.06", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/535b32b064426e838eaeeb96e1eb70f3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/535b32b064426e838eaeeb96e1eb70f3\", \"key\": \"I Know You\", \"title\": \"Abstract Mindstate - I Know You\", \"artists\": \"(feat. Consequence) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: I Know You - Cons HK 01.16.20\\nVersion of \\\"I Know You,\\\" with a Consequence feature that is possibly reference vocals for <PERSON><PERSON><PERSON>.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c75619175317d7e4b1b7d757ada28391\", \"url\": \"https://api.pillowcase.su/api/download/c75619175317d7e4b1b7d757ada28391\", \"size\": \"4.36 MB\", \"duration\": 145.06}", "aliases": [], "size": "4.36 MB"}, {"id": "better-move", "name": "<PERSON> the Rapper & Lizzo - Better Move [V1]", "artists": [], "producers": ["Kanye West"], "notes": "<PERSON><PERSON> reference track for <PERSON><PERSON> and <PERSON> the Rapper with <PERSON><PERSON><PERSON> production. Originally thought to be made for Good Ass Job but according to <PERSON><PERSON> it was actually created on April 13th, 2020. Unknown if <PERSON>, <PERSON><PERSON><PERSON> or <PERSON><PERSON> ever recorded for this, or if it was actually a song intended for <PERSON><PERSON><PERSON>'s album, but there's a chance <PERSON><PERSON>'s verse was intended for <PERSON><PERSON><PERSON>, due to the \"Different game and the same Yeezus\" and \"They say I'm bipolar\" lines.", "length": "176.04", "fileDate": 16260480, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/f13c7025fce14c28b610d9f716bb2d32", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f13c7025fce14c28b610d9f716bb2d32\", \"key\": \"Better Move\", \"title\": \"<PERSON> the Rapper & <PERSON>zo - Better Move [V1]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"<PERSON><PERSON> reference track for <PERSON><PERSON> and <PERSON> the Rapper with <PERSON><PERSON><PERSON> production. Originally thought to be made for Good Ass Job but according to <PERSON><PERSON> it was actually created on April 13th, 2020. Unknown if <PERSON>, <PERSON><PERSON><PERSON> or <PERSON><PERSON> ever recorded for this, or if it was actually a song intended for <PERSON><PERSON><PERSON>'s album, but there's a chance <PERSON><PERSON>'s verse was intended for <PERSON><PERSON><PERSON>, due to the \\\"Different game and the same Yeezus\\\" and \\\"They say I'm bipolar\\\" lines.\", \"date\": 16260480, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"670ffdfb4c3366081bb911966585401b\", \"url\": \"https://api.pillowcase.su/api/download/670ffdfb4c3366081bb911966585401b\", \"size\": \"4.86 MB\", \"duration\": 176.04}", "aliases": [], "size": "4.86 MB"}, {"id": "better-move-145", "name": "<PERSON> the Rapper & Lizzo - Better Move [V2]", "artists": [], "producers": ["Kanye West"], "notes": "Version of \"Better Move\" with alternate production.", "length": "167.19", "fileDate": 16684704, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/d1c10846314f72a575925ae1f6ea90c1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d1c10846314f72a575925ae1f6ea90c1\", \"key\": \"Better Move\", \"title\": \"<PERSON> the Rapper & Lizzo - Better Move [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Version of \\\"Better Move\\\" with alternate production.\", \"date\": 16684704, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"eaf4a460c67d12e75d6be8d717e917d1\", \"url\": \"https://api.pillowcase.su/api/download/eaf4a460c67d12e75d6be8d717e917d1\", \"size\": \"4.72 MB\", \"duration\": 167.19}", "aliases": [], "size": "4.72 MB"}, {"id": "follow-me", "name": "North West - Follow Me", "artists": ["Sunday Service Choir"], "producers": ["Budgie Beats"], "notes": "OG Filename: Follow me_budgie edit\nRandom throwaway track done by <PERSON>'s daughter, <PERSON> West, with Sunday Service Choir vocals. This probably has no ties to <PERSON> past the familial relation, but it's cool.", "length": "147.37", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/7f62bf772ff364ed18ffe4e2e6db9219", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7f62bf772ff364ed18ffe4e2e6db9219\", \"key\": \"Follow Me\", \"title\": \"North West - Follow Me\", \"artists\": \"(feat. Sunday Service Choir) (prod. <PERSON><PERSON> Beats)\", \"description\": \"OG Filename: Follow me_budgie edit\\nRandom throwaway track done by <PERSON>'s daughter, <PERSON> West, with Sunday Service Choir vocals. This probably has no ties to <PERSON> past the familial relation, but it's cool.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"e7b80bb7b06aac8cbd3f3c1c390a1771\", \"url\": \"https://api.pillowcase.su/api/download/e7b80bb7b06aac8cbd3f3c1c390a1771\", \"size\": \"4.4 MB\", \"duration\": 147.37}", "aliases": [], "size": "4.4 MB"}, {"id": "headshot", "name": "<PERSON><PERSON><PERSON> - Headshot", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: headshot 2.3.20\nWas on early July 2021 Donda tracklists, the same file being reused. <PERSON><PERSON><PERSON> never recorded for the track.", "length": "164.36", "fileDate": 15868224, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/4c92e87bfef918ca50af1646d2b39d52", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4c92e87bfef918ca50af1646d2b39d52\", \"key\": \"Headshot\", \"title\": \"<PERSON><PERSON><PERSON> - Headshot\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Heads Off\"], \"description\": \"OG Filename: headshot 2.3.20\\nWas on early July 2021 Donda tracklists, the same file being reused. <PERSON><PERSON><PERSON> never recorded for the track.\", \"date\": 15868224, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"41d9a701a0159bdb18b36fcebb097816\", \"url\": \"https://api.pillowcase.su/api/download/41d9a701a0159bdb18b36fcebb097816\", \"size\": \"4.67 MB\", \"duration\": 164.36}", "aliases": ["Heads Off"], "size": "4.67 MB"}, {"id": "the-glade", "name": "<PERSON> - The Glade [V18]", "artists": ["<PERSON>"], "producers": ["Ace G", "S1"], "notes": "Made sometime in March 2020, when <PERSON> started work on the song again. Has some slight mixing changes to release.", "length": "227.11", "fileDate": 17036352, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/d1a91764bf25f5df681da609081c8ce9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d1a91764bf25f5df681da609081c8ce9\", \"key\": \"The Glade\", \"title\": \"<PERSON> - The Glade [V18]\", \"artists\": \"(feat. <PERSON>) (prod. Ace G & S1)\", \"aliases\": [\"Ashes\", \"Beauty From The Ashes\", \"Beauty To The Ashes\", \"Up From The Ashes\"], \"description\": \"Made sometime in March 2020, when <PERSON> started work on the song again. Has some slight mixing changes to release.\", \"date\": 17036352, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"1f25a7f4c66453bf4d77990638be662a\", \"url\": \"https://api.pillowcase.su/api/download/1f25a7f4c66453bf4d77990638be662a\", \"size\": \"5.68 MB\", \"duration\": 227.11}", "aliases": ["Ashes", "Beauty From The Ashes", "Beauty To The Ashes", "Up From The Ashes"], "size": "5.68 MB"}, {"id": "12-000-acres-149", "name": "12,000 Acres [V10]", "artists": [], "producers": ["BoogzDaBeast", "<PERSON>", "<PERSON>"], "notes": "Victory reference track for \"12,000 Acres\", used for the DONDA visual album. Date unknown, but likely recorded in late May 2020 when she was at Wyoming recording refs. Contains drums recorded and used for \"Welcome To My Life,\" as you can hear the \"WTML\" sample and freestyle in the drum bleed.", "length": "177.7", "fileDate": 16943904, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/9c86595a0f4c5711e3b225f8105370a7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9c86595a0f4c5711e3b225f8105370a7\", \"key\": \"12,000 Acres\", \"title\": \"12,000 Acres [V10]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"aliases\": [\"12 Thousand Acres\"], \"description\": \"Victory reference track for \\\"12,000 Acres\\\", used for the DONDA visual album. Date unknown, but likely recorded in late May 2020 when she was at Wyoming recording refs. Contains drums recorded and used for \\\"Welcome To My Life,\\\" as you can hear the \\\"WTML\\\" sample and freestyle in the drum bleed.\", \"date\": 16943904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b809d503a3994f5f40fe273f5ecf15e7\", \"url\": \"https://api.pillowcase.su/api/download/b809d503a3994f5f40fe273f5ecf15e7\", \"size\": \"4.89 MB\", \"duration\": 177.7}", "aliases": ["12 Thousand Acres"], "size": "4.89 MB"}, {"id": "12-000-acres-150", "name": "12,000 Acres [V11]", "artists": [], "producers": ["E.VAX"], "notes": "OG Filename: EVAN_mix\nEarly July version of \"12,000 Acres\", produced by E.VAX. Features the original <PERSON> freestyle vocals.", "length": "182.89", "fileDate": 16943904, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/eb1655880fa2683b846cfaf4034e1e3c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/eb1655880fa2683b846cfaf4034e1e3c\", \"key\": \"12,000 Acres\", \"title\": \"12,000 Acres [V11]\", \"artists\": \"(prod. E.VAX)\", \"aliases\": [\"12 Thousand Acres\"], \"description\": \"OG Filename: EVAN_mix\\nEarly July version of \\\"12,000 Acres\\\", produced by E.VAX. Features the original Ye freestyle vocals.\", \"date\": 16943904, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"34749623b1f6b15a4a10fb08db49c46b\", \"url\": \"https://api.pillowcase.su/api/download/34749623b1f6b15a4a10fb08db49c46b\", \"size\": \"4.97 MB\", \"duration\": 182.89}", "aliases": ["12 Thousand Acres"], "size": "4.97 MB"}, {"id": "24", "name": "24 [V6]", "artists": ["Kay<PERSON>y<PERSON>", "Sunday Service Choir"], "producers": ["SHŌLZ"], "notes": "OG Filename: LEFT\nVersion of 24 labeled \"LEFT,\" with drums from SHŌLZ and a different choir arangement. Likely from May 2020.", "length": "212.32", "fileDate": 16967232, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/5fb6c633da0f382d5e38176e7d7133c0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5fb6c633da0f382d5e38176e7d7133c0\", \"key\": \"24\", \"title\": \"24 [V6]\", \"artists\": \"(feat. KayCyy & Sunday Service Choir) (prod. SH\\u014cLZ)\", \"aliases\": [\"24 Candles\", \"24 Hours\"], \"description\": \"OG Filename: LEFT\\nVersion of 24 labeled \\\"LEFT,\\\" with drums from SH\\u014cLZ and a different choir arangement. Likely from May 2020.\", \"date\": 16967232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"dbbd9eb40e797e27e892e49f784ae1d7\", \"url\": \"https://api.pillowcase.su/api/download/dbbd9eb40e797e27e892e49f784ae1d7\", \"size\": \"5.44 MB\", \"duration\": 212.32}", "aliases": ["24 Candles", "24 Hours"], "size": "5.44 MB"}, {"id": "24-152", "name": "24 [V8]", "artists": ["Sunday Service Choir"], "producers": ["Dem <PERSON>z", "<PERSON>", "<PERSON>"], "notes": "Version of \"24\" with alternate production compared to later versions, and more Sunday Service Choir vocals not seen in later versions. It's unknown who else is on this version of the track. Previewed in the YZYTV documentary.", "length": "9.82", "fileDate": 16540416, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/38684eaf34866748bda595d068121772", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/38684eaf34866748bda595d068121772\", \"key\": \"24\", \"title\": \"24 [V8]\", \"artists\": \"(feat. Sunday Service Choir) (prod. <PERSON><PERSON>, <PERSON> & <PERSON>)\", \"aliases\": [\"24 Candles\", \"24 Hours\"], \"description\": \"Version of \\\"24\\\" with alternate production compared to later versions, and more Sunday Service Choir vocals not seen in later versions. It's unknown who else is on this version of the track. Previewed in the YZYTV documentary.\", \"date\": 16540416, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2f35722d01c1663c1b957410faeace09\", \"url\": \"https://api.pillowcase.su/api/download/2f35722d01c1663c1b957410faeace09\", \"size\": \"2.2 MB\", \"duration\": 9.82}", "aliases": ["24 Candles", "24 Hours"], "size": "2.2 MB"}, {"id": "24-153", "name": "24 [V8]", "artists": ["Sunday Service Choir"], "producers": ["Dem <PERSON>z", "<PERSON>", "<PERSON>"], "notes": "Version of \"24\" with alternate production compared to later versions, and more Sunday Service Choir vocals not seen in later versions. It's unknown who else is on this version of the track. Previewed in the YZYTV documentary.", "length": "62.55", "fileDate": 16540416, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/98e4f8919f90d2d888ce56ca8bbf67da", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/98e4f8919f90d2d888ce56ca8bbf67da\", \"key\": \"24\", \"title\": \"24 [V8]\", \"artists\": \"(feat. Sunday Service Choir) (prod. <PERSON><PERSON>, <PERSON> & <PERSON>)\", \"aliases\": [\"24 Candles\", \"24 Hours\"], \"description\": \"Version of \\\"24\\\" with alternate production compared to later versions, and more Sunday Service Choir vocals not seen in later versions. It's unknown who else is on this version of the track. Previewed in the YZYTV documentary.\", \"date\": 16540416, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8f3c9f414d7813f2c7e880ae7c523c0d\", \"url\": \"https://api.pillowcase.su/api/download/8f3c9f414d7813f2c7e880ae7c523c0d\", \"size\": \"3.05 MB\", \"duration\": 62.55}", "aliases": ["24 Candles", "24 Hours"], "size": "3.05 MB"}, {"id": "awakening-154", "name": "Awakening [V4]", "artists": [], "producers": ["E.VAX"], "notes": "OG Filename: EVAN_mix ref\nVictory \"Awakening\" reference track.", "length": "146.09", "fileDate": 16682976, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/955419b07cf92cc722a8a2a112eb157d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/955419b07cf92cc722a8a2a112eb157d\", \"key\": \"Awakening\", \"title\": \"Awakening [V4]\", \"artists\": \"(ref. <PERSON>) (prod. E.VAX)\", \"description\": \"OG Filename: EVAN_mix ref\\nVictory \\\"Awakening\\\" reference track.\", \"date\": 16682976, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c2df3cd476b5cf470ebd2723800086cf\", \"url\": \"https://api.pillowcase.su/api/download/c2df3cd476b5cf470ebd2723800086cf\", \"size\": \"4.38 MB\", \"duration\": 146.09}", "aliases": [], "size": "4.38 MB"}, {"id": "before-i-go", "name": "Before I Go", "artists": [], "producers": [], "notes": "OG Filename: Before I Go - 06.03.20 <PERSON><PERSON><PERSON> Re<PERSON> \"Before I Go\" reference track. Unknown if a version with <PERSON><PERSON><PERSON> vocals exists.", "length": "57.73", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/063281f97d75705f27a2a5f1aa4e7bdf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/063281f97d75705f27a2a5f1aa4e7bdf\", \"key\": \"Before I Go\", \"title\": \"Before I Go\", \"artists\": \"(ref. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Before I Go - 06.03.20 Kaycyy Ref\\nKayCyy \\\"Before I Go\\\" reference track. Unknown if a version with <PERSON><PERSON><PERSON> vocals exists.\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"71246cdf70ab1d937e5558b301d8121c\", \"url\": \"https://api.pillowcase.su/api/download/71246cdf70ab1d937e5558b301d8121c\", \"size\": \"2.97 MB\", \"duration\": 57.73}", "aliases": [], "size": "2.97 MB"}, {"id": "by-your-side", "name": "By Your Side", "artists": [], "producers": [], "notes": "OG Filename: By Your Side - 06.03.20 <PERSON><PERSON><PERSON> Re<PERSON> \"By Your Side\" reference track. Does not seem to be related to the DONDA [V1] track featuring <PERSON>ign. Unknown if a version with <PERSON><PERSON><PERSON> vocals exists.", "length": "60.92", "fileDate": 16962048, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/e9930003802e80401e222ebc5da981a9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e9930003802e80401e222ebc5da981a9\", \"key\": \"By Your Side\", \"title\": \"By Your Side\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: By Your Side - 06.03.20 Kaycyy Ref\\nKayCyy \\\"By Your Side\\\" reference track. Does not seem to be related to the DONDA [V1] track featuring <PERSON> Doll<PERSON> $ign. Unknown if a version with <PERSON><PERSON><PERSON> vocals exists.\", \"date\": 16962048, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"351a63843ea0e01287f31070a1675614\", \"url\": \"https://api.pillowcase.su/api/download/351a63843ea0e01287f31070a1675614\", \"size\": \"3.02 MB\", \"duration\": 60.92}", "aliases": [], "size": "3.02 MB"}, {"id": "blood-of-the-lamb", "name": "Blood Of The Lamb [V6]", "artists": [], "producers": [], "notes": "OG Filename: Blood of the Lamb - 06.18.20 Cons Verse\nConsequence reference track for \"Blood Of The Lamb\". Does not feature the adlibs seen in later versions of the song, along with featuring much simpler production. Has less than a minute of Consequence vocals.", "length": "312.65", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/d1d5dc94845d5fd8eb1a865239a6c247", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d1d5dc94845d5fd8eb1a865239a6c247\", \"key\": \"Blood Of The Lamb\", \"title\": \"Blood Of The Lamb [V6]\", \"artists\": \"(ref. Consequence)\", \"aliases\": [\"<PERSON><PERSON>\", \"They Were Overcome\"], \"description\": \"OG Filename: Blood of the Lamb - 06.18.20 Cons Verse\\nConsequence reference track for \\\"Blood Of The Lamb\\\". Does not feature the adlibs seen in later versions of the song, along with featuring much simpler production. Has less than a minute of Consequence vocals.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"eccaa6492d4ae3ea94080339a5896ed4\", \"url\": \"https://api.pillowcase.su/api/download/eccaa6492d4ae3ea94080339a5896ed4\", \"size\": \"7.05 MB\", \"duration\": 312.65}", "aliases": ["<PERSON><PERSON>", "They Were Overcome"], "size": "7.05 MB"}, {"id": "donda", "name": "<PERSON><PERSON> [V10]", "artists": ["Consequence", "Dem <PERSON>z"], "producers": ["E.VAX", "Dem <PERSON>z"], "notes": "Appears on every DONDA tracklist posted throughout July 2020. Finished version with no mumble. <PERSON> Conse<PERSON> doing some vocals on the track and <PERSON><PERSON> has backing vocals on the \"wash the face\" lines. Samples \"They Were Overcome\" by The Clark Sisters. Snippet first shared onto <PERSON><PERSON><PERSON>'s Twitter.", "length": "140.02", "fileDate": 15945120, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/a0f170ef7dcedf6cec7440bdfc480865", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a0f170ef7dcedf6cec7440bdfc480865\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V10]\", \"artists\": \"(feat. Conse<PERSON> & Dem Jointz) (prod. E.VAX & Dem Jointz)\", \"aliases\": [\"Blood Of The Lamb\", \"They Were Overcome\"], \"description\": \"Appears on every DONDA tracklist posted throughout July 2020. Finished version with no mumble. Has Consequence doing some vocals on the track and <PERSON><PERSON> has backing vocals on the \\\"wash the face\\\" lines. Samples \\\"They Were Overcome\\\" by The Clark Sisters. Snippet first shared onto <PERSON><PERSON><PERSON>'s Twitter.\", \"date\": 15945120, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ede181edb67c3b4b4d3340b0919b560d\", \"url\": \"https://api.pillowcase.su/api/download/ede181edb67c3b4b4d3340b0919b560d\", \"size\": \"4.28 MB\", \"duration\": 140.02}", "aliases": ["Blood Of The Lamb", "They Were Overcome"], "size": "4.28 MB"}, {"id": "despite", "name": "Despite", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Despite - 06.04.20 <PERSON><PERSON><PERSON> ref\n<PERSON><PERSON><PERSON><PERSON> \"Despite\" reference track. Recorded at the ranch. May have also been considered for <PERSON><PERSON><PERSON><PERSON>'s album From The Basement", "length": "14.84", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/41cba7e0669b54100b6599e5d16a1c88", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/41cba7e0669b54100b6599e5d16a1c88\", \"key\": \"Despite\", \"title\": \"Despite\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Despite - 06.04.20 Kaycyy ref\\nKayCyy \\\"Despite\\\" reference track. Recorded at the ranch. May have also been considered for <PERSON><PERSON><PERSON><PERSON>'s album From The Basement\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b4f01a99b9b57ccc74908f4e05a86bcb\", \"url\": \"https://api.pillowcase.su/api/download/b4f01a99b9b57ccc74908f4e05a86bcb\", \"size\": \"2.28 MB\", \"duration\": 14.84}", "aliases": [], "size": "2.28 MB"}, {"id": "despite-160", "name": "Despite", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Despite - 06.04.20 <PERSON><PERSON><PERSON> ref\n<PERSON><PERSON><PERSON><PERSON> \"Despite\" reference track. Recorded at the ranch. May have also been considered for <PERSON><PERSON><PERSON><PERSON>'s album From The Basement", "length": "13.58", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/dc7d0cb6cdd4446ba4f4f00c2de3d839", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dc7d0cb6cdd4446ba4f4f00c2de3d839\", \"key\": \"Despite\", \"title\": \"Despite\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Despite - 06.04.20 Kaycyy ref\\nKayCyy \\\"Despite\\\" reference track. Recorded at the ranch. May have also been considered for <PERSON><PERSON><PERSON><PERSON>'s album From The Basement\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4d11a62aeaceee51e31f4d893b91b7d1\", \"url\": \"https://api.pillowcase.su/api/download/4d11a62aeaceee51e31f4d893b91b7d1\", \"size\": \"2.26 MB\", \"duration\": 13.58}", "aliases": [], "size": "2.26 MB"}, {"id": "eternal-life-161", "name": "Eternal Life [V13]", "artists": ["Big Sean", "<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "Wheezy"], "notes": "OG Filename: Eternal Life - 07.06.20 Wheezy Sheck Temp\nVersion found on the \"With Child Continuous\" copy of <PERSON><PERSON> from July 23rd, 2020. Features worse mixing, weird tuning on <PERSON><PERSON>, and less Wheezy tags compared to the later July 19th version.", "length": "166.01", "fileDate": 16949952, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/79f8591bc7cb66c8d96a657005c2e2e7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/79f8591bc7cb66c8d96a657005c2e2e7\", \"key\": \"Eternal Life\", \"title\": \"Eternal Life [V13]\", \"artists\": \"(feat. <PERSON> & <PERSON><PERSON>) (prod. BoogzDaBeas<PERSON> & Wheezy)\", \"aliases\": [\"Ambitious\", \"Devil Weak\", \"The Devil Is Weak\", \"We Got Eternal Life\"], \"description\": \"OG Filename: Eternal Life - 07.06.20 Wheezy Sheck Temp\\nVersion found on the \\\"With Child Continuous\\\" copy of <PERSON><PERSON> from July 23rd, 2020. Features worse mixing, weird tuning on <PERSON><PERSON>, and less Wheezy tags compared to the later July 19th version.\", \"date\": 16949952, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"b82e32966f21f94ba554de26cf51890c\", \"url\": \"https://api.pillowcase.su/api/download/b82e32966f21f94ba554de26cf51890c\", \"size\": \"4.7 MB\", \"duration\": 166.01}", "aliases": ["Ambitious", "Devil Weak", "The Devil Is Weak", "We Got Eternal Life"], "size": "4.7 MB"}, {"id": "eternal-rest", "name": "Eternal Rest [V1]", "artists": ["Sunday Service Choir"], "producers": [], "notes": "Planned as a solo Sunday Service Choir interlude similar to \"Every Hour\". The same version was also used on 2020 DONDA according to a leaker. A reworked version was released on the Sunday Service Choir's 2020 EP Emmanuel. Was played at <PERSON>ny<PERSON>'s Nebuchadnezzar opera. Snippet from North West's birthday party was posted by <PERSON><PERSON><PERSON>. Leaked in full with the July 23rd, 2020 copy of Donda: With Child leaked.", "length": "132.34", "fileDate": 16955136, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/c997fa649edfdc008fe37d23f5285770", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c997fa649edfdc008fe37d23f5285770\", \"key\": \"Eternal Rest\", \"title\": \"Eternal Rest [V1]\", \"artists\": \"(feat. Sunday Service Choir)\", \"aliases\": [\"Requiem Aeterna\", \"The Living Word\"], \"description\": \"Planned as a solo Sunday Service Choir interlude similar to \\\"Every Hour\\\". The same version was also used on 2020 DONDA according to a leaker. A reworked version was released on the Sunday Service Choir's 2020 EP Emmanuel. Was played at Kanye's Nebuchadnezzar opera. Snippet from North West's birthday party was posted by <PERSON><PERSON><PERSON>. Leaked in full with the July 23rd, 2020 copy of Don<PERSON>: With Child leaked.\", \"date\": 16955136, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"fc7c3335dad575a15a87cdef5308859a\", \"url\": \"https://api.pillowcase.su/api/download/fc7c3335dad575a15a87cdef5308859a\", \"size\": \"4.16 MB\", \"duration\": 132.34}", "aliases": ["Requiem Aeterna", "The Living Word"], "size": "4.16 MB"}, {"id": "everything-163", "name": "Everything [V5]", "artists": [], "producers": [], "notes": "<PERSON> \"Everything\" reference vocals for the <PERSON><PERSON><PERSON> verse. Likely recorded in May 2020 when she was at the Wyoming ranch.", "length": "224.3", "fileDate": 16624224, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/c22bae3bdfa34492fd4c8082d5ed3600", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c22bae3bdfa34492fd4c8082d5ed3600\", \"key\": \"Everything\", \"title\": \"Everything [V5]\", \"artists\": \"(ref. <PERSON>)\", \"description\": \"Victory \\\"Everything\\\" reference vocals for the <PERSON><PERSON><PERSON> verse. Likely recorded in May 2020 when she was at the Wyoming ranch.\", \"date\": 16624224, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"602acb6d9d7e306b4353b5fb008128a0\", \"url\": \"https://api.pillowcase.su/api/download/602acb6d9d7e306b4353b5fb008128a0\", \"size\": \"5.63 MB\", \"duration\": 224.3}", "aliases": [], "size": "5.63 MB"}, {"id": "everything-164", "name": "Everything [V7]", "artists": ["<PERSON><PERSON>"], "producers": ["???", "E.VAX"], "notes": "OG Filename: EVAN_mix\nVersion of \"Everything\" with additional production from E.VAX. Has electric guitar and drums.", "length": "227.37", "fileDate": 16900704, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/6e6ed78880c86aa17d73863642fcfa52", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6e6ed78880c86aa17d73863642fcfa52\", \"key\": \"Everything\", \"title\": \"Everything [V7]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON>) (prod. ??? & E.VAX)\", \"description\": \"OG Filename: EVAN_mix\\nVersion of \\\"Everything\\\" with additional production from E.VAX. Has electric guitar and drums.\", \"date\": 16900704, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b635c09d6e6fdfcc150daa8c8ec8d795\", \"url\": \"https://api.pillowcase.su/api/download/b635c09d6e6fdfcc150daa8c8ec8d795\", \"size\": \"5.68 MB\", \"duration\": 227.37}", "aliases": [], "size": "5.68 MB"}, {"id": "fighting-fires-165", "name": "Fighting Fires [V10]", "artists": ["Sunday Service Choir"], "producers": ["BoogzDaBeast", "FnZ"], "notes": "May 8th version of \"Fighting Fires\". Features different production than every other version of the song. Includes 2:39 of audio followed by silence until the end of the 4:33 track.", "length": "272.83", "fileDate": 16953408, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/afb4702ba9ae48703dc2eac678fd1dcf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/afb4702ba9ae48703dc2eac678fd1dcf\", \"key\": \"Fighting Fires\", \"title\": \"Fighting Fires [V10]\", \"artists\": \"(ref. <PERSON> & <PERSON>) (feat. Sunday Service Choir) (prod. BoogzDaBeast & FnZ)\", \"aliases\": [\"Faithful\", \"Fightin Fire\", \"My Soul\"], \"description\": \"May 8th version of \\\"Fighting Fires\\\". Features different production than every other version of the song. Includes 2:39 of audio followed by silence until the end of the 4:33 track.\", \"date\": 16953408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c8bab83f19fa356b403ba1958ad4a396\", \"url\": \"https://api.pillowcase.su/api/download/c8bab83f19fa356b403ba1958ad4a396\", \"size\": \"6.41 MB\", \"duration\": 272.83}", "aliases": ["Faithful", "Fightin Fire", "My Soul"], "size": "6.41 MB"}, {"id": "future-bounce-166", "name": "Future Bounce [V11]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "<PERSON> wrote the \"Future Sounds\" verse that <PERSON> later recorded over. Likely written/recorded in May 2020. The original lyrics are as follows:\n\"I can see the future it's looking like we leveled through the sky,\nI can't wait to live in Glory of eternal Paradise\nWon't you take my will\nI'm ready to surrender my whole life\nMight as well turn up now\nHe gon' pop up unnanounced\nHear the trumpets\nDo you like the way it sounds?\"\nSnippet posted by Victory Jan 10th 2025.", "length": "66.31", "fileDate": 17364672, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/9a288a869b09738782945aa6c1fff369", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9a288a869b09738782945aa6c1fff369\", \"key\": \"Future Bounce\", \"title\": \"Future Bounce [V11]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON>zDaBeas<PERSON>)\", \"aliases\": [\"Future\", \"Future Of Sound\", \"Future Sounds\", \"TELEKINESIS\", \"Ultrasounds\"], \"description\": \"<PERSON> wrote the \\\"Future Sounds\\\" verse that <PERSON> later recorded over. Likely written/recorded in May 2020. The original lyrics are as follows:\\n\\\"I can see the future it's looking like we leveled through the sky,\\nI can't wait to live in Glory of eternal Paradise\\nWon't you take my will\\nI'm ready to surrender my whole life\\nMight as well turn up now\\nHe gon' pop up unnanounced\\nHear the trumpets\\nDo you like the way it sounds?\\\"\\nSnippet posted by <PERSON> Jan 10th 2025.\", \"date\": 17364672, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"4b704a7a8a4cf2d1a1cc1bcf1376abc5\", \"url\": \"https://api.pillowcase.su/api/download/4b704a7a8a4cf2d1a1cc1bcf1376abc5\", \"size\": \"3.11 MB\", \"duration\": 66.31}", "aliases": ["Future", "Future Of Sound", "Future Sounds", "TELEKINESIS", "Ultrasounds"], "size": "3.11 MB"}, {"id": "future-bounce-167", "name": "Future Bounce [V13]", "artists": ["<PERSON>", "Victory"], "producers": ["BoogzDaBeast"], "notes": "Version of \"Future Bounce\" with new <PERSON><PERSON><PERSON> vocals, as well as <PERSON>. Has no loud horn beat switch.", "length": "122.98", "fileDate": 16282080, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/f247a52d374482af6a6d83fd9588ad72", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f247a52d374482af6a6d83fd9588ad72\", \"key\": \"Future Bounce\", \"title\": \"<PERSON> Bounce [V13]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>DaBeas<PERSON>)\", \"aliases\": [\"Future\", \"Future Of Sound\", \"Future Sounds\", \"TELEKINESIS\", \"Ultrasounds\"], \"description\": \"Version of \\\"Future Bounce\\\" with new <PERSON><PERSON><PERSON> vocals, as well as <PERSON>. Has no loud horn beat switch.\", \"date\": 16282080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"641a7533ebd5ad0f63233fee6f6ef6e9\", \"url\": \"https://api.pillowcase.su/api/download/641a7533ebd5ad0f63233fee6f6ef6e9\", \"size\": \"4.01 MB\", \"duration\": 122.98}", "aliases": ["Future", "Future Of Sound", "Future Sounds", "TELEKINESIS", "Ultrasounds"], "size": "4.01 MB"}, {"id": "future-sounds", "name": "Future Sounds [V15]", "artists": ["<PERSON>", "Victory"], "producers": ["E.VAX", "BoogzDaBeast"], "notes": "OG Filename: EVAN_mix\nVersion of \"Future Bounce\" with alternate production from E.VAX. Likely from God's Country as it contains <PERSON> vocals not seen in any DONDA versions of the song.", "length": "153.25", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/6534551e43787b9544f3fd5d3b7b305b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6534551e43787b9544f3fd5d3b7b305b\", \"key\": \"Future Sounds\", \"title\": \"Future Sounds [V15]\", \"artists\": \"(feat. <PERSON>) (prod. E.VAX & BoogzDaBeast)\", \"aliases\": [\"Future\", \"Future Bounce\", \"Future Of Sound\", \"TELEKINESIS\", \"Ultrasounds\"], \"description\": \"OG Filename: EVAN_mix\\nVersion of \\\"Future Bounce\\\" with alternate production from E.<PERSON>X. Likely from God's Country as it contains Victory vocals not seen in any DONDA versions of the song.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e3b5e69c3f78bd949843baf759f5588f\", \"url\": \"https://api.pillowcase.su/api/download/e3b5e69c3f78bd949843baf759f5588f\", \"size\": \"4.5 MB\", \"duration\": 153.25}", "aliases": ["Future", "Future Bounce", "Future Of Sound", "TELEKINESIS", "Ultrasounds"], "size": "4.5 MB"}, {"id": "god-s-country", "name": "God's Country [V5]", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON>", "30 Roc", "Zentachi", "E.VAX"], "notes": "Another E.VAX version of \"God's Country\" with additional production. There is no official bounce for this, only the add stems are currently avaliable.", "length": "220.8", "fileDate": 16950816, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/057651c49c1ff4b84b121e4034ba725e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/057651c49c1ff4b84b121e4034ba725e\", \"key\": \"God's Country\", \"title\": \"God's Country [V5]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON>, 30 <PERSON><PERSON>, Zen<PERSON>chi & E.VAX)\", \"description\": \"Another E.VAX version of \\\"God's Country\\\" with additional production. There is no official bounce for this, only the add stems are currently avaliable.\", \"date\": 16950816, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e8a6cbe8676c12be1a189eeb94d00bdb\", \"url\": \"https://api.pillowcase.su/api/download/e8a6cbe8676c12be1a189eeb94d00bdb\", \"size\": \"5.58 MB\", \"duration\": 220.8}", "aliases": [], "size": "5.58 MB"}, {"id": "god-s-country-170", "name": "God's Country [V6]", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON>", "30 Roc", "Zentachi", "E.VAX"], "notes": "OG Filename: EVAN_mix\nVersion of \"God's Country\" with additional production from E.VAX exists. Has extra Travis vocals compared to later versions, with a guitar beatswitch halfway through the song.", "length": "220.8", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/6873d8e60add1e95544cd233d6c0340c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6873d8e60add1e95544cd233d6c0340c\", \"key\": \"God's Country\", \"title\": \"God's Country [V6]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON>, 30 <PERSON><PERSON>, Zen<PERSON>chi & E.VAX)\", \"description\": \"OG Filename: EVAN_mix\\nVersion of \\\"God's Country\\\" with additional production from E.VAX exists. Has extra <PERSON> vocals compared to later versions, with a guitar beatswitch halfway through the song.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"9419b482d16d9bbb0d007e6837ec0ee1\", \"url\": \"https://api.pillowcase.su/api/download/9419b482d16d9bbb0d007e6837ec0ee1\", \"size\": \"5.58 MB\", \"duration\": 220.8}", "aliases": [], "size": "5.58 MB"}, {"id": "god-s-country-171", "name": "God's Country [V7]", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON>", "30 Roc", "Zentachi"], "notes": "OG Filename: Gods Country - 07.05.20 Bentley Edit Fix 1\nFeatured on every DONDA tracklist posted throughout July 2020. Contains some mumble vocals from both <PERSON><PERSON><PERSON> and <PERSON>. This is the version of the song featured on the visual album, although the visual album cuts the song early. Samples \"DEZ WRIGHT Melody Loop Must Be Leaving 158 C#m\". Leaked in full on September 10th, 2023 after the visual album rip was groupbought months before.", "length": "186.21", "fileDate": 16943040, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/e34655a36f8cf3713d053033a9d92006", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e34655a36f8cf3713d053033a9d92006\", \"key\": \"God's Country\", \"title\": \"God's Country [V7]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON>, 30 Roc & Zen<PERSON>chi)\", \"description\": \"OG Filename: Gods Country - 07.05.20 Bentley Edit Fix 1\\nFeatured on every DONDA tracklist posted throughout July 2020. Contains some mumble vocals from both <PERSON><PERSON><PERSON> and <PERSON>. This is the version of the song featured on the visual album, although the visual album cuts the song early. Samples \\\"DEZ WRIGHT Melody Loop Must Be Leaving 158 C#m\\\". Leaked in full on September 10th, 2023 after the visual album rip was groupbought months before.\", \"date\": 16943040, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d09f2a65ffd5e80e0d4f1b47e95aae9a\", \"url\": \"https://api.pillowcase.su/api/download/d09f2a65ffd5e80e0d4f1b47e95aae9a\", \"size\": \"5.02 MB\", \"duration\": 186.21}", "aliases": [], "size": "5.02 MB"}, {"id": "grace-n-mercy", "name": "✨ Grace N Mercy [V1]", "artists": ["<PERSON><PERSON>"], "producers": ["Dem <PERSON>z"], "notes": "OG Filename: GRACE N MERCY 05.20...\n<PERSON><PERSON>z rework of the mumble JESUS IS KING: The Dr. Dre Version \"Use This Gospel\" verse. Since so much of the verse was mumble, <PERSON><PERSON><PERSON> is on the majority of the song. Has a <PERSON><PERSON> hook and a beatswitch. Originally leaked in mono December 2nd, 2022, before later leaking in stereo.", "length": "150.26", "fileDate": 16700256, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/354efcf3c5af76c617c98b367472b517", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/354efcf3c5af76c617c98b367472b517\", \"key\": \"Grace N Mercy\", \"title\": \"\\u2728 <PERSON> N Mercy [V1]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"<PERSON> and Mercy\"], \"description\": \"OG Filename: GRACE N MERCY 05.20...\\nDem Jointz rework of the mumble JESUS IS KING: The Dr. Dre Version \\\"Use This Gospel\\\" verse. Since so much of the verse was mumble, <PERSON><PERSON><PERSON> is on the majority of the song. Has a <PERSON><PERSON> hook and a beatswitch. Originally leaked in mono December 2nd, 2022, before later leaking in stereo.\", \"date\": 16700256, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e15d88e4863bf7794ae947a6b40a84da\", \"url\": \"https://api.pillowcase.su/api/download/e15d88e4863bf7794ae947a6b40a84da\", \"size\": \"4.45 MB\", \"duration\": 150.26}", "aliases": ["Grace and Mercy"], "size": "4.45 MB"}, {"id": "he-gave-it-all-173", "name": "He Gave It All [V4]", "artists": ["Boyz II Men", "<PERSON>"], "producers": ["BoogzDaBeast"], "notes": "OG Filename: He Gave It All - 05.10.20 Kaycyy x BIIM x KG\nVersion of \"He Gave It All\" with a saxaphone outro from <PERSON>, background vocals from Boyz II Men, and <PERSON><PERSON><PERSON><PERSON> reference vocals.", "length": "156.58", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/9d4e33893c400e6740cd79dfa78eb577", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9d4e33893c400e6740cd79dfa78eb577\", \"key\": \"He Gave It All\", \"title\": \"He Gave It All [V4]\", \"artists\": \"(ref. <PERSON>) (feat. Boyz II Men & Kenny G) (prod. BoogzDaBeast)\", \"description\": \"OG Filename: He Gave It All - 05.10.20 Kaycyy x BIIM x KG\\nVersion of \\\"He Gave It All\\\" with a saxaphone outro from <PERSON>, background vocals from <PERSON>z II Men, and <PERSON><PERSON><PERSON><PERSON> reference vocals.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8ddbe1b04a76e003487cf95962b489bd\", \"url\": \"https://api.pillowcase.su/api/download/8ddbe1b04a76e003487cf95962b489bd\", \"size\": \"4.55 MB\", \"duration\": 156.58}", "aliases": [], "size": "4.55 MB"}, {"id": "i-feel-terrific-174", "name": "I Feel Terrific [V10]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: I Feel Terrific - 05.09.20 Ye Notes Clean\nEarly May version of \"I Feel Terrific\". Has different drums and 808s compared to other versions.", "length": "111.17", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/a53fa9929d5843582faa19b7a6b25cd4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a53fa9929d5843582faa19b7a6b25cd4\", \"key\": \"I Feel Terrific\", \"title\": \"I Feel Terrific [V10]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: I Feel Terrific - 05.09.20 Ye Notes Clean\\nEarly May version of \\\"I Feel Terrific\\\". Has different drums and 808s compared to other versions.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"43c4604d3ae91fea526f93a52fa6a402\", \"url\": \"https://api.pillowcase.su/api/download/43c4604d3ae91fea526f93a52fa6a402\", \"size\": \"3.82 MB\", \"duration\": 111.17}", "aliases": [], "size": "3.82 MB"}, {"id": "i-feel-terrific-175", "name": "I Feel Terrific [V11]", "artists": [], "producers": ["<PERSON>"], "notes": "Seen on multiple God's Country and DONDA tracklists. According to <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> made the track after hearing an unreleased Kendrick track played by Dr<PERSON> <PERSON><PERSON>, which had a high pitched vocal. Has mumble and some finished lines. Vocals range from normal to high pitched screeching. Was in <PERSON> Dash's YZYTV WY Documentary.", "length": "103.7", "fileDate": 16509312, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/68166985bab4b36786184539c76d3d52", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/68166985bab4b36786184539c76d3d52\", \"key\": \"I Feel Terrific\", \"title\": \"I Feel Terrific [V11]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"Seen on multiple God's Country and DONDA tracklists. According to <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> made the track after hearing an unreleased Kendrick track played by <PERSON><PERSON> <PERSON><PERSON>, which had a high pitched vocal. Has mumble and some finished lines. Vocals range from normal to high pitched screeching. Was in <PERSON>'s YZYTV WY Documentary.\", \"date\": 16509312, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"de31217f338c3f73761e1b66ff5b544e\", \"url\": \"https://api.pillowcase.su/api/download/de31217f338c3f73761e1b66ff5b544e\", \"size\": \"3.7 MB\", \"duration\": 103.7}", "aliases": [], "size": "3.7 MB"}, {"id": "i-feel-terrific-176", "name": "I Feel Terrific [V12]", "artists": [], "producers": ["E.VAX", "<PERSON>"], "notes": "OG Filename: EVAN_mix\nVersion of \"I Feel Terrific\", with additional production from E.VAX.", "length": "102.22", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/f1ce504fef2714d436b22d9fb33abaa5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f1ce504fef2714d436b22d9fb33abaa5\", \"key\": \"I Feel Terrific\", \"title\": \"I Feel Terrific [V12]\", \"artists\": \"(prod. <PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: EVAN_mix\\nVersion of \\\"I Feel Terrific\\\", with additional production from E.VAX.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"36b85d0801fdb568ea5ed05714ae1aee\", \"url\": \"https://api.pillowcase.su/api/download/36b85d0801fdb568ea5ed05714ae1aee\", \"size\": \"3.68 MB\", \"duration\": 102.22}", "aliases": [], "size": "3.68 MB"}, {"id": "i-feel-terrific-177", "name": "I Feel Terrific [V13]", "artists": [], "producers": ["E.VAX", "<PERSON>"], "notes": "OG Filename: EVAN_mix\nAnother version of \"I Feel Terrific\", with additional production from E.VAX.", "length": "111.11", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/26fdc335e155866bd2201bad81b75565", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/26fdc335e155866bd2201bad81b75565\", \"key\": \"I Feel Terrific\", \"title\": \"I Feel Terrific [V13]\", \"artists\": \"(prod. <PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: EVAN_mix\\nAnother version of \\\"I Feel Terrific\\\", with additional production from E.VAX.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5a024ab0dcd0b1a02789611ae7b4abab\", \"url\": \"https://api.pillowcase.su/api/download/5a024ab0dcd0b1a02789611ae7b4abab\", \"size\": \"3.82 MB\", \"duration\": 111.11}", "aliases": [], "size": "3.82 MB"}, {"id": "i-feel-terrific-178", "name": "I Feel Terrific [V14]", "artists": [], "producers": ["E.VAX", "<PERSON>"], "notes": "OG Filename: I Feel Terrific - Evan - 07.14.20 Ye Edits\nSlightly shorter version of \"I Feel Terrific\", with slight production differences", "length": "96.72", "fileDate": 16530048, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/2eded21b8a63ba9356aa140cb7d9c598", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2eded21b8a63ba9356aa140cb7d9c598\", \"key\": \"I Feel Terrific\", \"title\": \"I Feel Terrific [V14]\", \"artists\": \"(prod. <PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: I Feel Terrific - Evan - 07.14.20 Ye Edits\\nSlightly shorter version of \\\"I Feel Terrific\\\", with slight production differences\", \"date\": 16530048, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6b5ac4a808dca59d6c2ae1a0dd1819f1\", \"url\": \"https://api.pillowcase.su/api/download/6b5ac4a808dca59d6c2ae1a0dd1819f1\", \"size\": \"3.59 MB\", \"duration\": 96.72}", "aliases": [], "size": "3.59 MB"}, {"id": "in-god-s-country", "name": "In God's Country [V5]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: In Gods Country 83 - 06.01.20 Shorter\nFile seen in the list posted by <PERSON><PERSON><PERSON> to Twitter. Samples \"Something To Talk About\" by JAMESDAVIS.", "length": "115.77", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/6d5f0b375a2891f9466fcc8633b93475", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6d5f0b375a2891f9466fcc8633b93475\", \"key\": \"In God's Country\", \"title\": \"In God's Country [V5]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON>zDaBeas<PERSON>) \", \"aliases\": [\"Work It Out\", \"God's Country\"], \"description\": \"OG Filename: In Gods Country 83 - 06.01.20 Shorter\\nFile seen in the list posted by Kanye to Twitter. Samples \\\"Something To Talk About\\\" by JAMESDAVIS.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e69df0bc4163f5f6d2d27b2326d3e82f\", \"url\": \"https://api.pillowcase.su/api/download/e69df0bc4163f5f6d2d27b2326d3e82f\", \"size\": \"3.9 MB\", \"duration\": 115.77}", "aliases": ["Work It Out", "God's Country"], "size": "3.9 MB"}, {"id": "in-god-s-country-180", "name": "In God's Country [V7]", "artists": [], "producers": ["BoogzDaBeast", "Israel Boyd"], "notes": "OG Filename: In Gods Country - 06.04.20 Israel Drums <PERSON><PERSON>\nHas drums from Israel Boyd.", "length": "138.86", "fileDate": 16956864, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/5a95b0e2a0e67c7bdca05a29299e2979", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5a95b0e2a0e67c7bdca05a29299e2979\", \"key\": \"In God's Country\", \"title\": \"In God's Country [V7]\", \"artists\": \"(prod. BoogzDaBeast & Israel Boyd) \", \"aliases\": [\"Work It Out\", \"God's Country\"], \"description\": \"OG Filename: In Gods Country - 06.04.20 Israel Drums Ref NoYe\\nHas drums from Israel Boyd.\", \"date\": 16956864, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5c8e18e93203a94e2ae72edaf65c206a\", \"url\": \"https://api.pillowcase.su/api/download/5c8e18e93203a94e2ae72edaf65c206a\", \"size\": \"4.27 MB\", \"duration\": 138.86}", "aliases": ["Work It Out", "God's Country"], "size": "4.27 MB"}, {"id": "jas", "name": "<PERSON><PERSON>", "artists": [], "producers": ["SHŌLZ"], "notes": "OG Filename: <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>_<PERSON> Freestyle Ref \nRough freestyle, which the exact era of is unknown, but it might be from May 2020 judging from \"May\" in the filename and the fact <PERSON><PERSON><PERSON><PERSON> also worked with SHŌLZ during this time.", "length": "147.72", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/92cc6b9dfd153b35f6955410d2734745", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/92cc6b9dfd153b35f6955410d2734745\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON>\", \"artists\": \"(prod. SH\\u014cLZ)\", \"description\": \"OG Filename: <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON> May_Ye Freestyle Ref \\nRough freestyle, which the exact era of is unknown, but it might be from May 2020 judging from \\\"May\\\" in the filename and the fact <PERSON><PERSON><PERSON><PERSON> also worked with <PERSON>\\u014cLZ during this time.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"807bbfcb9e649554864ad5b2ed81d430\", \"url\": \"https://api.pillowcase.su/api/download/807bbfcb9e649554864ad5b2ed81d430\", \"size\": \"4.41 MB\", \"duration\": 147.72}", "aliases": [], "size": "4.41 MB"}, {"id": "keep-my-spirit-alive-182", "name": "Keep My Spirit Alive [V18]", "artists": ["Kay<PERSON>y<PERSON>"], "producers": ["E.VAX"], "notes": "OG Filename: EVAN_mix\nVersion of \"Keep My Spirit Alive\" with production from E.VAX. KayCyy hook only, and open verses likely meant for <PERSON><PERSON><PERSON>.", "length": "183.7", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/40204d2c48d231a96efac32d7aa16e89", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/40204d2c48d231a96efac32d7aa16e89\", \"key\": \"Keep My Spirit Alive\", \"title\": \"Keep My Spirit Alive [V18]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. E.VAX)\", \"aliases\": [\"Keep Our Spirit Alive\"], \"description\": \"OG Filename: EVAN_mix\\nVersion of \\\"Keep My Spirit Alive\\\" with production from E.VAX. KayCyy hook only, and open verses likely meant for <PERSON><PERSON><PERSON>.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"7b42d65a5bdde813ae92784340d19fec\", \"url\": \"https://api.pillowcase.su/api/download/7b42d65a5bdde813ae92784340d19fec\", \"size\": \"4.98 MB\", \"duration\": 183.7}", "aliases": ["Keep Our Spirit Alive"], "size": "4.98 MB"}, {"id": "let-the-spirit-go-wild", "name": "Let The Spirit Go Wild [V3]", "artists": [], "producers": ["FnZ", "Styalz <PERSON>"], "notes": "OG Filename: 200419 Let The Spirit Go Wild\nA rough edit of the freestyle with chaotic layering of various vocal takes.", "length": "106.74", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/4ac0b2b5992de4761a3cbca4bf50e87d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4ac0b2b5992de4761a3cbca4bf50e87d\", \"key\": \"Let The Spirit Go Wild\", \"title\": \"Let The Spirit Go Wild [V3]\", \"artists\": \"(prod. FnZ & Styalz Fuego)\", \"description\": \"OG Filename: 200419 Let The Spirit Go Wild\\nA rough edit of the freestyle with chaotic layering of various vocal takes.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bedaf28702e07dd774e4e6c4ab4fe046\", \"url\": \"https://api.pillowcase.su/api/download/bedaf28702e07dd774e4e6c4ab4fe046\", \"size\": \"3.75 MB\", \"duration\": 106.74}", "aliases": [], "size": "3.75 MB"}, {"id": "let-the-spirit-go-wild-184", "name": "Let The Spirit Go Wild [V5]", "artists": ["EP Da Hellcat"], "producers": ["FnZ", "RONNY J", "Staylz Fuego"], "notes": "OG Filename: Let The Spirit Go Wild - 07.13.20 RJ x EP\nVersion of \"Let The Spirit Go Wild\" with RONNY J production, but only featuring the EP (of Abstract Mindstate) verse and <PERSON><PERSON><PERSON> hook.", "length": "110.96", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/e244bcba64d7a9a13cb7bf7d957c43d1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e244bcba64d7a9a13cb7bf7d957c43d1\", \"key\": \"Let The Spirit Go Wild\", \"title\": \"Let The Spirit Go Wild [V5]\", \"artists\": \"(feat. EP Da Hellcat) (prod. <PERSON><PERSON><PERSON>, RONNY J & Staylz Fuego)\", \"description\": \"OG Filename: Let The Spirit Go Wild - 07.13.20 RJ x EP\\nVersion of \\\"Let The Spirit Go Wild\\\" with RONNY J production, but only featuring the EP (of Abstract Mindstate) verse and <PERSON><PERSON><PERSON> hook.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"17f2d8f4db67d9261c06ec624312978c\", \"url\": \"https://api.pillowcase.su/api/download/17f2d8f4db67d9261c06ec624312978c\", \"size\": \"3.82 MB\", \"duration\": 110.96}", "aliases": [], "size": "3.82 MB"}, {"id": "let-the-spirit-go-wild-185", "name": "⭐ Let The Spirit Go Wild [V6]", "artists": ["Abstract Mindstate"], "producers": ["FnZ", "RONNY J", "Styalz <PERSON>"], "notes": "OG Filename: Let The Spirit Go Wild - 07.16.20 RJ\nMid-July version, seen being recorded by <PERSON><PERSON><PERSON> on the intro to the first episode of jeen-yuhs. Contains less mumble than other versions, and additional production from RONNY J. Has verses from both Abstract Mindstate members. An edit of the song, adding on Sunday Service Choir vocals, was leaked on September 11th, 2023, and the full OG File leaked on September 17th, 2023.", "length": "195.58", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/059139af20cc745f0eb3e89247456f24", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/059139af20cc745f0eb3e89247456f24\", \"key\": \"Let The Spirit Go Wild\", \"title\": \"\\u2b50 Let The Spirit Go Wild [V6]\", \"artists\": \"(feat. Abstract Mindstate) (prod. <PERSON>n<PERSON>, RONNY J & Styalz Fuego)\", \"description\": \"OG Filename: Let The Spirit Go Wild - 07.16.20 RJ\\nMid-July version, seen being recorded by <PERSON><PERSON><PERSON> on the intro to the first episode of jeen-yuhs. Contains less mumble than other versions, and additional production from RONNY J. Has verses from both Abstract Mindstate members. An edit of the song, adding on Sunday Service Choir vocals, was leaked on September 11th, 2023, and the full OG File leaked on September 17th, 2023.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3c70c276dc843f41a4544e31a0c6d891\", \"url\": \"https://api.pillowcase.su/api/download/3c70c276dc843f41a4544e31a0c6d891\", \"size\": \"5.17 MB\", \"duration\": 195.58}", "aliases": [], "size": "5.17 MB"}, {"id": "life-of-the-party-186", "name": "Life Of The Party [V3]", "artists": [], "producers": ["AllDay"], "notes": "OG Filename: Life Of The Party - 200504 Freestyle, Life Of The Party - 05.04.20 Barn Freestyle\nFilename on a May 9th playlist/album copy. Another freestyle for \"Life Of The Party.\"", "length": "217.81", "fileDate": 16954272, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/1bbbe6aa04ec64f838acbf85b7459845", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1bbbe6aa04ec64f838acbf85b7459845\", \"key\": \"Life Of The Party\", \"title\": \"Life Of The Party [V3]\", \"artists\": \"(prod. AllDay)\", \"description\": \"OG Filename: Life Of The Party - 200504 Freestyle, Life Of The Party - 05.04.20 Barn Freestyle\\nFilename on a May 9th playlist/album copy. Another freestyle for \\\"Life Of The Party.\\\"\", \"date\": 16954272, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"91e5c37b3655ec98beac61b8aad992a3\", \"url\": \"https://api.pillowcase.su/api/download/91e5c37b3655ec98beac61b8aad992a3\", \"size\": \"5.53 MB\", \"duration\": 217.81}", "aliases": [], "size": "5.53 MB"}, {"id": "lord-i-need-you-187", "name": "Lord I Need You [V12]", "artists": [], "producers": ["E.VAX", "BoogzDaBeast"], "notes": "OG Filename: <PERSON>VA<PERSON>_mix\nHas the original mumble <PERSON><PERSON><PERSON> vocals.", "length": "104.73", "fileDate": 16949952, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/8f562bd8685b9b738ca32afe421be93e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8f562bd8685b9b738ca32afe421be93e\", \"key\": \"Lord I Need You\", \"title\": \"Lord I Need You [V12]\", \"artists\": \"(prod. E.<PERSON> & BoogzDaBeast)\", \"aliases\": [\"Lord\", \"Lord I Need You\", \"Lord I Need You Right Now\", \"Lord I Need You To Wrap Your Arms Around Me\", \"Wrap Your Arms Around Me\"], \"description\": \"OG Filename: EVAN_mix\\nHas the original mumble Kanye vocals.\", \"date\": 16949952, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d8415a096c9da0ba08ba29344181cf40\", \"url\": \"https://api.pillowcase.su/api/download/d8415a096c9da0ba08ba29344181cf40\", \"size\": \"3.72 MB\", \"duration\": 104.73}", "aliases": ["Lord", "Lord I Need You", "Lord I Need You Right Now", "Lord I Need You To Wrap Your Arms Around Me", "Wrap Your Arms Around Me"], "size": "3.72 MB"}, {"id": "man-of-god", "name": "Man Of God", "artists": [], "producers": [], "notes": "OG Filename: Man Of God - 06.03.20 Kay<PERSON>y Ref\n<PERSON>y reference track.", "length": "107.17", "fileDate": 16365888, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/209302d33d8c43232cce0ab744876b7b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/209302d33d8c43232cce0ab744876b7b\", \"key\": \"Man Of God\", \"title\": \"Man Of God\", \"artists\": \"(ref. <PERSON><PERSON>)\", \"description\": \"OG Filename: Man Of God - 06.03.20 <PERSON><PERSON>y Ref\\nKayCyy reference track.\", \"date\": 16365888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"51fb29344f4e3bd1919c424d97094ada\", \"url\": \"https://api.pillowcase.su/api/download/51fb29344f4e3bd1919c424d97094ada\", \"size\": \"3.76 MB\", \"duration\": 107.17}", "aliases": [], "size": "3.76 MB"}, {"id": "new-body", "name": "New Body [V31] ", "artists": ["Ty Dolla $ign", "<PERSON><PERSON>"], "producers": ["BoogzDaBeast", "RONNY J", "Kanye West"], "notes": "OG Filename: New Body - 07.10.20 Ye Edit\nOn several tracklists throughout late June/July 2020. Has the <PERSON> and <PERSON><PERSON><PERSON> vocals seen on the JESUS IS KING version of the song, plus a new Nicki verse. Oddly enough, the \"don't be acting like you don't know nobody\" line from <PERSON>'s 2018 version of the hook is spliced in with his 2019 hook. Has new drums, likely done by <PERSON><PERSON><PERSON>.", "length": "242.59", "fileDate": 16886880, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/3418aa29a96c9fec6d3701f03f75c3d3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3418aa29a96c9fec6d3701f03f75c3d3\", \"key\": \"New Body\", \"title\": \"New Body [V31] \", \"artists\": \"(feat. <PERSON>gn & <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, RONNY J & Ka<PERSON><PERSON>)\", \"aliases\": [\"Can't Wait To See Your New Body\"], \"description\": \"OG Filename: New Body - 07.10.20 Ye Edit\\nOn several tracklists throughout late June/July 2020. Has the <PERSON> and <PERSON><PERSON><PERSON> vocals seen on the JESUS IS KING version of the song, plus a new <PERSON><PERSON> verse. Oddly enough, the \\\"don't be acting like you don't know nobody\\\" line from <PERSON>'s 2018 version of the hook is spliced in with his 2019 hook. Has new drums, likely done by <PERSON><PERSON><PERSON>.\", \"date\": 16886880, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bd6e266464237f7ffe9b217a15465dd8\", \"url\": \"https://api.pillowcase.su/api/download/bd6e266464237f7ffe9b217a15465dd8\", \"size\": \"5.93 MB\", \"duration\": 242.59}", "aliases": ["Can't Wait To See Your New Body"], "size": "5.93 MB"}, {"id": "off-the-grid", "name": "Off The Grid [V2]", "artists": [], "producers": ["30 Roc"], "notes": "OG Filename: Off The Grid - 05.20.20 KayCyy Ref 2\nHas <PERSON><PERSON><PERSON><PERSON> doing the hook, which is not seen on the second May 20th version.", "length": "86.69", "fileDate": 17388864, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/13f0e1acbbf7702bafe8cb703656413a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/13f0e1acbbf7702bafe8cb703656413a\", \"key\": \"Off The Grid\", \"title\": \"Off The Grid [V2]\", \"artists\": \"(ref. <PERSON>) (prod. 30 Roc)\", \"description\": \"OG Filename: Off The Grid - 05.20.20 KayCyy Ref 2\\nHas KayCyy doing the hook, which is not seen on the second May 20th version.\", \"date\": 17388864, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"88b0113e283a6b2fdd86aca73b57ff0b\", \"url\": \"https://api.pillowcase.su/api/download/88b0113e283a6b2fdd86aca73b57ff0b\", \"size\": \"3.43 MB\", \"duration\": 86.69}", "aliases": [], "size": "3.43 MB"}, {"id": "off-the-grid-191", "name": "Off The Grid [V3]", "artists": ["Kay<PERSON>y<PERSON>"], "producers": ["30 Roc"], "notes": "OG Filename: Off The Grid - 05.20.20 <PERSON><PERSON><PERSON><PERSON>f with <PERSON> of \"Off The Grid\" with mumble <PERSON><PERSON><PERSON> vocals, and a <PERSON><PERSON><PERSON><PERSON> hook and verse. Snippet leaked December 9th, 2022.", "length": "86.69", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/a0d314ebfc45cb3ee2c3e1e0cbde6eda", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a0d314ebfc45cb3ee2c3e1e0cbde6eda\", \"key\": \"Off The Grid\", \"title\": \"Off The Grid [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. 30 Roc)\", \"description\": \"OG Filename: Off The Grid - 05.20.20 Kay<PERSON>yy Verse Ref with <PERSON>\\nVersion of \\\"Off The Grid\\\" with mumble <PERSON><PERSON><PERSON> vocals, and a Kay<PERSON><PERSON><PERSON> hook and verse. Snippet leaked December 9th, 2022.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"33aa84fc144fe5506b8fbfb1c110b764\", \"url\": \"https://api.pillowcase.su/api/download/33aa84fc144fe5506b8fbfb1c110b764\", \"size\": \"3.43 MB\", \"duration\": 86.69}", "aliases": [], "size": "3.43 MB"}, {"id": "off-the-grid-192", "name": "Off The Grid [V4]", "artists": [], "producers": ["E.VAX", "30 Roc"], "notes": "OG Filename: EVAN_mix\nHas additional E.VAX production, <PERSON><PERSON><PERSON>'s hook and a lot of open.", "length": "110.08", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/c1f5aa09a5f0867e19a5d0de7b83e0d5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c1f5aa09a5f0867e19a5d0de7b83e0d5\", \"key\": \"Off The Grid\", \"title\": \"Off The Grid [V4]\", \"artists\": \"(prod. E.VAX & 30 Roc)\", \"description\": \"OG Filename: EVAN_mix\\nHas additional E.VAX production, <PERSON><PERSON><PERSON>'s hook and a lot of open.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"eaef92b1552dafbfaaed6faa19229cf1\", \"url\": \"https://api.pillowcase.su/api/download/eaef92b1552dafbfaaed6faa19229cf1\", \"size\": \"3.81 MB\", \"duration\": 110.08}", "aliases": [], "size": "3.81 MB"}, {"id": "praise-god", "name": "Praise God [V9]", "artists": ["<PERSON>", "Kay<PERSON>y<PERSON>"], "producers": ["30 Roc", "E.VAX", "Zentachi"], "notes": "OG Filename: EVAN_mix\nHas additional production from E.VAX, but is different from the tracklist E.VAX version. Original snippet leaked October 25th, 2022.", "length": "157.38", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/02eeff05662071b340fa6be842c7014b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/02eeff05662071b340fa6be842c7014b\", \"key\": \"Praise <PERSON>\", \"title\": \"Praise God [V9]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. 30 Roc, E.VAX & Zentachi)\", \"aliases\": [\"Praise\"], \"description\": \"OG Filename: EVAN_mix\\nHas additional production from E.VAX, but is different from the tracklist E.VAX version. Original snippet leaked October 25th, 2022.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"2d06a810dba45a9699fbf9e282c41106\", \"url\": \"https://api.pillowcase.su/api/download/2d06a810dba45a9699fbf9e282c41106\", \"size\": \"4.56 MB\", \"duration\": 157.38}", "aliases": ["<PERSON>raise"], "size": "4.56 MB"}, {"id": "praise-god-194", "name": "Praise God [V10]", "artists": ["<PERSON>", "Kay<PERSON>y<PERSON>"], "producers": ["30 Roc", "E.VAX", "Zentachi"], "notes": "OG Filename: EVAN_mix\nAnother E.VAX-produced version.", "length": "134.24", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/3ed9aec915084d1b125d8221d4a085c1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3ed9aec915084d1b125d8221d4a085c1\", \"key\": \"Praise God\", \"title\": \"Praise <PERSON> [V10]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. 30 R<PERSON>, E.<PERSON>X & Zen<PERSON>chi)\", \"aliases\": [\"Praise\"], \"description\": \"OG Filename: EVAN_mix\\nAnother E.VAX-produced version.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c427d15ec0b65c08ad7e22e267a2f648\", \"url\": \"https://api.pillowcase.su/api/download/c427d15ec0b65c08ad7e22e267a2f648\", \"size\": \"4.19 MB\", \"duration\": 134.24}", "aliases": ["<PERSON>raise"], "size": "4.19 MB"}, {"id": "pull-up-like-skuuurrr", "name": "Pull Up Like <PERSON><PERSON><PERSON><PERSON><PERSON> [V3]", "artists": [], "producers": ["BoogzDaBeast", "???"], "notes": "OG Filename: Pull Up Like <PERSON><PERSON><PERSON>urrr - 06.16.20 <PERSON>\nFeatured on DONDA 2020 tracklists. Has no vocals.", "length": "150.44", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/3182487178c21b5e37c15482c8f080c5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3182487178c21b5e37c15482c8f080c5\", \"key\": \"Pull Up Like Skuuurrr\", \"title\": \"Pull Up Like <PERSON>kuuurrr [V3]\", \"artists\": \"(prod. BoogzDaBeast & ???)\", \"aliases\": [\"Skuuurrruurrr\", \"Skuuurrr\"], \"description\": \"OG Filename: Pull Up Like Skuuurrr - 06.16.20 Ali\\nFeatured on DONDA 2020 tracklists. Has no vocals.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"da7c3cfd32a4a4d3ee4a51aa1d503450\", \"url\": \"https://api.pillowcase.su/api/download/da7c3cfd32a4a4d3ee4a51aa1d503450\", \"size\": \"4.45 MB\", \"duration\": 150.44}", "aliases": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "size": "4.45 MB"}, {"id": "navy", "name": "Navy [V4]", "artists": [], "producers": ["BoogzDaBeast"], "notes": "OG Filename: 06.17.20 Kaycyy Navy VS\nVersion with an alternate KayCyy verse and <PERSON><PERSON><PERSON> acting as a hook of sorts. Potentially titled \"Navy\" based on the line \"In the sea, I'm too Navy\". Original snippet leaked on July 30th, 2023.", "length": "117.17", "fileDate": 16968960, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/b71c8da4ec806150b6e962c070986772", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b71c8da4ec806150b6e962c070986772\", \"key\": \"Navy\", \"title\": \"Navy [V4]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. BoogzDaBeast)\", \"aliases\": [\"Skuuurrruurrr\", \"Pull Up Like Skuuurrr\", \"Skurr\", \"Skurrrr\", \"Skuuur\", \"Skuuurrr\"], \"description\": \"OG Filename: 06.17.20 Kaycyy Navy VS\\nVersion with an alternate Kay<PERSON>yy verse and <PERSON><PERSON><PERSON> acting as a hook of sorts. Potentially titled \\\"Navy\\\" based on the line \\\"In the sea, I'm too Navy\\\". Original snippet leaked on July 30th, 2023.\", \"date\": 16968960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"21360891f76da304b0ab80ec61ffabc5\", \"url\": \"https://api.pillowcase.su/api/download/21360891f76da304b0ab80ec61ffabc5\", \"size\": \"3.92 MB\", \"duration\": 117.17}", "aliases": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Pull Up Like <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "size": "3.92 MB"}, {"id": "skuuurrruurrr-197", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> [V5]", "artists": [], "producers": ["E.VAX", "BoogzDaBeast"], "notes": "OG Filename: EVAN_mix\nVersion of \"Skuuurrruurrr\" with production from E.VAX.", "length": "141.08", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/a8b87016501fae8f0c70bc1d62b32c03", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a8b87016501fae8f0c70bc1d62b32c03\", \"key\": \"Skuuurrruurrr\", \"title\": \"Skuuurrruurrr [V5]\", \"artists\": \"(ref. <PERSON>) (prod. E.VAX & BoogzDaBeast)\", \"aliases\": [\"Navy\", \"Pull Up Like Skuuurrr\", \"Skurr\", \"Skurrrr\", \"Skuuur\", \"Skuuurrr\"], \"description\": \"OG Filename: EVAN_mix\\nVersion of \\\"Skuuurrruurrr\\\" with production from E.VAX.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"076b599f66c5efd323f9a49b0501f548\", \"url\": \"https://api.pillowcase.su/api/download/076b599f66c5efd323f9a49b0501f548\", \"size\": \"4.3 MB\", \"duration\": 141.08}", "aliases": ["Navy", "Pull Up Like <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "size": "4.3 MB"}, {"id": "serving-god", "name": "Serving God [V1]", "artists": [], "producers": ["E.VAX"], "notes": "Solo mumble Kanye version of \"Serving God\". Original snippets leaked September 19th & October 16th, 2022.", "length": "83.59", "fileDate": 17005248, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/c639255b90489c52074c95087fa40c60", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c639255b90489c52074c95087fa40c60\", \"key\": \"Serving God\", \"title\": \"Serving God [V1]\", \"artists\": \"(prod. E.VAX)\", \"description\": \"Solo mumble Kanye version of \\\"Serving God\\\". Original snippets leaked September 19th & October 16th, 2022.\", \"date\": 17005248, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1abcef0c058130a1240caacd7d7c9655\", \"url\": \"https://api.pillowcase.su/api/download/1abcef0c058130a1240caacd7d7c9655\", \"size\": \"3.38 MB\", \"duration\": 83.59}", "aliases": [], "size": "3.38 MB"}, {"id": "serving-god-199", "name": "Serving God [V2]", "artists": [], "producers": [], "notes": "OG Filename Serving God 78 - 07.02.20 <PERSON><PERSON><PERSON> Re<PERSON> \"Serving God\" reference track. Snippet leaked alongside snippets for the Ant Clemons and <PERSON> reference tracks for the song. Was up for a groupbuy, but was cancelled after <PERSON><PERSON> said he would leak \"Run It Up\" if the groupbuy was cancelled. However, this \"Run It Up\" was actually referring to an unrelated A$AP Rocky song of the same name.", "length": "135.5", "fileDate": 16952544, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/16f594b9e41cd03c2fe90e16b1ee34dc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/16f594b9e41cd03c2fe90e16b1ee34dc\", \"key\": \"Serving God\", \"title\": \"Serving God [V2]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename Serving God 78 - 07.02.20 Kaycyy Ref\\nKayCyy \\\"Serving God\\\" reference track. Snippet leaked alongside snippets for the Ant Clemons and <PERSON> reference tracks for the song. Was up for a groupbuy, but was cancelled after <PERSON><PERSON> said he would leak \\\"Run It Up\\\" if the groupbuy was cancelled. However, this \\\"Run It Up\\\" was actually referring to an unrelated A$AP Rocky song of the same name.\", \"date\": 16952544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dd31a0bc221a6d7cb604af1ba9e996a8\", \"url\": \"https://api.pillowcase.su/api/download/dd31a0bc221a6d7cb604af1ba9e996a8\", \"size\": \"4.21 MB\", \"duration\": 135.5}", "aliases": [], "size": "4.21 MB"}, {"id": "soldier", "name": "Soldier", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: Soldier - 06.03.20 <PERSON><PERSON><PERSON> Ref\nRough KayCyy reference. Unknown if <PERSON> ever recorded, but <PERSON><PERSON><PERSON><PERSON> would later go on to drop this song without any affiliation with <PERSON>.", "length": "96.57", "fileDate": 16578432, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/638ea5c2f49c984539b56b7338fceb43", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/638ea5c2f49c984539b56b7338fceb43\", \"key\": \"Soldier\", \"title\": \"Soldier\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: Soldier - 06.03.20 <PERSON>cyy Ref\\nRough KayCyy reference. Unknown if <PERSON> ever recorded, but <PERSON><PERSON><PERSON><PERSON> would later go on to drop this song without any affiliation with <PERSON>.\", \"date\": 16578432, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6cafcb7b863dd4232a57892046070477\", \"url\": \"https://api.pillowcase.su/api/download/6cafcb7b863dd4232a57892046070477\", \"size\": \"3.59 MB\", \"duration\": 96.57}", "aliases": [], "size": "3.59 MB"}, {"id": "tell-the-vision", "name": "Tell The Vision [V1]", "artists": [], "producers": [], "notes": "Version of \"Tell The Vision\" with additional drums can be heard in the bleed on the E.VAX version's acapella. Likely the original freestyle for the song.", "length": "148.33", "fileDate": 16950816, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/744c0a4e2d20fc4b265b6df24cdaa3ce", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/744c0a4e2d20fc4b265b6df24cdaa3ce\", \"key\": \"Tell The Vision\", \"title\": \"Tell The Vision [V1]\", \"aliases\": [\"RIP Pop Smoke\", \"Television\", \"We Made It\"], \"description\": \"Version of \\\"Tell The Vision\\\" with additional drums can be heard in the bleed on the E.VAX version's acapella. Likely the original freestyle for the song.\", \"date\": 16950816, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"16c4855df2f31dfd411f699e32f2e389\", \"url\": \"https://api.pillowcase.su/api/download/16c4855df2f31dfd411f699e32f2e389\", \"size\": \"4.42 MB\", \"duration\": 148.33}", "aliases": ["RIP Pop Smoke", "Television", "We Made It"], "size": "4.42 MB"}, {"id": "tell-the-vision-202", "name": "Tell The Vision [V3]", "artists": [], "producers": ["E.VAX", "BoogzDaBeast"], "notes": "OG Filename: EVAN_mix\nOriginal solo mumble freestyle version of \"Tell The Vision\". Has different production compared to later versions. Samples \"Call Me Bad\" by <PERSON> and \"Distance Equals Rate Times Time\" by Pixies. According to <PERSON> November, the sample was brought to Ka<PERSON>e by BoogzDaBeast.", "length": "148.33", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/47b4854f37c2f735acbe2eec38a1e935", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/47b4854f37c2f735acbe2eec38a1e935\", \"key\": \"Tell The Vision\", \"title\": \"Tell The Vision [V3]\", \"artists\": \"(prod. E.VAX & BoogzDaBeast) \", \"aliases\": [\"RIP Pop Smoke\", \"Television\", \"We Made It\"], \"description\": \"OG Filename: EVAN_mix\\nOriginal solo mumble freestyle version of \\\"Tell The Vision\\\". Has different production compared to later versions. Samples \\\"Call Me Bad\\\" by <PERSON> and \\\"Distance Equals Rate Times Time\\\" by Pixies. According to <PERSON>, the sample was brought to Kanye by BoogzDaBeast.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"abf0e20fe9da8ff966f2ff6d9f08d747\", \"url\": \"https://api.pillowcase.su/api/download/abf0e20fe9da8ff966f2ff6d9f08d747\", \"size\": \"4.42 MB\", \"duration\": 148.33}", "aliases": ["RIP Pop Smoke", "Television", "We Made It"], "size": "4.42 MB"}, {"id": "this-is-the-glory", "name": "This Is The Glory [V5]", "artists": ["Dem <PERSON>z", "Dr. <PERSON><PERSON>", "Snoop Dogg"], "producers": ["Dr. <PERSON><PERSON>", "Dem <PERSON>z"], "notes": "OG Filename: This Is The Glory - 07.14.20 Ye Edit\nFilename posted by <PERSON><PERSON><PERSON> on Twitter on September 16th, 2020, thinking it would post the file. Alt mix of the June 28th, 2020 version. Was on several 2021 tracklists, played by <PERSON><PERSON><PERSON> at <PERSON>'s 2021 birthday event, and in the Beats By Dre advertisement.", "length": "", "fileDate": 16613856, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/ba514434b6dfbeee2f34ae9d4ed925ff", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ba514434b6dfbeee2f34ae9d4ed925ff\", \"key\": \"This Is The Glory\", \"title\": \"This Is The Glory [V5]\", \"artists\": \"(feat. <PERSON><PERSON>, Dr. Dre & Snoop <PERSON>) (prod. Dr. Dre & Dem Jointz)\", \"description\": \"OG Filename: This Is The Glory - 07.14.20 Ye Edit\\nFilename posted by <PERSON><PERSON><PERSON> on Twitter on September 16th, 2020, thinking it would post the file. Alt mix of the June 28th, 2020 version. Was on several 2021 tracklists, played by <PERSON><PERSON><PERSON> at <PERSON>'s 2021 birthday event, and in the Beats By Dre advertisement.\", \"date\": 16613856, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}, {"id": "this-is-the-glory-204", "name": "This Is The Glory [V5]", "artists": ["Dem <PERSON>z", "Dr. <PERSON><PERSON>", "Snoop Dogg"], "producers": ["Dr. <PERSON><PERSON>", "Dem <PERSON>z"], "notes": "OG Filename: This Is The Glory - 07.14.20 Ye Edit\nFilename posted by <PERSON><PERSON><PERSON> on Twitter on September 16th, 2020, thinking it would post the file. Alt mix of the June 28th, 2020 version. Was on several 2021 tracklists, played by <PERSON><PERSON><PERSON> at <PERSON>'s 2021 birthday event, and in the Beats By Dre advertisement.", "length": "10.25", "fileDate": 16613856, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/e5cbf4ad8b3dbe21d303c756f76f93b7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e5cbf4ad8b3dbe21d303c756f76f93b7\", \"key\": \"This Is The Glory\", \"title\": \"This Is The Glory [V5]\", \"artists\": \"(feat. <PERSON><PERSON>, Dr. Dre & Snoop Dogg) (prod. Dr. Dre & Dem Jointz)\", \"description\": \"OG Filename: This Is The Glory - 07.14.20 Ye Edit\\nFilename posted by <PERSON><PERSON><PERSON> on Twitter on September 16th, 2020, thinking it would post the file. Alt mix of the June 28th, 2020 version. Was on several 2021 tracklists, played by <PERSON><PERSON><PERSON> at <PERSON>'s 2021 birthday event, and in the Beats By Dre advertisement.\", \"date\": 16613856, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"8923accdd5320775af199126e5b2063d\", \"url\": \"https://api.pillowcase.su/api/download/8923accdd5320775af199126e5b2063d\", \"size\": \"2.21 MB\", \"duration\": 10.25}", "aliases": [], "size": "2.21 MB"}, {"id": "tulsa-205", "name": "🗑️ Tulsa [V5]", "artists": [], "producers": ["E.VAX", "BoogzDaBeast", "FnZ"], "notes": "OG Filename: EVAN_mix\nVersion of the KayCyy reference with additional production from E.VAX.", "length": "141.37", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/5d3c3b139d31131879a19992767d9905", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5d3c3b139d31131879a19992767d9905\", \"key\": \"Tulsa\", \"title\": \"\\ud83d\\uddd1\\ufe0f Tulsa [V5]\", \"artists\": \"(ref. <PERSON>) (prod. E.<PERSON>X, BoogzDaBeast & FnZ)\", \"description\": \"OG Filename: EVAN_mix\\nVersion of the KayCyy reference with additional production from E.VAX.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d5482aa8d7c17f5c1abcf3fb14815690\", \"url\": \"https://api.pillowcase.su/api/download/d5482aa8d7c17f5c1abcf3fb14815690\", \"size\": \"4.31 MB\", \"duration\": 141.37}", "aliases": [], "size": "4.31 MB"}, {"id": "wash-us-in-the-blood-206", "name": "Wash Us In The Blood [V10]", "artists": [], "producers": ["BoogzDaBeast", "FnZ"], "notes": "Version of \"Wash Us In The Blood\" from April 2020. A rough draft of the MV was shot for this version.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/c5630e523a44a9e5903ff95a2138ca5f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c5630e523a44a9e5903ff95a2138ca5f\", \"key\": \"Wash Us In The Blood\", \"title\": \"Wash Us In The Blood [V10]\", \"artists\": \"(prod. BoogzDaBeast & FnZ)\", \"aliases\": [\"Aesop\", \"Washed in the Blood\", \"Wash Us In The Blood Of Jesus\"], \"description\": \"Version of \\\"Wash Us In The Blood\\\" from April 2020. A rough draft of the MV was shot for this version.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["<PERSON><PERSON><PERSON>", "Washed in the Blood", "<PERSON>h Us In The Blood Of Jesus"], "size": ""}, {"id": "wash-us-in-the-blood-207", "name": "Wash Us In The Blood [V12]", "artists": [], "producers": ["BoogzDaBeast", "FnZ", "RONNY J"], "notes": "Version with different lyrics from the official release. Verse is likely the one made before replacing it with the pastor vocals. First known version with RONNY J production. Recorded by <PERSON> when he visited Wyoming in early June 2020, later shared onto Weibo.", "length": "33.98", "fileDate": 15909696, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/a86e73071f8195639671032f6bf232c2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a86e73071f8195639671032f6bf232c2\", \"key\": \"Wash Us In The Blood\", \"title\": \"Wash Us In The Blood [V12]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>, FnZ & RONNY J)\", \"aliases\": [\"Aesop\", \"Washed in the Blood\", \"Wash Us In The Blood Of Jesus\"], \"description\": \"Version with different lyrics from the official release. Verse is likely the one made before replacing it with the pastor vocals. First known version with R<PERSON>N<PERSON> J production. Recorded by <PERSON> when he visited Wyoming in early June 2020, later shared onto Weibo.\", \"date\": 15909696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"4e08b702cd7c95b93ebf535c4251492f\", \"url\": \"https://api.pillowcase.su/api/download/4e08b702cd7c95b93ebf535c4251492f\", \"size\": \"2.59 MB\", \"duration\": 33.98}", "aliases": ["<PERSON><PERSON><PERSON>", "Washed in the Blood", "<PERSON>h Us In The Blood Of Jesus"], "size": "2.59 MB"}, {"id": "wash-us-in-the-blood-208", "name": "<PERSON>h Us In The Blood [V13]", "artists": [], "producers": ["BoogzDaBeast", "FnZ", "RONNY J"], "notes": "Similar to the final version but has a partially mumble <PERSON><PERSON><PERSON> verse that <PERSON> later replaced, the pastor samples are absent and has simpler production.", "length": "182.62", "fileDate": 15977088, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/9a0b09334d29ccffb327938752be1a2f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9a0b09334d29ccffb327938752be1a2f\", \"key\": \"Wash Us In The Blood\", \"title\": \"Wash Us In The Blood [V13]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, FnZ & RONNY J)\", \"aliases\": [\"Aesop\", \"Washed in the Blood\", \"Wash Us In The Blood Of Jesus\"], \"description\": \"Similar to the final version but has a partially mumble Kanye verse that <PERSON> later replaced, the pastor samples are absent and has simpler production.\", \"date\": 15977088, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ad4e2229a62de7ecadd603ace6ffaca6\", \"url\": \"https://api.pillowcase.su/api/download/ad4e2229a62de7ecadd603ace6ffaca6\", \"size\": \"4.97 MB\", \"duration\": 182.62}", "aliases": ["<PERSON><PERSON><PERSON>", "Washed in the Blood", "<PERSON>h Us In The Blood Of Jesus"], "size": "4.97 MB"}, {"id": "wash-us-in-the-blood-209", "name": "Wash Us In The Blood [V14]", "artists": [], "producers": ["BoogzDaBeast", "FnZ", "RONNY J", "Dem <PERSON>z"], "notes": "OG Filename: WASH US N THE BLOOD _ TWO JOINTZ SAMPLE 137bpm\nVersion of \"Wash Us In The Blood\" with more complex production throughout and more pastor samples. <PERSON><PERSON><PERSON>'s vocals are still partially mumble.", "length": "190.09", "fileDate": 16288992, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/72eaa7ab288577b47967ef2fd16af1e0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/72eaa7ab288577b47967ef2fd16af1e0\", \"key\": \"Wash Us In The Blood\", \"title\": \"Wash Us In The Blood [V14]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, FnZ, RONNY J & Dem Jointz)\", \"aliases\": [\"Aesop\", \"Washed in the Blood\", \"Wash Us In The Blood Of Jesus\"], \"description\": \"OG Filename: WASH US N THE BLOOD _ TWO JOINTZ SAMPLE 137bpm\\nVersion of \\\"Wash Us In The Blood\\\" with more complex production throughout and more pastor samples. <PERSON><PERSON><PERSON>'s vocals are still partially mumble.\", \"date\": 16288992, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4a702e01ffd35bcb75ee6c004b9f1eca\", \"url\": \"https://api.pillowcase.su/api/download/4a702e01ffd35bcb75ee6c004b9f1eca\", \"size\": \"5.09 MB\", \"duration\": 190.09}", "aliases": ["<PERSON><PERSON><PERSON>", "Washed in the Blood", "<PERSON>h Us In The Blood Of Jesus"], "size": "5.09 MB"}, {"id": "wash-us-in-the-blood-210", "name": "Wash Us In The Blood [V15]", "artists": [], "producers": ["BoogzDaBeast", "FnZ", "RONNY J", "Dem <PERSON>z"], "notes": "OG Filename: WASH US N THE BLOOD _ JOINTZ SAMPLE 137bpm\nVersion with production closer to the final and more finished <PERSON><PERSON><PERSON> vocals.", "length": "190.04", "fileDate": 16288992, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/51e0cf0e143a7bf6e698392fb9b6ec16", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/51e0cf0e143a7bf6e698392fb9b6ec16\", \"key\": \"Wash Us In The Blood\", \"title\": \"Wash Us In The Blood [V15]\", \"artists\": \"(prod. <PERSON><PERSON>, FnZ, RONNY J & Dem Jointz)\", \"aliases\": [\"Aesop\", \"Washed in the Blood\", \"Wash Us In The Blood Of Jesus\"], \"description\": \"OG Filename: WASH US N THE BLOOD _ JOINTZ SAMPLE 137bpm\\nVersion with production closer to the final and more finished <PERSON><PERSON><PERSON> vocals.\", \"date\": 16288992, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4f469ba14ecc9d37958b4b5ac447c236\", \"url\": \"https://api.pillowcase.su/api/download/4f469ba14ecc9d37958b4b5ac447c236\", \"size\": \"5.09 MB\", \"duration\": 190.04}", "aliases": ["<PERSON><PERSON><PERSON>", "Washed in the Blood", "<PERSON>h Us In The Blood Of Jesus"], "size": "5.09 MB"}, {"id": "welcome-to-my-life-211", "name": "Welcome To My Life [V3]", "artists": ["Ty Dolla $ign"], "producers": ["<PERSON>", "<PERSON>"], "notes": "OG Filename: Welcome To My Life - 06.27.20 Add RxD Adds\nVersion of \"Welcome To My Life\" that's fully finished, but rougher than later versions.", "length": "252.67", "fileDate": 16949088, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/3edae74736fa3ee2655624ae68561471", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3edae74736fa3ee2655624ae68561471\", \"key\": \"Welcome To My Life\", \"title\": \"Welcome To My Life [V3]\", \"artists\": \"(feat. <PERSON>ign) (prod. <PERSON> & <PERSON>)\", \"description\": \"OG Filename: Welcome To My Life - 06.27.20 Add RxD Adds\\nVersion of \\\"Welcome To My Life\\\" that's fully finished, but rougher than later versions.\", \"date\": 16949088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5789cd8afd2fdb8caefb49a1feac3ef5\", \"url\": \"https://api.pillowcase.su/api/download/5789cd8afd2fdb8caefb49a1feac3ef5\", \"size\": \"6.09 MB\", \"duration\": 252.67}", "aliases": [], "size": "6.09 MB"}, {"id": "welcome-to-my-life-212", "name": "✨ Welcome To My Life [V4]", "artists": ["Ty Dolla $ign"], "producers": ["<PERSON>", "<PERSON>", "E.VAX"], "notes": "OG Filename: EVAN_mix\nVersion with additional production from E.VAX that is different from any other version of the song. VC recorded snippet leaked in 2022.", "length": "257.02", "fileDate": 16950816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/86557c9fcb975c41f24899d7aceb5a79", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/86557c9fcb975c41f24899d7aceb5a79\", \"key\": \"Welcome To My Life\", \"title\": \"\\u2728 Welcome To My Life [V4]\", \"artists\": \"(feat. <PERSON>ign) (prod. <PERSON>, <PERSON> & E.<PERSON>)\", \"description\": \"OG Filename: EVAN_mix\\nVersion with additional production from E.VAX that is different from any other version of the song. VC recorded snippet leaked in 2022.\", \"date\": 16950816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"25c28450db4557da3ea73dbe330840ef\", \"url\": \"https://api.pillowcase.su/api/download/25c28450db4557da3ea73dbe330840ef\", \"size\": \"6.16 MB\", \"duration\": 257.02}", "aliases": [], "size": "6.16 MB"}, {"id": "treat-you-good", "name": "Abstract Mindstate - <PERSON><PERSON><PERSON> You Good [V8]", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: AM TREAT YOU GOOD -  - Output - Stereo Out\nAbstract Mindstate version of \"Treat You Good\". From June 2020, given to them after Pusha T scrapped it.", "length": "130.09", "fileDate": 16820352, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/174a561aff7f0a0fd9c3829dde80f79e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/174a561aff7f0a0fd9c3829dde80f79e\", \"key\": \"Treat You Good\", \"title\": \"Abstract Mindstate - Treat You Good [V8]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: AM TREAT YOU GOOD -  - Output - Stereo Out\\nAbstract Mindstate version of \\\"Treat You Good\\\". From June 2020, given to them after <PERSON><PERSON><PERSON> T scrapped it.\", \"date\": 16820352, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7ee8d2eaf6e3cba2990fe7711978454e\", \"url\": \"https://api.pillowcase.su/api/download/7ee8d2eaf6e3cba2990fe7711978454e\", \"size\": \"4.13 MB\", \"duration\": 130.09}", "aliases": [], "size": "4.13 MB"}, {"id": "hotshit", "name": "Cardi B - HotShit [V2]", "artists": [], "producers": ["<PERSON><PERSON>", "BanBwoi"], "notes": "OG Filename: CB - HOTSHIT - REFF V1\nFilename shown by <PERSON><PERSON> B. Nothing else is known.", "length": "44.98", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/2173174608e7f12c73de28b7160beeb1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2173174608e7f12c73de28b7160beeb1\", \"key\": \"HotShit\", \"title\": \"Cardi B - HotShit [V2]\", \"artists\": \"(prod. <PERSON><PERSON> & BanBwoi)\", \"description\": \"OG Filename: CB - HOTSHIT - REFF V1\\nFilename shown by Card<PERSON> B. Nothing else is known.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"0352df403f4ff55d060346044407c7e4\", \"url\": \"https://api.pillowcase.su/api/download/0352df403f4ff55d060346044407c7e4\", \"size\": \"2.76 MB\", \"duration\": 44.98}", "aliases": [], "size": "2.76 MB"}, {"id": "take-me-to-the-light-2030", "name": "<PERSON> and the Lights - Take Me To The Light 2030 [V14]", "artists": ["Kanye West", "<PERSON>"], "producers": [], "notes": "Version likely planned for <PERSON>' album. Has a more dynamic and upbeat version of the instrumental compared to other versions. Original snippet was from a <PERSON> Instagram post of a possible music video for a new version of the song. Unofficial bounce.", "length": "180.92", "fileDate": 17237664, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/ad6d912d0ff8af65ded21c0735c56ec8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ad6d912d0ff8af65ded21c0735c56ec8\", \"key\": \"Take Me To The Light 2030\", \"title\": \"<PERSON> and the Lights - Take Me To The Light 2030 [V14]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & Bon Iver)\", \"aliases\": [\"Fine Line\", \"You Still Take Me To The Light\", \"Metta World Peace\"], \"description\": \"Version likely planned for <PERSON>' album. Has a more dynamic and upbeat version of the instrumental compared to other versions. Original snippet was from a <PERSON> Instagram post of a possible music video for a new version of the song. Unofficial bounce.\", \"date\": 17237664, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"652b0a12cca33747518b058223d82d8f\", \"url\": \"https://api.pillowcase.su/api/download/652b0a12cca33747518b058223d82d8f\", \"size\": \"4.94 MB\", \"duration\": 180.92}", "aliases": ["Fine Line", "You Still Take Me To The Light", "Metta World Peace"], "size": "4.94 MB"}, {"id": "friends-don-t-hurt-friends", "name": "<PERSON><PERSON><PERSON><PERSON> - Friends Don't Hurt Friends [V1]", "artists": ["<PERSON> and the Lights"], "producers": ["Kanye West"], "notes": "OG Filenames: <PERSON> <PERSON><PERSON> Hurt Friends - Em - 130.07, Friends Idea\nMumble freestyle with some clear words. Was made during the God's Country sessions, most likely in April or May.", "length": "177.3", "fileDate": 16539552, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/550a963b1179f42f13f8cd855a50949f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/550a963b1179f42f13f8cd855a50949f\", \"key\": \"Friends Don't Hurt Friends\", \"title\": \"Kay<PERSON>yy - Friends Don't Hurt Friends [V1]\", \"artists\": \"(feat. <PERSON> and the Lights) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filenames: Friends Dont Hurt Friends - Em - 130.07, Friends Idea\\nMumble freestyle with some clear words. Was made during the God's Country sessions, most likely in April or May.\", \"date\": 16539552, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"84997753f0fcf365ab50dea30a267b6e\", \"url\": \"https://api.pillowcase.su/api/download/84997753f0fcf365ab50dea30a267b6e\", \"size\": \"4.88 MB\", \"duration\": 177.3}", "aliases": [], "size": "4.88 MB"}, {"id": "friends-don-t-hurt-friends-217", "name": "<PERSON><PERSON><PERSON><PERSON> - Friends Don't Hurt Friends [V2]", "artists": ["<PERSON> and the Lights"], "producers": ["Kanye West"], "notes": "OG Filename: Friends w Drums LF Prep 6.23.20\nSimilar to the previous version, but with added trap drums.", "length": "177.3", "fileDate": 16539552, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/16dcba58298c1e98dcc0710eaed4078b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/16dcba58298c1e98dcc0710eaed4078b\", \"key\": \"Friends Don't Hurt Friends\", \"title\": \"Kay<PERSON>yy - Friends Don't Hurt Friends [V2]\", \"artists\": \"(feat. <PERSON> and the Lights) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Don't Hurt Your Friends\"], \"description\": \"OG Filename: Friends w Drums LF Prep 6.23.20\\nSimilar to the previous version, but with added trap drums.\", \"date\": 16539552, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4e0bf73f7c9c48ce728b1e350edcec8a\", \"url\": \"https://api.pillowcase.su/api/download/4e0bf73f7c9c48ce728b1e350edcec8a\", \"size\": \"4.88 MB\", \"duration\": 177.3}", "aliases": ["Don't Hurt Your Friends"], "size": "4.88 MB"}, {"id": "we-made-it", "name": "Pop Smoke - We Made It [V2]", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Rico Beats"], "notes": "Second version of \"We Made It\", with a verse from <PERSON>. Was intended for the deluxe version of the posthumous Pop Smoke album Shoot for the Stars, Aim for the Moon, but was scrapped. <PERSON> Beats leaked the full verse in HQ on Instagram on May 12th, 2023.", "length": "44.54", "fileDate": 16838496, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/368e5fa4436a6ab3e47bbc6cf71208b2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/368e5fa4436a6ab3e47bbc6cf71208b2\", \"key\": \"We Made It\", \"title\": \"Pop Smoke - We Made It [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON>n<PERSON>heK<PERSON>en & Rico Beats)\", \"description\": \"Second version of \\\"We Made It\\\", with a verse from <PERSON>. Was intended for the deluxe version of the posthumous Pop Smoke album Shoot for the Stars, Aim for the Moon, but was scrapped. <PERSON> leaked the full verse in HQ on Instagram on May 12th, 2023.\", \"date\": 16838496, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d81dc04699b64f128d7c53a481182c3f\", \"url\": \"https://api.pillowcase.su/api/download/d81dc04699b64f128d7c53a481182c3f\", \"size\": \"947 kB\", \"duration\": 44.54}", "aliases": [], "size": "947 kB"}, {"id": "dawn", "name": "<PERSON> [V1]", "artists": ["<PERSON>"], "producers": ["<PERSON>", "CVRE", "LUCA"], "notes": "According to <PERSON>, <PERSON>'s \"Dawn\" dates back to 2020. <PERSON> initially posted a snippet of the song to his Instagram on November 4, 2020, however he since deleted the post. Snippet resurfaced in 2023.", "length": "60.11", "fileDate": 16788384, "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/11e75f1aa59e0552014de347a2512116", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/11e75f1aa59e0552014de347a2512116\", \"key\": \"<PERSON>\", \"title\": \"<PERSON> [V1]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, CVRE & LUCA)\", \"aliases\": [\"Oxygen\", \"Oxygen or Wifi\", \"Sci Fi\", \"Sci Fi Wi Fi\", \"Sci-Fi\"], \"description\": \"According to <PERSON>, <PERSON>'s \\\"Dawn\\\" dates back to 2020. <PERSON> initially posted a snippet of the song to his Instagram on November 4, 2020, however he since deleted the post. Snippet resurfaced in 2023.\", \"date\": 16788384, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3b5b8d495e7ab607a180d8d11937d5d5\", \"url\": \"https://api.pillowcase.su/api/download/3b5b8d495e7ab607a180d8d11937d5d5\", \"size\": \"3.01 MB\", \"duration\": 60.11}", "aliases": ["Oxygen", "Oxygen or Wifi", "Sci Fi", "Sci Fi Wi Fi", "Sci-Fi"], "size": "3.01 MB"}, {"id": "big-champ", "name": "<PERSON> - <PERSON>mp [V1]", "artists": ["Kanye West"], "producers": [], "notes": "Has rough mumble vocals from <PERSON><PERSON><PERSON><PERSON> leaked March 30th, 2025, in response to <PERSON> claiming in an interview with DJ <PERSON><PERSON><PERSON><PERSON> that 4 songs he did in Wyoming eventually ended up on <PERSON>'s album Utopia, meaning this version likely has the Travis feature from the <PERSON><PERSON> Rich verison, but was <PERSON>' song at the time. Unknown if <PERSON><PERSON> is on this version. Exact era is unknown, but said to originate from the Wyoming sessions.", "length": "12.28", "fileDate": 17432928, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "jesus-is-lord", "originalUrl": "https://pillowcase.su/f/6a692878b7affb7545a09fc61a305020", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6a692878b7affb7545a09fc61a305020\", \"key\": \"Big Champ\", \"title\": \"<PERSON> - Big Champ [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Bad Champ\"], \"description\": \"Has rough mumble vocals from <PERSON><PERSON> leaked March 30th, 2025, in response to <PERSON> claiming in an interview with <PERSON> A<PERSON> that 4 songs he did in Wyoming eventually ended up on <PERSON>'s album Utopia, meaning this version likely has the <PERSON> feature from the <PERSON><PERSON> Rich verison, but was <PERSON>' song at the time. Unknown if <PERSON><PERSON> is on this version. Exact era is unknown, but said to originate from the Wyoming sessions.\", \"date\": 17432928, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ae1d2b8f47842f9c982558df73d3cc2c\", \"url\": \"https://api.pillowcase.su/api/download/ae1d2b8f47842f9c982558df73d3cc2c\", \"size\": \"2.24 MB\", \"duration\": 12.28}", "aliases": ["Bad Champ"], "size": "2.24 MB"}]}