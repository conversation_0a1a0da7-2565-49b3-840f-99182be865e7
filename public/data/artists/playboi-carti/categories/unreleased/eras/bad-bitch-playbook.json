{"id": "bad-bitch-playbook", "name": "Bad Bitch Playbook", "description": "<PERSON> and <PERSON>ign frequently collaborated during the sessions for YEBU, hinting at a joint project. A snippet featuring <PERSON> was released on October 2, 2023, leading to the song \"BACK TO ME,\" and confirming a joint album via Instagram. TMZ later reported that <PERSON> was producing both a solo album and a collaborative one with <PERSON>. Then, during the Saudi Arabia recording sessions for Bad Bitch Playbook Vol. 1 in November 2023, the name for the collaborative project between Ty Dolla $ign & Ye would be changed to Vultures, with that coming a sonic change in the project.", "backgroundColor": "rgb(171, 157, 122)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17H8C_KBaixw-EdKb3Nsx6Sw_WK49RR3HY9ayUatVG9jvY1ob4eaBYnBRZ9hSBiNxb8_N_fEm657BIkw4Ci9KKly-a2l06a7xfiKbScenwdpZ7_Zw4ZNviAeo-9q-LJO?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "we-back-outside", "name": "WE BACK OUTSIDE [V1]", "artists": [], "producers": ["Digital Nas"], "notes": "OG Filename: we back outside dn\nInstrumental to the first version of \"Aperol Spritz\" / \"Back Outside.\"", "length": "92.73", "fileDate": 17034624, "leakDate": "", "availableLength": "Beat Only", "quality": "High Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/b3bb67ecaa91516dba1310b2ac5a7aec", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b3bb67ecaa91516dba1310b2ac5a7aec\", \"key\": \"WE BACK OUTSIDE\", \"title\": \"WE BACK OUTSIDE [V1]\", \"artists\": \"(prod. Digital Nas)\", \"aliases\": [\"APEROL SPRITZ\", \"BACK OUTSIDE\"], \"description\": \"OG Filename: we back outside dn\\nInstrumental to the first version of \\\"Aperol Spritz\\\" / \\\"Back Outside.\\\"\", \"date\": 17034624, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"ce3ceede799d99dbe7d34f71eab8d130\", \"url\": \"https://api.pillowcase.su/api/download/ce3ceede799d99dbe7d34f71eab8d130\", \"size\": \"6.49 MB\", \"duration\": 92.73}", "aliases": ["APEROL SPRITZ", "BACK OUTSIDE"], "size": "6.49 MB"}, {"id": "aperol-spritz", "name": "APEROL SPRITZ (BACKOUTSIDE) [V2]", "artists": ["<PERSON><PERSON>"], "producers": ["Digital Nas", "damn james!"], "notes": "Track seen on the tracklist in the \"Back To Me\" video. This version has no <PERSON> vocals, and has vocals from <PERSON><PERSON>. Snippet leaked August 9th, 2024.", "length": "14.18", "fileDate": 17231616, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/571d11929e4e3a7fb273de7e3b10f6a1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/571d11929e4e3a7fb273de7e3b10f6a1\", \"key\": \"APEROL SPRITZ (BACKOUTSIDE)\", \"title\": \"APEROL SPRITZ (BACKOUTSIDE) [V2]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. Digital Nas & damn james!)\", \"aliases\": [\"WE BACK OUTSIDE\"], \"description\": \"Track seen on the tracklist in the \\\"Back To Me\\\" video. This version has no Ye vocals, and has vocals from <PERSON><PERSON>. Snippet leaked August 9th, 2024.\", \"date\": 17231616, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8f6aee74eee339293a1bb23583c68bbd\", \"url\": \"https://api.pillowcase.su/api/download/8f6aee74eee339293a1bb23583c68bbd\", \"size\": \"1.86 MB\", \"duration\": 14.18}", "aliases": ["WE BACK OUTSIDE"], "size": "1.86 MB"}, {"id": "back-n-that", "name": "BACK N THAT (IMAGINE THAT) [V1]", "artists": [], "producers": [], "notes": "Track name posted by <PERSON><PERSON><PERSON>, said to be on the 40 song long tracklist. Unknown if <PERSON> $ign recorded for this song. Snippet played by <PERSON><PERSON> on stream November 12th, 2024.", "length": "14.86", "fileDate": 17313696, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/996b99893e7572efa255e8f88b37af4e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/996b99893e7572efa255e8f88b37af4e\", \"key\": \"BACK N THAT (IMAGINE THAT)\", \"title\": \"BACK N THAT (IMAGINE THAT) [V1]\", \"aliases\": [\"IMAGINE DAT\"], \"description\": \"Track name posted by <PERSON><PERSON><PERSON>, said to be on the 40 song long tracklist. Unknown if <PERSON>ign recorded for this song. Snippet played by <PERSON><PERSON> on stream November 12th, 2024.\", \"date\": 17313696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"7fd9064e4872478c08d9098077f137d3\", \"url\": \"https://api.pillowcase.su/api/download/7fd9064e4872478c08d9098077f137d3\", \"size\": \"407 kB\", \"duration\": 14.86}", "aliases": ["IMAGINE DAT"], "size": "407 kB"}, {"id": "back-2-me", "name": "BACK 2 ME [V6]", "artists": [], "producers": [], "notes": "OG filename: back 2 me bj\nBump J reference track that has a different instrumental from all other known versions of the song.", "length": "6.38", "fileDate": 17065728, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/befe402d124b5e8611673c9a4f1e1299", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/befe402d124b5e8611673c9a4f1e1299\", \"key\": \"BACK 2 ME\", \"title\": \"BACK 2 ME [V6]\", \"artists\": \"(ref. <PERSON>ump J)\", \"aliases\": [\"BACK TO ME\"], \"description\": \"OG filename: back 2 me bj\\nBump J reference track that has a different instrumental from all other known versions of the song.\", \"date\": 17065728, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f124287e04fd9eb968e68295f38b8486\", \"url\": \"https://api.pillowcase.su/api/download/f124287e04fd9eb968e68295f38b8486\", \"size\": \"5.11 MB\", \"duration\": 6.38}", "aliases": ["BACK TO ME"], "size": "5.11 MB"}, {"id": "back-to-me", "name": "BACK TO ME [V7]", "artists": [], "producers": ["88-<PERSON>", "AyoAA", "Wax Motif"], "notes": "Snippet of \"Back To Me\" with <PERSON><PERSON><PERSON>. According to <PERSON><PERSON>, <PERSON><PERSON><PERSON>'s verse was a reference track for <PERSON>. Likely an earlier version due to missing production and effect, and a much different beat from the LP version of the song.", "length": "38.79", "fileDate": 16975872, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/d09390b5b4a9e719234e34ea67285952", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d09390b5b4a9e719234e34ea67285952\", \"key\": \"BACK TO ME\", \"title\": \"BACK TO ME [V7]\", \"artists\": \"(ref. <PERSON>ua<PERSON>) (prod. 88-<PERSON>, AyoAA & Wax Motif)\", \"aliases\": [\"BACK 2 ME\"], \"description\": \"Snippet of \\\"Back To Me\\\" with Q<PERSON><PERSON>. According to <PERSON><PERSON>, <PERSON><PERSON><PERSON>'s verse was a reference track for Ye. Likely an earlier version due to missing production and effect, and a much different beat from the LP version of the song.\", \"date\": 16975872, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"770525860ade381360207bcbd37f7a26\", \"url\": \"https://api.pillowcase.su/api/download/770525860ade381360207bcbd37f7a26\", \"size\": \"1.94 MB\", \"duration\": 38.79}", "aliases": ["BACK 2 ME"], "size": "1.94 MB"}, {"id": "back-to-me-6", "name": "BACK TO ME [V9]", "artists": [], "producers": ["88-<PERSON>", "AyoAA", "Wax Motif"], "notes": "Reference track made by <PERSON><PERSON><PERSON>, played by <PERSON><PERSON> himself at a club. Longer snippet with the original rough Ye verse was played between <PERSON><PERSON> and <PERSON>'s Rolling Loud sets.", "length": "", "fileDate": 17342208, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/b9fccea4cd7cd47b4e9b010d982758fc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b9fccea4cd7cd47b4e9b010d982758fc\", \"key\": \"BACK TO ME\", \"title\": \"BACK TO ME [V9]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. 88-<PERSON>, AyoAA & Wax Motif)\", \"aliases\": [\"BACK 2 ME\"], \"description\": \"Reference track made by <PERSON><PERSON><PERSON>, played by <PERSON><PERSON> himself at a club. Longer snippet with the original rough Ye verse was played between <PERSON><PERSON> and <PERSON>'s Rolling Loud sets.\", \"date\": 17342208, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["BACK 2 ME"], "size": ""}, {"id": "back-to-me-7", "name": "BACK TO ME [V9]", "artists": [], "producers": ["88-<PERSON>", "AyoAA", "Wax Motif"], "notes": "Reference track made by <PERSON><PERSON><PERSON>, played by <PERSON><PERSON> himself at a club. Longer snippet with the original rough Ye verse was played between <PERSON><PERSON> and <PERSON>'s Rolling Loud sets.", "length": "28.55", "fileDate": 17342208, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/3d8294dc104f77109e5ef9f8e6fdc10b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3d8294dc104f77109e5ef9f8e6fdc10b\", \"key\": \"BACK TO ME\", \"title\": \"BACK TO ME [V9]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. 88-<PERSON>, AyoAA & Wax Motif)\", \"aliases\": [\"BACK 2 ME\"], \"description\": \"Reference track made by <PERSON><PERSON><PERSON>, played by <PERSON><PERSON> himself at a club. Longer snippet with the original rough Ye verse was played between <PERSON><PERSON> and <PERSON>'s Rolling Loud sets.\", \"date\": 17342208, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"29ad9acd5eee5960f524b93fcac47a9f\", \"url\": \"https://api.pillowcase.su/api/download/29ad9acd5eee5960f524b93fcac47a9f\", \"size\": \"5.47 MB\", \"duration\": 28.55}", "aliases": ["BACK 2 ME"], "size": "5.47 MB"}, {"id": "back-to-me-8", "name": "BACK TO ME [V10]", "artists": [], "producers": ["88-<PERSON>", "AyoAA", "Wax Motif"], "notes": "OG Filename: back to me a...\nA short video of <PERSON> <PERSON> <PERSON> $ign playing a song from <PERSON>'s new album leaked October 2nd, 2023 after an article published just a day before said that <PERSON> looked 'furious and distressed as he previews his upcoming album'. Likely produced by <PERSON><PERSON><PERSON> as he's seen in the background of the video. Title and length were partially posted by <PERSON> on November 11th, 2023, appears to be around ~5:00 in length.", "length": "12.02", "fileDate": 16963776, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/ab80547db49057de63ece68f87e4e31e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ab80547db49057de63ece68f87e4e31e\", \"key\": \"BACK TO ME\", \"title\": \"BACK TO ME [V10]\", \"artists\": \"(prod. 88-<PERSON>, AyoAA & Wax Motif)\", \"aliases\": [\"BACK 2 ME\"], \"description\": \"OG Filename: back to me a...\\nA short video of <PERSON> & <PERSON> $ign playing a song from <PERSON>'s new album leaked October 2nd, 2023 after an article published just a day before said that <PERSON> looked 'furious and distressed as he previews his upcoming album'. Likely produced by <PERSON><PERSON><PERSON> as he's seen in the background of the video. Title and length were partially posted by <PERSON> on November 11th, 2023, appears to be around ~5:00 in length.\", \"date\": 16963776, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"6b2666ad58aac1f2908e6af0c9bff38d\", \"url\": \"https://api.pillowcase.su/api/download/6b2666ad58aac1f2908e6af0c9bff38d\", \"size\": \"1.83 MB\", \"duration\": 12.02}", "aliases": ["BACK 2 ME"], "size": "1.83 MB"}, {"id": "back-to-me-9", "name": "BACK TO ME [V11]", "artists": [], "producers": ["88-<PERSON>", "AyoAA", "DJ Camper", "Wax Motif"], "notes": "Version with an added piano done by producer DJ <PERSON><PERSON>. Made during the Italy sessions.", "length": "15.75", "fileDate": 17075232, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/f45624c082a18ab9c5cc0308a7657e79", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f45624c082a18ab9c5cc0308a7657e79\", \"key\": \"BACK TO ME\", \"title\": \"BACK TO ME [V11]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> & Wax Motif)\", \"aliases\": [\"BACK 2 ME\"], \"description\": \"Version with an added piano done by producer <PERSON>. Made during the Italy sessions.\", \"date\": 17075232, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"86f10ade1d1dd835c363c001a4624227\", \"url\": \"https://api.pillowcase.su/api/download/86f10ade1d1dd835c363c001a4624227\", \"size\": \"5.26 MB\", \"duration\": 15.75}", "aliases": ["BACK 2 ME"], "size": "5.26 MB"}, {"id": "burn", "name": "BURN [V6]", "artists": [], "producers": ["The Legendary Traxster", "<PERSON><PERSON>"], "notes": "Has slightly different production. Possibly one of the first versions of the song, due to the person who leaked the snippet stating that they had the song for \"around a year.\" This is likely an exaggeration.", "length": "55.51", "fileDate": 17055360, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/16bed3778d2b1c6170895e228b7c9af5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/16bed3778d2b1c6170895e228b7c9af5\", \"key\": \"BURN\", \"title\": \"BURN [V6]\", \"artists\": \"(prod. The Legendary Trax<PERSON> & <PERSON>)\", \"aliases\": [\"Dangerous\"], \"description\": \"Has slightly different production. Possibly one of the first versions of the song, due to the person who leaked the snippet stating that they had the song for \\\"around a year.\\\" This is likely an exaggeration.\", \"date\": 17055360, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a484a20f7d6b6de0c9b78396b4829e7e\", \"url\": \"https://api.pillowcase.su/api/download/a484a20f7d6b6de0c9b78396b4829e7e\", \"size\": \"2.52 MB\", \"duration\": 55.51}", "aliases": ["Dangerous"], "size": "2.52 MB"}, {"id": "burn-11", "name": "✨ BURN [V7]", "artists": [], "producers": ["The Legendary Traxster", "<PERSON><PERSON>"], "notes": "OG Filename: burn - V5\nCompletely finished, has quieter drums and a Ty$ verse that isn't on the released version.", "length": "146.39", "fileDate": 17044128, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/7f02c994578b64c75ad11474c949645b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7f02c994578b64c75ad11474c949645b\", \"key\": \"BURN\", \"title\": \"\\u2728 BURN [V7]\", \"artists\": \"(prod. The Legendary Traxster & Chrishan)\", \"aliases\": [\"Dangerous\"], \"description\": \"OG Filename: burn - V5\\nCompletely finished, has quieter drums and a Ty$ verse that isn't on the released version.\", \"date\": 17044128, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8c22512e019e31569b88c65809841a15\", \"url\": \"https://api.pillowcase.su/api/download/8c22512e019e31569b88c65809841a15\", \"size\": \"7.35 MB\", \"duration\": 146.39}", "aliases": ["Dangerous"], "size": "7.35 MB"}, {"id": "city-underground", "name": "CITY UNDERGROUND [V3]", "artists": [], "producers": ["<PERSON>", "REMED"], "notes": "Finished solo Ty version of \"City Underground\" said to have been made during the Italy sessions, and was seen on an Italy tracklist. Fat Money posted a snippet of the lyrics on March 9th, 2024. Snippet leaked June 30th, 2024 - seems to have <PERSON> humming over <PERSON>'s version being played.", "length": "11.47", "fileDate": 17197056, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/bec244839d4ecbde04251f804bc4568f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bec244839d4ecbde04251f804bc4568f\", \"key\": \"CITY UNDERGROUND\", \"title\": \"CITY UNDERGROUND [V3]\", \"artists\": \"(prod. <PERSON> & REMED)\", \"description\": \"Finished solo Ty version of \\\"City Underground\\\" said to have been made during the Italy sessions, and was seen on an Italy tracklist. Fat Money posted a snippet of the lyrics on March 9th, 2024. Snippet leaked June 30th, 2024 - seems to have <PERSON> humming over <PERSON>'s version being played.\", \"date\": 17197056, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"c3c20e17c77a0b830f9957b1ae2617f4\", \"url\": \"https://api.pillowcase.su/api/download/c3c20e17c77a0b830f9957b1ae2617f4\", \"size\": \"5.19 MB\", \"duration\": 11.47}", "aliases": [], "size": "5.19 MB"}, {"id": "forever", "name": "FOREVER [V3]", "artists": ["Juice WRLD"], "producers": ["<PERSON>", "JRHITMAKER"], "notes": "OG Filename: forver - all togheter\nSong considered to be included on VULTURES. <PERSON> doing the hook and the first verse with <PERSON> doing the second verse and a couple other punch-ins for the hook & Juices verse. <PERSON>'s verse is most likely a Ye ref judging from some of the lyrics including slapping <PERSON>, who had been blamed for getting <PERSON> kicked from performing at the 2022 Grammys. Original snippet leaked January 5th, 2024.", "length": "197.35", "fileDate": 17044992, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/9c461210f905dec5ca82d45dc65b6276", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9c461210f905dec5ca82d45dc65b6276\", \"key\": \"FOREVER\", \"title\": \"FOREVER [V3]\", \"artists\": \"(ref. <PERSON>ign) (feat. <PERSON><PERSON>) (prod. <PERSON> & JRHITMAKER)\", \"aliases\": [\"Timeouts\"], \"description\": \"OG Filename: forver - all togheter\\nSong considered to be included on VULTURES. Has <PERSON><PERSON> doing the hook and the first verse with <PERSON> doing the second verse and a couple other punch-ins for the hook & Juices verse. <PERSON>'s verse is most likely a Ye ref judging from some of the lyrics including slapping <PERSON>, who had been blamed for getting <PERSON> kicked from performing at the 2022 Grammys. Original snippet leaked January 5th, 2024.\", \"date\": 17044992, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ac10c13a6a0bd9f4faa0176a7174c065\", \"url\": \"https://api.pillowcase.su/api/download/ac10c13a6a0bd9f4faa0176a7174c065\", \"size\": \"8.17 MB\", \"duration\": 197.35}", "aliases": ["Timeouts"], "size": "8.17 MB"}, {"id": "smokin-on-junt", "name": "SMOKIN' ON JUNT [V2]", "artists": [], "producers": [], "notes": "OG Filename: smokin on junt new verses\nHas reference vocals from <PERSON>, which were likely intended to be ran through a Carti AI program for a feature verse. Likely a Vulture's song at this point instead of solo <PERSON>. Has a lower pitch instrumental than any other version of the song.", "length": "132.92", "fileDate": 17360352, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/6799b30cb4383a9aea549fcc105a6b71", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6799b30cb4383a9aea549fcc105a6b71\", \"key\": \"SMOKIN' ON JUNT\", \"title\": \"SMOKIN' ON JUNT [V2]\", \"artists\": \"(ref. <PERSON>)\", \"aliases\": [\"Smoking On Junt\", \"FUK SUMN\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"OG Filename: smokin on junt new verses\\nHas reference vocals from <PERSON>, which were likely intended to be ran through a Carti AI program for a feature verse. Likely a Vulture's song at this point instead of solo Ty. Has a lower pitch instrumental than any other version of the song.\", \"date\": 17360352, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d34e7f50661c24339d254c7d40f34984\", \"url\": \"https://api.pillowcase.su/api/download/d34e7f50661c24339d254c7d40f34984\", \"size\": \"2.3 MB\", \"duration\": 132.92}", "aliases": ["Smoking On Junt", "FUK SUMN", "FUKK SUMN", "FUCK SUM"], "size": "2.3 MB"}, {"id": "fvk-smn", "name": "FVK SMN [V3]", "artists": [], "producers": [], "notes": "OG Filename: bj fvk smn .R\nUpdated version with <PERSON><PERSON> reference vocals, as well as a new Ty Dolla $ign verse. Made on the same day as the \"BACK TO ME\" Italy snippet video, so this most likely is the version on the Italy Bad Bitch Playbook Vol. 1 tracklist. Original snippet leaked January 15th, 2024. Full song leaked after a groupbuy.", "length": "150.92", "fileDate": 17160768, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/f702b36eee7e5fafbfa2118279caff55", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f702b36eee7e5fafbfa2118279caff55\", \"key\": \"FVK SMN\", \"title\": \"FVK SMN [V3]\", \"artists\": \"(ref. <PERSON>ump J)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\", \"FUK SUMN\"], \"description\": \"OG Filename: bj fvk smn .R\\nUpdated version with Bump J reference vocals, as well as a new Ty Dolla $ign verse. Made on the same day as the \\\"BACK TO ME\\\" Italy snippet video, so this most likely is the version on the Italy Bad Bitch Playbook Vol. 1 tracklist. Original snippet leaked January 15th, 2024. Full song leaked after a groupbuy.\", \"date\": 17160768, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1ee2d62740fc8cb316f2db68e610d442\", \"url\": \"https://api.pillowcase.su/api/download/1ee2d62740fc8cb316f2db68e610d442\", \"size\": \"7.42 MB\", \"duration\": 150.92}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM", "FUK SUMN"], "size": "7.42 MB"}, {"id": "fuk-sumn", "name": "FUK SUMN [V5]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": [], "notes": "According to <PERSON><PERSON>, this acapella is used on a \"Quavo Carti version\". Full file has not leaked, only the acapella for <PERSON><PERSON>.", "length": "185.15", "fileDate": 17160768, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/fb2c77e49d007c1924e97bcd3045397a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fb2c77e49d007c1924e97bcd3045397a\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V5]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"According to <PERSON><PERSON>, this acapella is used on a \\\"Quavo Carti version\\\". Full file has not leaked, only the acapella for <PERSON><PERSON>.\", \"date\": 17160768, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c6bcf85277607b7740b80c97c05697bb\", \"url\": \"https://api.pillowcase.su/api/download/c6bcf85277607b7740b80c97c05697bb\", \"size\": \"7.97 MB\", \"duration\": 185.15}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "7.97 MB"}, {"id": "fuk-sumn-17", "name": "FUK SUMN [V6]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": [], "notes": "Uses the same beat as earlier versions and has <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> on it. According to <PERSON><PERSON>, <PERSON><PERSON><PERSON>'s verse was recorded as a reference track for <PERSON> and being noted as to why <PERSON> was not on the song \"for so long\". A CDQ snippet leaked November 15th, 2023, with the full song later forceleaking.", "length": "205.74", "fileDate": 17162496, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/352355aea0ae47b38438d8a4e16ee402", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/352355aea0ae47b38438d8a4e16ee402\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V6]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"Uses the same beat as earlier versions and has <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> on it. According to <PERSON><PERSON>, <PERSON><PERSON><PERSON>'s verse was recorded as a reference track for <PERSON> and being noted as to why <PERSON> was not on the song \\\"for so long\\\". A CDQ snippet leaked November 15th, 2023, with the full song later forceleaking.\", \"date\": 17162496, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2969ba3e8453ad92282fe1e759ff09f2\", \"url\": \"https://api.pillowcase.su/api/download/2969ba3e8453ad92282fe1e759ff09f2\", \"size\": \"8.3 MB\", \"duration\": 205.74}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "8.3 MB"}, {"id": "fuk-sumn-18", "name": "FUK SUMN [V7]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": [], "notes": "Version similar to the above version, but with <PERSON>'s \"I'm tryna fuck sum right now\" lines being repeated less. Played at the Vultures City Event in Las Vegas.", "length": "161.83", "fileDate": 17025984, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/1f797964e42302501b7a3e7b925822d2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1f797964e42302501b7a3e7b925822d2\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V7]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"Version similar to the above version, but with <PERSON>'s \\\"I'm tryna fuck sum right now\\\" lines being repeated less. Played at the Vultures City Event in Las Vegas.\", \"date\": 17025984, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f23c2860d026af099a275ba64cc5f0ca\", \"url\": \"https://api.pillowcase.su/api/download/f23c2860d026af099a275ba64cc5f0ca\", \"size\": \"7.6 MB\", \"duration\": 161.83}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "7.6 MB"}, {"id": "fuk-sumn-19", "name": "FUK SUMN [V8]", "artists": [], "producers": [], "notes": "Version played by <PERSON><PERSON> on stream November 12th, 2024. Differences are unknown.", "length": "4.31", "fileDate": 17313696, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/a95066db9356fbffbfdbe8fe90e9f8d9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a95066db9356fbffbfdbe8fe90e9f8d9\", \"key\": \"FUK SUMN\", \"title\": \"FUK SUMN [V8]\", \"aliases\": [\"Smoking On Junt\", \"FUKK SUMN\", \"FUCK SUM\"], \"description\": \"Version played by <PERSON><PERSON> on stream November 12th, 2024. Differences are unknown.\", \"date\": 17313696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"6fa431ddfb18145e8a7af57ac1b520db\", \"url\": \"https://api.pillowcase.su/api/download/6fa431ddfb18145e8a7af57ac1b520db\", \"size\": \"238 kB\", \"duration\": 4.31}", "aliases": ["Smoking On Junt", "FUKK SUMN", "FUCK SUM"], "size": "238 kB"}, {"id": "loop-20", "name": "LOOP 20 [V2]", "artists": [], "producers": ["<PERSON>"], "notes": "Freestyle made during the Italy sessions. Ye rants about parents in the snippet. Was thought to be from YEBU era at one point and was nicknamed \"Parents\" for a while. According to <PERSON><PERSON>, this is an earlier version of \"Nice To Meet You\". Has some lines such as \"One in the pink, one in the stink\" that later were used on \"EVERYBODY\".", "length": "21.6", "fileDate": 17206560, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/5fd50dc0694dc8b471ecc60987369cfd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5fd50dc0694dc8b471ecc60987369cfd\", \"key\": \"LOOP 20\", \"title\": \"LOOP 20 [V2]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"NICE TO MEET YOU\"], \"description\": \"Freestyle made during the Italy sessions. Ye rants about parents in the snippet. Was thought to be from YEBU era at one point and was nicknamed \\\"Parents\\\" for a while. According to <PERSON><PERSON>, this is an earlier version of \\\"Nice To Meet You\\\". Has some lines such as \\\"One in the pink, one in the stink\\\" that later were used on \\\"EVERYBODY\\\".\", \"date\": 17206560, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7c9eeaec11d3233a35645ac3479d12ab\", \"url\": \"https://api.pillowcase.su/api/download/7c9eeaec11d3233a35645ac3479d12ab\", \"size\": \"1.28 MB\", \"duration\": 21.6}", "aliases": ["NICE TO MEET YOU"], "size": "1.28 MB"}, {"id": "love-again", "name": "LOVE AGAIN [V2]", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: Love Again - Ov_Cm_81\nSeen on the Joyboy 40-song tracklist. Snippet is of the Italy version. Originally stated to be an original version of \"Crying\" by <PERSON><PERSON><PERSON>, but <PERSON><PERSON> has confirmed it's a different song. Snippets were played by <PERSON><PERSON> on stream November 12th, 2024.", "length": "12.07", "fileDate": 17313696, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/725c9723dafc278c718517142e905c07", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/725c9723dafc278c718517142e905c07\", \"key\": \"LOVE AGAIN\", \"title\": \"LOVE AGAIN [V2]\", \"artists\": \"(prod. Ojivolta)\", \"description\": \"OG Filename: Love Again - Ov_Cm_81\\nSeen on the Joyboy 40-song tracklist. Snippet is of the Italy version. Originally stated to be an original version of \\\"Crying\\\" by <PERSON><PERSON><PERSON>, but <PERSON><PERSON> has confirmed it's a different song. Snippets were played by <PERSON><PERSON> on stream November 12th, 2024.\", \"date\": 17313696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3d0a18189d2675856e8d4f90036a280a\", \"url\": \"https://api.pillowcase.su/api/download/3d0a18189d2675856e8d4f90036a280a\", \"size\": \"5.2 MB\", \"duration\": 12.07}", "aliases": [], "size": "5.2 MB"}, {"id": "love-again-22", "name": "LOVE AGAIN [V2]", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: Love Again - Ov_Cm_81\nSeen on the Joyboy 40-song tracklist. Snippet is of the Italy version. Originally stated to be an original version of \"Crying\" by <PERSON><PERSON><PERSON>, but <PERSON><PERSON> has confirmed it's a different song. Snippets were played by <PERSON><PERSON> on stream November 12th, 2024.", "length": "33.15", "fileDate": 17313696, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/e656f6364d0c14ce7eb42fc796e43922", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e656f6364d0c14ce7eb42fc796e43922\", \"key\": \"LOVE AGAIN\", \"title\": \"LOVE AGAIN [V2]\", \"artists\": \"(prod. Ojivolta)\", \"description\": \"OG Filename: Love Again - Ov_Cm_81\\nSeen on the Joyboy 40-song tracklist. Snippet is of the Italy version. Originally stated to be an original version of \\\"Crying\\\" by <PERSON><PERSON><PERSON>, but <PERSON><PERSON> has confirmed it's a different song. Snippets were played by <PERSON><PERSON> on stream November 12th, 2024.\", \"date\": 17313696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"21a65c38333d421780d4ca76442b8e18\", \"url\": \"https://api.pillowcase.su/api/download/21a65c38333d421780d4ca76442b8e18\", \"size\": \"700 kB\", \"duration\": 33.15}", "aliases": [], "size": "700 kB"}, {"id": "love-again-23", "name": "LOVE AGAIN [V2]", "artists": [], "producers": ["Ojivolta"], "notes": "OG Filename: Love Again - Ov_Cm_81\nSeen on the Joyboy 40-song tracklist. Snippet is of the Italy version. Originally stated to be an original version of \"Crying\" by <PERSON><PERSON><PERSON>, but <PERSON><PERSON> has confirmed it's a different song. Snippets were played by <PERSON><PERSON> on stream November 12th, 2024.", "length": "15.31", "fileDate": 17313696, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/7c2c6288ae683f94557d0b0fefa7e22a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7c2c6288ae683f94557d0b0fefa7e22a\", \"key\": \"LOVE AGAIN\", \"title\": \"LOVE AGAIN [V2]\", \"artists\": \"(prod. Ojivolta)\", \"description\": \"OG Filename: Love Again - Ov_Cm_81\\nSeen on the Joyboy 40-song tracklist. Snippet is of the Italy version. Originally stated to be an original version of \\\"Crying\\\" by <PERSON><PERSON><PERSON>, but <PERSON><PERSON> has confirmed it's a different song. Snippets were played by <PERSON><PERSON> on stream November 12th, 2024.\", \"date\": 17313696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3507439ae87871613d3fe4843e756777\", \"url\": \"https://api.pillowcase.su/api/download/3507439ae87871613d3fe4843e756777\", \"size\": \"414 kB\", \"duration\": 15.31}", "aliases": [], "size": "414 kB"}, {"id": "love-me-the-most", "name": "🏆 LOVE ME THE MOST [V2]", "artists": [], "producers": ["<PERSON>", "88-<PERSON>"], "notes": "Track seen on tracklists from the Japan and Italy sessions. Confirmed to be fully finished during Italy era. Snippet leaked June 14th, 2024.", "length": "10.24", "fileDate": 17183232, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/f730aaa9f4986bd747fe46c5015b075e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f730aaa9f4986bd747fe46c5015b075e\", \"key\": \"LOVE ME THE MOST\", \"title\": \"\\ud83c\\udfc6 LOVE ME THE MOST [V2]\", \"artists\": \"(prod. <PERSON> 88-<PERSON>)\", \"description\": \"Track seen on tracklists from the Japan and Italy sessions. Confirmed to be fully finished during Italy era. Snippet leaked June 14th, 2024.\", \"date\": 17183232, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"6b70a995f1cfbecb39ea82ff70e351ec\", \"url\": \"https://api.pillowcase.su/api/download/6b70a995f1cfbecb39ea82ff70e351ec\", \"size\": \"5.17 MB\", \"duration\": 10.24}", "aliases": [], "size": "5.17 MB"}, {"id": "new-body", "name": "NEW BODY [V32]", "artists": ["<PERSON><PERSON>"], "producers": ["AyoAA"], "notes": "Updated version of \"New Body\" with drums from AyoAA. <PERSON><PERSON>'s original verse added back. Seen on a blurred tracklist from the leaked \"Back 2 Me\" snippet. Confirmed by <PERSON><PERSON> on his Instagram story. Played at the Spaceclub Firenze in Florence, Italy on October 10th, 2023. Track 10 on tracklist posted by Ty Dolla $ign.", "length": "21.47", "fileDate": 16968960, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/e02c7caacc6e75ce85060a994f639d11", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e02c7caacc6e75ce85060a994f639d11\", \"key\": \"NEW BODY\", \"title\": \"NEW BODY [V32]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. AyoAA)\", \"description\": \"Updated version of \\\"New Body\\\" with drums from AyoAA. <PERSON><PERSON>'s original verse added back. Seen on a blurred tracklist from the leaked \\\"Back 2 Me\\\" snippet. Confirmed by <PERSON><PERSON> on his Instagram story. Played at the Spaceclub Firenze in Florence, Italy on October 10th, 2023. Track 10 on tracklist posted by Ty Dolla $ign.\", \"date\": 16968960, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"1b930b3aa37db85dd8c51e68106f6dd9\", \"url\": \"https://api.pillowcase.su/api/download/1b930b3aa37db85dd8c51e68106f6dd9\", \"size\": \"1.8 MB\", \"duration\": 21.47}", "aliases": [], "size": "1.8 MB"}, {"id": "new-body-26", "name": "NEW BODY [V32]", "artists": ["<PERSON><PERSON>"], "producers": ["AyoAA"], "notes": "Updated version of \"New Body\" with drums from AyoAA. <PERSON><PERSON>'s original verse added back. Seen on a blurred tracklist from the leaked \"Back 2 Me\" snippet. Confirmed by <PERSON><PERSON> on his Instagram story. Played at the Spaceclub Firenze in Florence, Italy on October 10th, 2023. Track 10 on tracklist posted by Ty Dolla $ign.", "length": "5.3", "fileDate": 16968960, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/34bf66ccee7a4c8cb78656b7aa61ee1f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/34bf66ccee7a4c8cb78656b7aa61ee1f\", \"key\": \"NEW BODY\", \"title\": \"NEW BODY [V32]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. AyoAA)\", \"description\": \"Updated version of \\\"New Body\\\" with drums from AyoAA. <PERSON><PERSON>'s original verse added back. Seen on a blurred tracklist from the leaked \\\"Back 2 Me\\\" snippet. Confirmed by <PERSON><PERSON> on his Instagram story. Played at the Spaceclub Firenze in Florence, Italy on October 10th, 2023. Track 10 on tracklist posted by Ty Dolla $ign.\", \"date\": 16968960, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"029656baa8c13e1bb47381a78ad4e366\", \"url\": \"https://api.pillowcase.su/api/download/029656baa8c13e1bb47381a78ad4e366\", \"size\": \"1.67 MB\", \"duration\": 5.3}", "aliases": [], "size": "1.67 MB"}, {"id": "new-body-27", "name": "NEW BODY [V32]", "artists": ["<PERSON><PERSON>"], "producers": ["AyoAA"], "notes": "Updated version of \"New Body\" with drums from AyoAA. <PERSON><PERSON>'s original verse added back. Seen on a blurred tracklist from the leaked \"Back 2 Me\" snippet. Confirmed by <PERSON><PERSON> on his Instagram story. Played at the Spaceclub Firenze in Florence, Italy on October 10th, 2023. Track 10 on tracklist posted by Ty Dolla $ign.", "length": "9.48", "fileDate": 16968960, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/c398eaac7776d151dbc637fc6b890b63", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c398eaac7776d151dbc637fc6b890b63\", \"key\": \"NEW BODY\", \"title\": \"NEW BODY [V32]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. AyoAA)\", \"description\": \"Updated version of \\\"New Body\\\" with drums from AyoAA. <PERSON><PERSON>'s original verse added back. Seen on a blurred tracklist from the leaked \\\"Back 2 Me\\\" snippet. Confirmed by <PERSON><PERSON> on his Instagram story. Played at the Spaceclub Firenze in Florence, Italy on October 10th, 2023. Track 10 on tracklist posted by Ty Dolla $ign.\", \"date\": 16968960, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"79bb76bc91d70f20fbe4dee9ce51fbee\", \"url\": \"https://api.pillowcase.su/api/download/79bb76bc91d70f20fbe4dee9ce51fbee\", \"size\": \"321 kB\", \"duration\": 9.48}", "aliases": [], "size": "321 kB"}, {"id": "new-body-28", "name": "NEW BODY [V32]", "artists": ["<PERSON><PERSON>"], "producers": ["AyoAA"], "notes": "Updated version of \"New Body\" with drums from AyoAA. <PERSON><PERSON>'s original verse added back. Seen on a blurred tracklist from the leaked \"Back 2 Me\" snippet. Confirmed by <PERSON><PERSON> on his Instagram story. Played at the Spaceclub Firenze in Florence, Italy on October 10th, 2023. Track 10 on tracklist posted by Ty Dolla $ign.", "length": "21.11", "fileDate": 16968960, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/1f37075126d1aa8031a91804230ec51d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1f37075126d1aa8031a91804230ec51d\", \"key\": \"NEW BODY\", \"title\": \"NEW BODY [V32]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. AyoAA)\", \"description\": \"Updated version of \\\"New Body\\\" with drums from AyoAA. <PERSON><PERSON>'s original verse added back. Seen on a blurred tracklist from the leaked \\\"Back 2 Me\\\" snippet. Confirmed by <PERSON><PERSON> on his Instagram story. Played at the Spaceclub Firenze in Florence, Italy on October 10th, 2023. Track 10 on tracklist posted by Ty Dolla $ign.\", \"date\": 16968960, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ed102872fa369b925c620e913121cf9a\", \"url\": \"https://api.pillowcase.su/api/download/ed102872fa369b925c620e913121cf9a\", \"size\": \"507 kB\", \"duration\": 21.11}", "aliases": [], "size": "507 kB"}, {"id": "new-body-29", "name": "NEW BODY [V34]", "artists": [], "producers": [], "notes": "Version of \"New Body\" played by <PERSON><PERSON> on stream November 12th, 2024. Differences are unknown.", "length": "1.02", "fileDate": 17313696, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/dd3eb0144ba6c6eeaf68b421be4a9630", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dd3eb0144ba6c6eeaf68b421be4a9630\", \"key\": \"NEW BODY\", \"title\": \"NEW BODY [V34]\", \"description\": \"Version of \\\"New Body\\\" played by <PERSON><PERSON> on stream November 12th, 2024. Differences are unknown.\", \"date\": 17313696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"d89ef28806d11d1cf3ca72cd1bb4658e\", \"url\": \"https://api.pillowcase.su/api/download/d89ef28806d11d1cf3ca72cd1bb4658e\", \"size\": \"185 kB\", \"duration\": 1.02}", "aliases": [], "size": "185 kB"}, {"id": "paid", "name": "PAID [V3]", "artists": ["K-Ci"], "producers": ["<PERSON><PERSON>v", "The Legendary Traxster"], "notes": "Earlier version of \"Paid\" with a full <PERSON> verse and alternate production. Snippet leaked February 12th, 2024.", "length": "7.92", "fileDate": 17076960, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/919373bb06ecd58226df0c6b9acbbada", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/919373bb06ecd58226df0c6b9acbbada\", \"key\": \"PAID\", \"title\": \"PAID [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON> & The Legendary Traxster)\", \"description\": \"Earlier version of \\\"Paid\\\" with a full Ye verse and alternate production. Snippet leaked February 12th, 2024.\", \"date\": 17076960, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1360a2a37fc1809f7dde15db1d0d51bc\", \"url\": \"https://api.pillowcase.su/api/download/1360a2a37fc1809f7dde15db1d0d51bc\", \"size\": \"5.14 MB\", \"duration\": 7.92}", "aliases": [], "size": "5.14 MB"}, {"id": "paid-31", "name": "PAID [V4]", "artists": ["K-Ci", "The Legendary Traxster"], "producers": [], "notes": "Earlier version of \"Paid\" with a Dembow style of production. Unknown when this was made. Snippet leaked February 12th, 2024.", "length": "8.18", "fileDate": 17076960, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/39bac8199df034434f75de99c5c34ce7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/39bac8199df034434f75de99c5c34ce7\", \"key\": \"PAID\", \"title\": \"PAID [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON> & The Legendary Traxster)\", \"description\": \"Earlier version of \\\"Paid\\\" with a Dembow style of production. Unknown when this was made. Snippet leaked February 12th, 2024.\", \"date\": 17076960, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e67ca2e3ca6d351103593a57825f9962\", \"url\": \"https://api.pillowcase.su/api/download/e67ca2e3ca6d351103593a57825f9962\", \"size\": \"5.14 MB\", \"duration\": 8.18}", "aliases": [], "size": "5.14 MB"}, {"id": "papa-wanna-see", "name": "PAPA WANNA SEE [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: See -PAPA WANNA SEE 112_g#m\nPlayed in full December 15th, 2023 at the Vultures City Event in Las Vegas. Mumble version of the song that was played right after the finished version. Solo Ye. Italy version of the song.", "length": "137.08", "fileDate": 17075232, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/632dbe7c179ec28d725fd96088f7c244", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/632dbe7c179ec28d725fd96088f7c244\", \"key\": \"PAPA WANNA SEE\", \"title\": \"PAPA WANNA SEE [V1]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"TAKE OFF YOUR DRESS\"], \"description\": \"OG Filename: See -PAPA WANNA SEE 112_g#m\\nPlayed in full December 15th, 2023 at the Vultures City Event in Las Vegas. Mumble version of the song that was played right after the finished version. Solo Ye. Italy version of the song.\", \"date\": 17075232, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6de6c15c9977432913802a0e22efffd2\", \"url\": \"https://api.pillowcase.su/api/download/6de6c15c9977432913802a0e22efffd2\", \"size\": \"7.2 MB\", \"duration\": 137.08}", "aliases": ["TAKE OFF YOUR DRESS"], "size": "7.2 MB"}, {"id": "paperwork", "name": "PAPERWORK [V3]", "artists": [], "producers": [], "notes": "OG Filename: Paperwork - voice memo\nEarly version with mumble <PERSON> <PERSON> <PERSON> vocals. Contains some of the pitched up mumble vocals like how it is in the Quavo ref. Also contains a porno sample of some sort and has some vocals from <PERSON>. Snippets originally leaked June 28th, 2024 & October 19th, 2024. <PERSON> does some reference vocals for <PERSON><PERSON><PERSON>'s <PERSON> verse.", "length": "159.3", "fileDate": 17320608, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/0da30bd9ab7bec4f6525127ddad8688a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0da30bd9ab7bec4f6525127ddad8688a\", \"key\": \"PAPERWORK\", \"title\": \"PAPERWORK [V3]\", \"artists\": \"(ref. <PERSON> $ign)\", \"description\": \"OG Filename: Paperwork - voice memo\\nEarly version with mumble Ye & Ty vocals. Contains some of the pitched up mumble vocals like how it is in the Quavo ref. Also contains a porno sample of some sort and has some vocals from <PERSON>. Snippets originally leaked June 28th, 2024 & October 19th, 2024. <PERSON> does some reference vocals for <PERSON><PERSON><PERSON>'s Alvin verse.\", \"date\": 17320608, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"c74a060af4b6d94a09bb2c0131358168\", \"url\": \"https://api.pillowcase.su/api/download/c74a060af4b6d94a09bb2c0131358168\", \"size\": \"1.44 MB\", \"duration\": 159.3}", "aliases": [], "size": "1.44 MB"}, {"id": "paperwork-34", "name": "✨ PAPERWORK [V5]", "artists": [], "producers": [], "notes": "OG Filename: Paperwork - r9\nItaly version that has <PERSON><PERSON><PERSON> and pitched up mumble <PERSON> vocals. The verse done by <PERSON><PERSON><PERSON> would later be used for <PERSON>'s finished release verse, <PERSON>'s unfinished verse was sung by <PERSON> on release with finished lyrics and <PERSON><PERSON><PERSON> on release made a second verse.", "length": "122.93", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/51999fd0c9e4c9a4dcecf3b874d63d3f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/51999fd0c9e4c9a4dcecf3b874d63d3f\", \"key\": \"PAPERWORK\", \"title\": \"\\u2728 PAPERWORK [V5]\", \"artists\": \"(ref. Quavo)\", \"description\": \"OG Filename: Paperwork - r9\\nItaly version that has <PERSON><PERSON><PERSON> and pitched up mumble Ye vocals. The verse done by <PERSON><PERSON><PERSON> would later be used for <PERSON>'s finished release verse, <PERSON>'s unfinished verse was sung by <PERSON> on release with finished lyrics and <PERSON><PERSON><PERSON> on release made a second verse.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fe7688716b695b95fccdd30cb22101ef\", \"url\": \"https://api.pillowcase.su/api/download/fe7688716b695b95fccdd30cb22101ef\", \"size\": \"6.98 MB\", \"duration\": 122.93}", "aliases": [], "size": "6.98 MB"}, {"id": "pray", "name": "✨ PRAY [V3]", "artists": [], "producers": ["TryBishop"], "notes": "OG Filename: pray ye ref (new arrangement)\nTrack made during the Italy sessions for Vultures. Features about 4-5 minutes of vocals from <PERSON>, and a sample hook. Beat is partially reused from a song of the same name by <PERSON><PERSON>. Originally thought to be from YEBU, before a trusted source corrected this information.", "length": "425.38", "fileDate": 17280864, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/a35b2dc433ccdce7d0ff8496101ea2d4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a35b2dc433ccdce7d0ff8496101ea2d4\", \"key\": \"PRAY\", \"title\": \"\\u2728 PRAY [V3]\", \"artists\": \"(prod. <PERSON>Bishop)\", \"description\": \"OG Filename: pray ye ref (new arrangement)\\nTrack made during the Italy sessions for Vultures. Features about 4-5 minutes of vocals from <PERSON>, and a sample hook. Beat is partially reused from a song of the same name by <PERSON><PERSON>. Originally thought to be from YEBU, before a trusted source corrected this information.\", \"date\": 17280864, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0b58d5041402a87f307c06960db38607\", \"url\": \"https://api.pillowcase.su/api/download/0b58d5041402a87f307c06960db38607\", \"size\": \"7.75 MB\", \"duration\": 425.38}", "aliases": [], "size": "7.75 MB"}, {"id": "pray-36", "name": "PRAY [V4] ", "artists": [], "producers": ["TryBishop"], "notes": "Version of \"<PERSON><PERSON>\" with <PERSON>ign & unknown vocals. Unknown if this is for a feature or a reference. Snippets originally played by <PERSON><PERSON> on stream November 13th, 2024, before he would later play it again on stream but in full.", "length": "172.2", "fileDate": 17322336, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/a6a5e024311fce1c451f44baf649d497", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a6a5e024311fce1c451f44baf649d497\", \"key\": \"PRAY\", \"title\": \"PRAY [V4] \", \"artists\": \"(ref. ???) (prod. TryBishop)\", \"description\": \"Version of \\\"Pray\\\" with <PERSON> $ign & unknown vocals. Unknown if this is for a feature or a reference. Snippets originally played by <PERSON><PERSON> on stream November 13th, 2024, before he would later play it again on stream but in full.\", \"date\": 17322336, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"5c2d5c56400410028f781f79a276d6ed\", \"url\": \"https://api.pillowcase.su/api/download/5c2d5c56400410028f781f79a276d6ed\", \"size\": \"2.92 MB\", \"duration\": 172.2}", "aliases": [], "size": "2.92 MB"}, {"id": "slide", "name": "SLIDE [V3]", "artists": [], "producers": ["<PERSON> again..", "AyoAA", "Wheezy"], "notes": "Version played by <PERSON><PERSON> on stream November 12th, 2024. Any differences from other versions are unknown.", "length": "8.86", "fileDate": 17313696, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/ac2ae56f9fd52d81cef6efae15ee8c10", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ac2ae56f9fd52d81cef6efae15ee8c10\", \"key\": \"SLIDE\", \"title\": \"SLIDE [V3]\", \"artists\": \"(prod. <PERSON> again.., Ayo<PERSON> & Wheezy)\", \"aliases\": [\"Slide In\", \"SLIDIN\"], \"description\": \"Version played by <PERSON><PERSON> on stream November 12th, 2024. Any differences from other versions are unknown.\", \"date\": 17313696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"bd8087fe8f55555e3eeb68b38c3f0260\", \"url\": \"https://api.pillowcase.su/api/download/bd8087fe8f55555e3eeb68b38c3f0260\", \"size\": \"311 kB\", \"duration\": 8.86}", "aliases": ["Slide In", "SLIDIN"], "size": "311 kB"}, {"id": "slide-38", "name": "SLIDE [V4]", "artists": [], "producers": ["<PERSON> again..", "AyoAA", "Wheezy"], "notes": "<PERSON> played at a club by <PERSON> $ign himself on September 29th, 2023. Then played at <PERSON>'s set on November 4th, 2023. <PERSON> finished Ye vocals. Track 9 on tracklist posted by <PERSON> Dolla $ign.", "length": "229.28", "fileDate": 17021664, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/cad5ffaf71de282d53949dde5455f9eb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cad5ffaf71de282d53949dde5455f9eb\", \"key\": \"SLIDE\", \"title\": \"SLIDE [V4]\", \"artists\": \"(prod. <PERSON> again.., <PERSON><PERSON><PERSON> & Wheezy)\", \"aliases\": [\"Slide In\", \"SLIDIN\"], \"description\": \"<PERSON> played at a club by <PERSON>ign himself on September 29th, 2023. Then played at <PERSON>'s set on November 4th, 2023. <PERSON> finished Ye vocals. Track 9 on tracklist posted by <PERSON> Dolla $ign.\", \"date\": 17021664, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"cfae423455be12f908700151357ed366\", \"url\": \"https://api.pillowcase.su/api/download/cfae423455be12f908700151357ed366\", \"size\": \"8.68 MB\", \"duration\": 229.28}", "aliases": ["Slide In", "SLIDIN"], "size": "8.68 MB"}, {"id": "so-called-friends", "name": "⭐ SO CALLED FRIENDS [V6]", "artists": ["<PERSON>"], "producers": [], "notes": "OG Filename: so called friends _58bpm_F#_R5_1\nPosted by <PERSON><PERSON> after he fully leaked the blurry tracklist. Features finished verses from <PERSON>, <PERSON> and <PERSON>. Said to be from October 2023. Leaked after reaching the 24k goal in the \"Pressure\" groupbuy.", "length": "231.59", "fileDate": 17166816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/00f626aae662781c817b91975e6bdca5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/00f626aae662781c817b91975e6bdca5\", \"key\": \"SO CALLED FRIENDS\", \"title\": \"\\u2b50 SO CALLED FRIENDS [V6]\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"OG Filename: so called friends _58bpm_F#_R5_1\\nPosted by <PERSON><PERSON> after he fully leaked the blurry tracklist. Features finished verses from <PERSON>, <PERSON> and <PERSON>. Said to be from October 2023. Leaked after reaching the 24k goal in the \\\"Pressure\\\" groupbuy.\", \"date\": 17166816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"15074e43ba4fb3973c5f0ff644e9a3be\", \"url\": \"https://api.pillowcase.su/api/download/15074e43ba4fb3973c5f0ff644e9a3be\", \"size\": \"8.72 MB\", \"duration\": 231.59}", "aliases": [], "size": "8.72 MB"}, {"id": "so-called-friends-40", "name": "SO CALLED FRIENDS [V8]", "artists": ["<PERSON>"], "producers": ["IRKO"], "notes": "Version mixed by IRKO. Snippet leaked February 12th, 2024.", "length": "6.6899999999999995", "fileDate": 17076960, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/10d59f20e1ddd53b8da5e6704c82db19", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/10d59f20e1ddd53b8da5e6704c82db19\", \"key\": \"SO CALLED FRIENDS\", \"title\": \"SO CALLED FRIENDS [V8]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>R<PERSON>)\", \"description\": \"Version mixed by IRKO. Snippet leaked February 12th, 2024.\", \"date\": 17076960, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d27d222bcd30c5a24507950d8d3069eb\", \"url\": \"https://api.pillowcase.su/api/download/d27d222bcd30c5a24507950d8d3069eb\", \"size\": \"277 kB\", \"duration\": 6.6899999999999995}", "aliases": [], "size": "277 kB"}, {"id": "so-good", "name": "SO GOOD [V3]", "artists": [], "producers": [], "notes": "OG Filename: so good - ref 1.1\nTrack 6 on tracklist posted by <PERSON><PERSON> \"I Feel Love\" by <PERSON> and is also the song <PERSON><PERSON><PERSON> had mentioned that had a \"Late 80s Sample\". Has no <PERSON><PERSON> and the sample is chopped differently at points.", "length": "147.69", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/a4b4cfeca387169752df64fd300e7eea", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a4b4cfeca387169752df64fd300e7eea\", \"key\": \"SO GOOD\", \"title\": \"SO GOOD [V3]\", \"aliases\": [\"GOOD (DON'T DIE)\"], \"description\": \"OG Filename: so good - ref 1.1\\nTrack 6 on tracklist posted by <PERSON><PERSON> \\\"I Feel Love\\\" by <PERSON> and is also the song <PERSON><PERSON><PERSON> had mentioned that had a \\\"Late 80s Sample\\\". Has no <PERSON><PERSON> Rey <PERSON> and the sample is chopped differently at points.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"bfc1c47af00bca54c4a2f475bda36dbf\", \"url\": \"https://api.pillowcase.su/api/download/bfc1c47af00bca54c4a2f475bda36dbf\", \"size\": \"7.37 MB\", \"duration\": 147.69}", "aliases": ["GOOD (DON'T DIE)"], "size": "7.37 MB"}, {"id": "stars", "name": "✨ STARS [V4]", "artists": [], "producers": ["FNZ", "Ojivolta"], "notes": "OG Filename: stars - ref 3_OV_62bpm_\nTrack seen on a tracklist from the Italy sessions.Has a completely different refrain done by <PERSON>, an alternate Ojivolta outro with a guitar and is way longer than release.", "length": "170.38", "fileDate": 17073504, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/cad243e31007aed170eddad387da1d3c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cad243e31007aed170eddad387da1d3c\", \"key\": \"STARS\", \"title\": \"\\u2728 STARS [V4]\", \"artists\": \"(prod. FNZ & Ojivolta)\", \"aliases\": [\"Up In The Stars\"], \"description\": \"OG Filename: stars - ref 3_OV_62bpm_\\nTrack seen on a tracklist from the Italy sessions.Has a completely different refrain done by <PERSON>, an alternate Ojivolta outro with a guitar and is way longer than release.\", \"date\": 17073504, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e9b69f4c7c2c60489061d9c97bdfd559\", \"url\": \"https://api.pillowcase.su/api/download/e9b69f4c7c2c60489061d9c97bdfd559\", \"size\": \"7.74 MB\", \"duration\": 170.38}", "aliases": ["Up In The Stars"], "size": "7.74 MB"}, {"id": "street-lights", "name": "✨ STREET LIGHTS [V8]", "artists": ["<PERSON>"], "producers": ["The Alchemist"], "notes": "OG Filename: Street Lights MIX TEST.08_08\nIRKO mix. Samples M.I.A. - Piedras de Color. This version was thought to have been for YEBU era but <PERSON><PERSON> would later confirm that this version is from BBPB era as IRKO started mixing BBPB songs during the Italy sessions. Leaked as part of a groupbuy. Unknown if there's any other mix tests after this. Original snippet leaked February 12th, 2024.", "length": "296.31", "fileDate": 17180640, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/674f5da8b2ee1b7c344c45e5735b634a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/674f5da8b2ee1b7c344c45e5735b634a\", \"key\": \"STREET LIGHTS\", \"title\": \"\\u2728 STREET LIGHTS [V8]\", \"artists\": \"(feat. <PERSON>) (prod. The Alchemist)\", \"description\": \"OG Filename: Street Lights MIX TEST.08_08\\nIRKO mix. Samples M.I.A. - Piedras de Color. This version was thought to have been for YEBU era but <PERSON><PERSON> would later confirm that this version is from BBPB era as IRKO started mixing BBPB songs during the Italy sessions. Leaked as part of a groupbuy. Unknown if there's any other mix tests after this. Original snippet leaked February 12th, 2024.\", \"date\": 17180640, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"296291a6ea29155b78e6d82e1ae8c7a0\", \"url\": \"https://api.pillowcase.su/api/download/296291a6ea29155b78e6d82e1ae8c7a0\", \"size\": \"4.91 MB\", \"duration\": 296.31}", "aliases": [], "size": "4.91 MB"}, {"id": "time-moving-slow", "name": "TIME MOVING SLOW [V2]", "artists": ["???"], "producers": [], "notes": "Version of \"Time Moving Slow\" with vocals from BEAM, and almost a completely different beat. The piano from this version was extracted using AI and used in the other versions, which means it predates all other known versions of the song. Features an unknown female vocalist. It's likely the track was BEAM's first, but it's unknown. Partial snippet posted by BEAM to his Instagram story December 1st, 2024.", "length": "90.07", "fileDate": 17330112, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/8910071795af9b7446681324a078f44f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8910071795af9b7446681324a078f44f\", \"key\": \"TIME MOVING SLOW\", \"title\": \"TIME MOVING SLOW [V2]\", \"artists\": \"(ref. BEAM) (feat. ???)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"Version of \\\"Time Moving Slow\\\" with vocals from BEAM, and almost a completely different beat. The piano from this version was extracted using AI and used in the other versions, which means it predates all other known versions of the song. Features an unknown female vocalist. It's likely the track was BEAM's first, but it's unknown. Partial snippet posted by BEAM to his Instagram story December 1st, 2024.\", \"date\": 17330112, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"aa0897a4209ecd90ac652ed0eb985183\", \"url\": \"https://api.pillowcase.su/api/download/aa0897a4209ecd90ac652ed0eb985183\", \"size\": \"1.61 MB\", \"duration\": 90.07}", "aliases": ["TIME MOVES SLOW"], "size": "1.61 MB"}, {"id": "time-moving-slow-45", "name": "TIME MOVING SLOW [V3]", "artists": [], "producers": ["AyoAA"], "notes": "Version of \"Time Moving Slow\" with either no Ye vocals or mumble Ye vocals is said to exist according to a tracklist posted by Joyboy, who played a snippet of it on stream November 12th, 2024.", "length": "22.49", "fileDate": 17313696, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/f91bcc2d1a6d6e24e52bc9ef5957d449", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f91bcc2d1a6d6e24e52bc9ef5957d449\", \"key\": \"TIME MOVING SLOW\", \"title\": \"TIME MOVING SLOW [V3]\", \"artists\": \"(prod. AyoAA)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"Version of \\\"Time Moving Slow\\\" with either no Ye vocals or mumble Ye vocals is said to exist according to a tracklist posted by Joyboy, who played a snippet of it on stream November 12th, 2024.\", \"date\": 17313696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"8b3de79375c3c346bc465ad62896a3c3\", \"url\": \"https://api.pillowcase.su/api/download/8b3de79375c3c346bc465ad62896a3c3\", \"size\": \"529 kB\", \"duration\": 22.49}", "aliases": ["TIME MOVES SLOW"], "size": "529 kB"}, {"id": "time-moving-slow-46", "name": "TIME MOVING SLOW [V4]", "artists": [], "producers": ["AyoAA"], "notes": "<PERSON> reference track for \"Time Moving Slow\". Snippet leaked January 30th, 2024.", "length": "6.58", "fileDate": 17065728, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/dbc4adf84f9b2cbaf5fc5c59a2b6dc58", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dbc4adf84f9b2cbaf5fc5c59a2b6dc58\", \"key\": \"TIME MOVING SLOW\", \"title\": \"TIME MOVING SLOW [V4]\", \"artists\": \"(ref. <PERSON>) (prod. AyoAA)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"<PERSON> reference track for \\\"Time Moving Slow\\\". Snippet leaked January 30th, 2024.\", \"date\": 17065728, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a058fcc5e9cd9cef40c30dad3dd965a7\", \"url\": \"https://api.pillowcase.su/api/download/a058fcc5e9cd9cef40c30dad3dd965a7\", \"size\": \"5.11 MB\", \"duration\": 6.58}", "aliases": ["TIME MOVES SLOW"], "size": "5.11 MB"}, {"id": "time-moving-slow-47", "name": "TIME MOVING SLOW [V5]", "artists": [], "producers": ["AyoAA"], "notes": "<PERSON><PERSON> reference track for \"Time Moving Slow\". Snippet leaked July 22nd, 2024.", "length": "15.15", "fileDate": 17216064, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/469fbeb548a7a5eeac7814f39d4d3965", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/469fbeb548a7a5eeac7814f39d4d3965\", \"key\": \"TIME MOVING SLOW\", \"title\": \"TIME MOVING SLOW [V5]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. AyoAA)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"Fya Man reference track for \\\"Time Moving Slow\\\". Snippet leaked July 22nd, 2024.\", \"date\": 17216064, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2478778911e01a1fe3c5654196574eb4\", \"url\": \"https://api.pillowcase.su/api/download/2478778911e01a1fe3c5654196574eb4\", \"size\": \"1.87 MB\", \"duration\": 15.15}", "aliases": ["TIME MOVES SLOW"], "size": "1.87 MB"}, {"id": "time-moving-slow-48", "name": "TIME MOVING SLOW [V6]", "artists": [], "producers": ["AyoAA"], "notes": "Version with rougher mixing and an Ice Spice-esque grah sound done by <PERSON> during his verse. Other differences are currently unknown. Snippet leaked April 1st, 2024.", "length": "16.74", "fileDate": 17119296, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/3ecf16221f28d0ac36ac4f1b4d52a7d8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3ecf16221f28d0ac36ac4f1b4d52a7d8\", \"key\": \"TIME MOVING SLOW\", \"title\": \"TIME MOVING SLOW [V6]\", \"artists\": \"(prod. AyoAA)\", \"aliases\": [\"TIME MOVES SLOW\"], \"description\": \"Version with rougher mixing and an Ice Spice-esque grah sound done by <PERSON> during his verse. Other differences are currently unknown. Snippet leaked April 1st, 2024.\", \"date\": 17119296, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"35ef90710758b65c9ae8a884a5108624\", \"url\": \"https://api.pillowcase.su/api/download/35ef90710758b65c9ae8a884a5108624\", \"size\": \"5.28 MB\", \"duration\": 16.74}", "aliases": ["TIME MOVES SLOW"], "size": "5.28 MB"}, {"id": "time", "name": "⭐ TIME [V12]", "artists": [], "producers": ["AyoAA"], "notes": "OG Filename: Time MIX TEST.06_06\nTrack seen on a tracklist from the Italy sessions and track 4 on tracklist posted by Ty <PERSON> $ign. Same version played at Miami brunch back in December. Misses the \"Got my heart broke, I liked that bitch a lot\" line in <PERSON>'s verse and has mixing differences. Seems like he had the idea to record it eventually, considering he rapped it over faintly in the brunch snippet. Unknown if there are any more mix tests after this.", "length": "179.41", "fileDate": 17226432, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/18645169897e97ef481b97e8f90c8540", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/18645169897e97ef481b97e8f90c8540\", \"key\": \"TIME\", \"title\": \"\\u2b50 TIME [V12]\", \"artists\": \"(prod. AyoAA)\", \"aliases\": [\"TIME MOVING SLOW\", \"TIME MOVES SLOW\"], \"description\": \"OG Filename: Time MIX TEST.06_06\\nTrack seen on a tracklist from the Italy sessions and track 4 on tracklist posted by Ty Dolla $ign. Same version played at Miami brunch back in December. Misses the \\\"Got my heart broke, I liked that bitch a lot\\\" line in <PERSON>'s verse and has mixing differences. Seems like he had the idea to record it eventually, considering he rapped it over faintly in the brunch snippet. Unknown if there are any more mix tests after this.\", \"date\": 17226432, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"803475a3232c0ed267772b11eb159c0b\", \"url\": \"https://api.pillowcase.su/api/download/803475a3232c0ed267772b11eb159c0b\", \"size\": \"4.51 MB\", \"duration\": 179.41}", "aliases": ["TIME MOVING SLOW", "TIME MOVES SLOW"], "size": "4.51 MB"}, {"id": "time-50", "name": "TIME [V14]", "artists": [], "producers": ["AyoAA"], "notes": "Version heard playing from outside the Banyan Tree Alula hotel in Saudi, where <PERSON> was staying.", "length": "9.95", "fileDate": 17000064, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/ecf079c51bd448755a84b8c321149ada", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ecf079c51bd448755a84b8c321149ada\", \"key\": \"TIME\", \"title\": \"TIME [V14]\", \"artists\": \"(prod. AyoAA)\", \"aliases\": [\"TIME MOVING SLOW\", \"TIME MOVES SLOW\"], \"description\": \"Version heard playing from outside the Banyan Tree Alula hotel in Saudi, where <PERSON> was staying.\", \"date\": 17000064, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"bb288bb29a5dd15a4856ca4cb8a790a4\", \"url\": \"https://api.pillowcase.su/api/download/bb288bb29a5dd15a4856ca4cb8a790a4\", \"size\": \"5.17 MB\", \"duration\": 9.95}", "aliases": ["TIME MOVING SLOW", "TIME MOVES SLOW"], "size": "5.17 MB"}, {"id": "worship", "name": "✨ WORSHIP [V5]", "artists": ["Chlöe"], "producers": ["Trybishop", "88-<PERSON>", "<PERSON>"], "notes": "Song seen on various tracklists during the Italy sessions. Potentially features no <PERSON> vocals or mumble Ye vocals according to a tracklist posted by <PERSON>. Partial iTunes file leaked July 31st, 2024. <PERSON> <PERSON> interpolating <PERSON>'s part. Original snippet leaked April 1st, 2024, with a snippet of the outro previewed by Joy<PERSON> on stream November 12th, 2024.", "length": "86.85", "fileDate": 17313696, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/71a6f8c0b3a79ec7a52d5a4748801124", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/71a6f8c0b3a79ec7a52d5a4748801124\", \"key\": \"WORSHIP\", \"title\": \"\\u2728 WORSHIP [V5]\", \"artists\": \"(feat. Chl\\u00f6e) (prod. <PERSON><PERSON><PERSON>, 88-<PERSON> & <PERSON>)\", \"description\": \"Song seen on various tracklists during the Italy sessions. Potentially features no Ye vocals or mumble Ye vocals according to a tracklist posted by <PERSON> Leon. Partial iTunes file leaked July 31st, 2024. Has <PERSON> interpolating <PERSON>'s part. Original snippet leaked April 1st, 2024, with a snippet of the outro previewed by <PERSON><PERSON> on stream November 12th, 2024.\", \"date\": 17313696, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3ec6dbbde8d87068b85604cae480730e\", \"url\": \"https://api.pillowcase.su/api/download/3ec6dbbde8d87068b85604cae480730e\", \"size\": \"3.02 MB\", \"duration\": 86.85}", "aliases": [], "size": "3.02 MB"}, {"id": "worship-52", "name": "✨ WORSHIP [V5]", "artists": ["Chlöe"], "producers": ["Trybishop", "88-<PERSON>", "<PERSON>"], "notes": "Song seen on various tracklists during the Italy sessions. Potentially features no <PERSON> vocals or mumble Ye vocals according to a tracklist posted by <PERSON>. Partial iTunes file leaked July 31st, 2024. <PERSON> <PERSON> interpolating <PERSON>'s part. Original snippet leaked April 1st, 2024, with a snippet of the outro previewed by Joy<PERSON> on stream November 12th, 2024.", "length": "13.69", "fileDate": 17313696, "leakDate": "", "availableLength": "Partial", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/bd15cd43d873541e8b783995dec5562c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/bd15cd43d873541e8b783995dec5562c\", \"key\": \"WORSHIP\", \"title\": \"\\u2728 WORSHIP [V5]\", \"artists\": \"(feat. Chl\\u00f6e) (prod. <PERSON><PERSON><PERSON>, <PERSON>-<PERSON> & <PERSON>)\", \"description\": \"Song seen on various tracklists during the Italy sessions. Potentially features no Ye vocals or mumble Ye vocals according to a tracklist posted by <PERSON> Leon. Partial iTunes file leaked July 31st, 2024. Has <PERSON> interpolating <PERSON>'s part. Original snippet leaked April 1st, 2024, with a snippet of the outro previewed by Joy<PERSON> on stream November 12th, 2024.\", \"date\": 17313696, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"adc99128414d7ff4507a06b43eda55e0\", \"url\": \"https://api.pillowcase.su/api/download/adc99128414d7ff4507a06b43eda55e0\", \"size\": \"388 kB\", \"duration\": 13.69}", "aliases": [], "size": "388 kB"}, {"id": "", "name": "???", "artists": [], "producers": [], "notes": "Track made during the 2023 VULTURES sessions. Unknown if it has Ye vocals or what song it truly is yet. Samples \"Hanging On\" by KNOWER. Snippet leaked August 12th, 2024.", "length": "6.84", "fileDate": 17234208, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/026b8ec877415e8123efcdec4e76a2f0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/026b8ec877415e8123efcdec4e76a2f0\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Switch On Me\"], \"description\": \"Track made during the 2023 VULTURES sessions. Unknown if it has Ye vocals or what song it truly is yet. Samples \\\"Hanging On\\\" by KNOWER. Snippet leaked August 12th, 2024.\", \"date\": 17234208, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7940a78606a37b34ce8a66afceb84900\", \"url\": \"https://api.pillowcase.su/api/download/7940a78606a37b34ce8a66afceb84900\", \"size\": \"5.12 MB\", \"duration\": 6.84}", "aliases": ["Switch On Me"], "size": "5.12 MB"}, {"id": "-54", "name": "???", "artists": [], "producers": [], "notes": "Track made during the 2023 VULTURES sessions. Unknown if it has Ye vocals or what song it truly is yet. Snippet leaked August 12th, 2024.", "length": "8.18", "fileDate": 17234208, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/b00c1c41af0c95b784c23b789eddfd3f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b00c1c41af0c95b784c23b789eddfd3f\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Bed To Lay In\"], \"description\": \"Track made during the 2023 VULTURES sessions. Unknown if it has Ye vocals or what song it truly is yet. Snippet leaked August 12th, 2024.\", \"date\": 17234208, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"e772760905e5b50763146b2f0b3aaaf9\", \"url\": \"https://api.pillowcase.su/api/download/e772760905e5b50763146b2f0b3aaaf9\", \"size\": \"1.76 MB\", \"duration\": 8.18}", "aliases": ["Bed To Lay In"], "size": "1.76 MB"}, {"id": "-55", "name": "???", "artists": [], "producers": [], "notes": "Snippet posted briefly to <PERSON><PERSON>'s Instagram before being deleted minutes later. Confirmed to be a Ye song worked on in Italy by <PERSON><PERSON>.", "length": "2.38", "fileDate": 16930080, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/1be6ef06f7daf58edbdf4f53c1bd20e3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1be6ef06f7daf58edbdf4f53c1bd20e3\", \"key\": \"???\", \"title\": \"???\", \"description\": \"Snippet posted briefly to <PERSON><PERSON>'s Instagram before being deleted minutes later. Confirmed to be a Ye song worked on in Italy by Luit.\", \"date\": 16930080, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f5a4a9c961398796bb05f83dc1177144\", \"url\": \"https://api.pillowcase.su/api/download/f5a4a9c961398796bb05f83dc1177144\", \"size\": \"954 kB\", \"duration\": 2.38}", "aliases": [], "size": "954 kB"}, {"id": "-56", "name": "???", "artists": [], "producers": [], "notes": "Unknown song made during the Italy sessions. Snippet played on stream by Joyboy November 12th, 2024. <PERSON><PERSON> \"Locked In A Feeling\" by <PERSON><PERSON><PERSON>.", "length": "34.77", "fileDate": 17313696, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/f1acb9339037f60eb4f96fcb58fe09cf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f1acb9339037f60eb4f96fcb58fe09cf\", \"key\": \"???\", \"title\": \"???\", \"description\": \"Unknown song made during the Italy sessions. Snippet played on stream by Joyboy November 12th, 2024. <PERSON><PERSON> \\\"Locked In A Feeling\\\" by <PERSON><PERSON><PERSON>.\", \"date\": 17313696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f5811a4dd8544de91d56ac217e7d6166\", \"url\": \"https://api.pillowcase.su/api/download/f5811a4dd8544de91d56ac217e7d6166\", \"size\": \"726 kB\", \"duration\": 34.77}", "aliases": [], "size": "726 kB"}, {"id": "-57", "name": "???", "artists": [], "producers": [], "notes": "Unknown song played by <PERSON><PERSON> on stream November 12th, 2024.", "length": "1.44", "fileDate": 17313696, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/f92b5ec18388c9777cbc59432321072c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f92b5ec18388c9777cbc59432321072c\", \"key\": \"???\", \"title\": \"???\", \"description\": \"Unknown song played by <PERSON><PERSON> on stream November 12th, 2024.\", \"date\": 17313696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"bcd933038f1d590e79e16839c09db3aa\", \"url\": \"https://api.pillowcase.su/api/download/bcd933038f1d590e79e16839c09db3aa\", \"size\": \"192 kB\", \"duration\": 1.44}", "aliases": [], "size": "192 kB"}, {"id": "-58", "name": "???", "artists": [], "producers": [], "notes": "Unknown song played by <PERSON><PERSON> on stream November 12th, 2024.", "length": "4.8100000000000005", "fileDate": 17313696, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/3fe8b795320ff9e669e8fa12d99d217f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3fe8b795320ff9e669e8fa12d99d217f\", \"key\": \"???\", \"title\": \"???\", \"description\": \"Unknown song played by <PERSON><PERSON> on stream November 12th, 2024.\", \"date\": 17313696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"c2f7dae81b7259007c4e747603ae5c74\", \"url\": \"https://api.pillowcase.su/api/download/c2f7dae81b7259007c4e747603ae5c74\", \"size\": \"246 kB\", \"duration\": 4.8100000000000005}", "aliases": [], "size": "246 kB"}, {"id": "all-yours", "name": "<PERSON> - All Yours [V1]", "artists": [], "producers": ["<PERSON><PERSON>", "Wheezy"], "notes": "OG Filename: All Yours (Wheezy) Ruff\n\"Field Trip\" started as a beat originally made for Lil Uzi <PERSON>, before being given to <PERSON>. From sometime in 2023. Leaked after a blind buy.", "length": "125.96", "fileDate": 17294688, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/d215a62c8598562dad4b5bfbf82542d0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d215a62c8598562dad4b5bfbf82542d0\", \"key\": \"All Yours\", \"title\": \"<PERSON> Uzi Vert - All Yours [V1]\", \"artists\": \"(prod. <PERSON><PERSON> & Wheezy)\", \"aliases\": [\"FIELD TRIP\"], \"description\": \"OG Filename: All Yours (Wheezy) Ruff\\n\\\"Field Trip\\\" started as a beat originally made for Lil Uzi Vert, before being given to <PERSON>. From sometime in 2023. Leaked after a blind buy.\", \"date\": 17294688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"dfc5746e2577dde98033c6945e57038c\", \"url\": \"https://api.pillowcase.su/api/download/dfc5746e2577dde98033c6945e57038c\", \"size\": \"2.02 MB\", \"duration\": 125.96}", "aliases": ["FIELD TRIP"], "size": "2.02 MB"}, {"id": "rain-on-me", "name": "<PERSON><PERSON><PERSON> <PERSON> <PERSON> On Me [V1]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON><PERSON> Rain on me\nEarly version of \"Once Again\", before <PERSON> and <PERSON>$ took it for VULTURES. <PERSON><PERSON><PERSON> said on her Twitter that it used to be a \"country\" record that wasn't intended for VULTURES.", "length": "18.49", "fileDate": 17072640, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "bad-bitch-playbook", "originalUrl": "https://pillowcase.su/f/b65eab1e7abce30719547ab58b2d929e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b65eab1e7abce30719547ab58b2d929e\", \"key\": \"Rain On Me\", \"title\": \"<PERSON><PERSON><PERSON> <PERSON> <PERSON> On Me [V1]\", \"aliases\": [\"Everything\", \"Once Again\"], \"description\": \"OG Filename: <PERSON><PERSON><PERSON> Rain on me\\nEarly version of \\\"Once Again\\\", before <PERSON> and <PERSON>$ took it for VULTURES. <PERSON><PERSON><PERSON> said on her Twitter that it used to be a \\\"country\\\" record that wasn't intended for VULTURES.\", \"date\": 17072640, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c78cb3a27570217d7280de002352f64a\", \"url\": \"https://api.pillowcase.su/api/download/c78cb3a27570217d7280de002352f64a\", \"size\": \"1.93 MB\", \"duration\": 18.49}", "aliases": ["Everything", "Once Again"], "size": "1.93 MB"}]}