{"id": "young-mi-fit", "name": "Young Mi$fit", "description": "A mixtape made in 2012, released on Nov 11, 2012. The mixtape was (probably) first called Kream, as the filename for OG version of <PERSON><PERSON><PERSON> suggests, but later it was retitled and probably reworked to the Young Mi$fit mixtape. The mixtape is fully availbable and OG files for the whole project leaked on Apr 27, 2021", "backgroundColor": "rgb(219, 111, 111)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17H7rKo5DiS8feFnjKE_Y6GzqiuWYBOWpUlxQCFFirOSD209gqXcqlUuEpUcmL422qJSgto0pxhNMDY-HUdn1tXmOLmatyXipwy4JXzY7Kt0TAtSmhEw59lnljAPs-BHb5yvKqtl9M1uZAKgTfbQ?key=QrLRwW_6ASWi9mCdhUkgTQ", "tracks": [{"id": "36-<PERSON>z", "name": "36 Villainz", "artists": [], "producers": ["Cold Hart", "DJ <PERSON><PERSON><PERSON>"], "notes": "OG Filename: 1. 36 Villianz\nOG Filename (Metadata): 36illvillianz\nOG File for track 1 from the Young Mi$fit mixtape, \"36 Villainz\".", "length": "3:19", "fileDate": 16194816, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "young-mi-fit", "originalUrl": "https://music.froste.lol/song/7d6861b3409a78645fa79133b19fdf5f/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/7d6861b3409a78645fa79133b19fdf5f/play\", \"key\": \"36 Villainz\", \"title\": \"36 Villainz\", \"artists\": \"(prod. <PERSON> & <PERSON>)\", \"aliases\": [\"36IllVillianz\"], \"description\": \"OG Filename: 1. 36 Villianz\\nOG Filename (Metadata): 36illvillianz\\nOG File for track 1 from the Young Mi$fit mixtape, \\\"36 Villainz\\\".\", \"date\": 16194816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"0cef466201c77d299b13614dc1028ce5\", \"url\": \"https://api.pillowcase.su/api/download/0cef466201c77d299b13614dc1028ce5\", \"size\": \"3.14 MB\", \"duration\": 199.44}", "aliases": ["36<PERSON>ll<PERSON><PERSON><PERSON><PERSON>"], "size": "3.14 MB"}, {"id": "zombie", "name": "ZOMBIE$", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: 2. ZOMBIE$\nOG Filename (Metadata): ZOMBIE$\nOG File for a song from the Young Mi$fit mixtape, \"ZOMBIE$\".", "length": "1:40", "fileDate": 16194816, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "young-mi-fit", "originalUrl": "https://music.froste.lol/song/79008b5e9eacbe96890935e9aed809b2/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/79008b5e9eacbe96890935e9aed809b2/play\", \"key\": \"ZOMBIE$\", \"title\": \"ZOMBIE$\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: 2. ZOMBIE$\\nOG Filename (Metadata): ZOMBIE$\\nOG File for a song from the Young Mi$fit mixtape, \\\"ZOMBIE$\\\".\", \"date\": 16194816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"f1b1a2987835b7e4502d2d8dd6b89702\", \"url\": \"https://api.pillowcase.su/api/download/f1b1a2987835b7e4502d2d8dd6b89702\", \"size\": \"2.35 MB\", \"duration\": 100.1}", "aliases": [], "size": "2.35 MB"}, {"id": "kit-1", "name": "$kit 1", "artists": [], "producers": [], "notes": "OG Filename: 3. $kit\nOG File for track 3 from the Young Mi$fit mixtape, \"$kit 1\".", "length": "0:40", "fileDate": 16194816, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "young-mi-fit", "originalUrl": "https://music.froste.lol/song/7987db6d0462834fe768afc90e403d92/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/7987db6d0462834fe768afc90e403d92/play\", \"key\": \"$kit 1\", \"title\": \"$kit 1\", \"description\": \"OG Filename: 3. $kit\\nOG File for track 3 from the Young Mi$fit mixtape, \\\"$kit 1\\\".\", \"date\": 16194816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a490f8147c7783735f486fe7eae2a238\", \"url\": \"https://api.pillowcase.su/api/download/a490f8147c7783735f486fe7eae2a238\", \"size\": \"2.2 MB\", \"duration\": 40.8}", "aliases": [], "size": "2.2 MB"}, {"id": "blue-crystal", "name": "Blue Crystal$ [V2]", "artists": ["<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: 4. Blue Crystal$\nOG Filename (Metadata): Blue Cystal$. PROD. tomvoduz\nOG File for track 4 from the Young Mi$fit mixtape, \"Blue Crystal$\".", "length": "3:38", "fileDate": 16194816, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "young-mi-fit", "originalUrl": "https://music.froste.lol/song/0c36fe371551a2603496a5b3da1d6ff8/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/0c36fe371551a2603496a5b3da1d6ff8/play\", \"key\": \"Blue Crystal$\", \"title\": \"Blue Crystal$ [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: 4. Blue Crystal$\\nOG Filename (Metadata): Blue Cystal$. PROD. tomvoduz\\nOG File for track 4 from the Young Mi$fit mixtape, \\\"Blue Crystal$\\\".\", \"date\": 16194816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a300162237593b2da2223f2c914857b0\", \"url\": \"https://api.pillowcase.su/api/download/a300162237593b2da2223f2c914857b0\", \"size\": \"3.3 MB\", \"duration\": 218.88}", "aliases": [], "size": "3.3 MB"}, {"id": "teeze", "name": "⭐ $teeze [V2]", "artists": [], "producers": ["TDeeZy"], "notes": "OG Filename: 5. <PERSON><PERSON><PERSON> [Pro. By TDEEZY]\nOG Filename (Metadata): <PERSON><PERSON><PERSON> [Pro. By TDEEZY]\nOG File for track 5 from the Young Mi$fit mixtape, \"$teeze\".", "length": "2:50", "fileDate": 16194816, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "young-mi-fit", "originalUrl": "https://music.froste.lol/song/0b3fb67c22b15299f13f7ebc61861df6/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/0b3fb67c22b15299f13f7ebc61861df6/play\", \"key\": \"$teeze\", \"title\": \"\\u2b50 $teeze [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: 5. <PERSON><PERSON><PERSON> [Pro. By TDEEZY]\\nOG Filename (Metadata): Steeze [Pro. By TDEEZY]\\nOG File for track 5 from the Young Mi$fit mixtape, \\\"$teeze\\\".\", \"date\": 16194816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c815d61d910c19b1828157ec98bdbc2a\", \"url\": \"https://api.pillowcase.su/api/download/c815d61d910c19b1828157ec98bdbc2a\", \"size\": \"2.91 MB\", \"duration\": 170.68}", "aliases": [], "size": "2.91 MB"}, {"id": "club-pink", "name": "Club Pink", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Cold Hart"], "notes": "OG Filename: 6. Club Pink Ft. Nessly\nOG Filename (Metadata): Club Pink Ft. Nessly\nOG File for track 6 from the Young Mi$fit mixtape, \"Club Pink\".", "length": "3:26", "fileDate": 16194816, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "young-mi-fit", "originalUrl": "https://music.froste.lol/song/bfe783140a340c88c4ae7aefb1f7a8be/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/bfe783140a340c88c4ae7aefb1f7a8be/play\", \"key\": \"Club Pink\", \"title\": \"Club Pink\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: 6. Club Pink Ft. Nessly\\nOG Filename (Metadata): Club Pink Ft. Nessly\\nOG File for track 6 from the Young Mi$fit mixtape, \\\"Club Pink\\\".\", \"date\": 16194816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"2157ffd1219ec81001a715155bb99b1c\", \"url\": \"https://api.pillowcase.su/api/download/2157ffd1219ec81001a715155bb99b1c\", \"size\": \"4.85 MB\", \"duration\": 206.39}", "aliases": [], "size": "4.85 MB"}, {"id": "van-go", "name": "<PERSON>", "artists": [], "producers": ["Cold Hart"], "notes": "OG Filename: 7. <PERSON>G Filename (Metadata): <PERSON>G File for track 7 from the Young Mi$fit mixtape, \"Van Go\".", "length": "", "fileDate": 16194816, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "young-mi-fit", "originalUrl": "https://music.froste.lol/song/57321e8bc9d38157a24f633b1e855400/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/57321e8bc9d38157a24f633b1e855400/play\", \"key\": \"Van Go\", \"title\": \"Van Go\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: 7. <PERSON>\\nOG Filename (Metadata): Van <PERSON>\\nOG File for track 7 from the Young Mi$fit mixtape, \\\"Van Go\\\".\", \"date\": 16194816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}, {"id": "kit-2", "name": "$kit 2", "artists": [], "producers": [], "notes": "OG Filename: 8. $kit\nOG File for track 8 from the Young Mi$fit mixtape, \"$kit 2\".", "length": "", "fileDate": 16194816, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "young-mi-fit", "originalUrl": "https://music.froste.lol/song/430b6d841af211310cbbed3bf81b8777/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/430b6d841af211310cbbed3bf81b8777/play\", \"key\": \"$kit 2\", \"title\": \"$kit 2\", \"description\": \"OG Filename: 8. $kit\\nOG File for track 8 from the Young Mi$fit mixtape, \\\"$kit 2\\\".\", \"date\": 16194816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}, {"id": "paper-cha-e", "name": "Paper Cha$e", "artists": [], "producers": ["SonoSoloSoul"], "notes": "OG Filename: 9. Paper Cha$e\nOG Filename (Metadata): Paper Cha$e\nOG File for track 9 from the Young Mi$fit mixtape, \"Paper Cha$e\".", "length": "", "fileDate": 16194816, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "young-mi-fit", "originalUrl": "https://music.froste.lol/song/878bfff0914b0bb02029bb714485fb81/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/878bfff0914b0bb02029bb714485fb81/play\", \"key\": \"Paper Cha$e\", \"title\": \"Paper Cha$e\", \"artists\": \"(prod. SonoSoloSoul)\", \"description\": \"OG Filename: 9. Paper Cha$e\\nOG Filename (Metadata): Paper Cha$e\\nOG File for track 9 from the Young Mi$fit mixtape, \\\"Paper Cha$e\\\".\", \"date\": 16194816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}]}