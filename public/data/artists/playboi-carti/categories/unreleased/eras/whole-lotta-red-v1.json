{"id": "whole-lotta-red-v1", "name": "Whole Lotta Red [V1]", "description": "(Aug 9, 2018) <PERSON><PERSON> makes Skeleton, where he mentions Whole Lotta Red\n(Jan 1, 2019) <PERSON><PERSON> begins expirimenting with his vocal range more", "backgroundColor": "rgb(194, 38, 46)", "coverImage": "", "tracks": [{"id": "all-day", "name": "🗑️ All Day", "artists": [], "producers": ["Astro"], "notes": "OG Filename: AllDay PBC x Astro 8.24.18 SR\nA throwaway from the 'Whole Lotta Red' sessions. The reason why it's known as the \"caveman song\", is because the snippet previewed at the time (which was practically the entire song), had the leaker watching a movie of some sort, with cavemen in the background. Turning into some sort of a meme, because of how repetitive and lackluster the song is, and because its strange instrumental fitted just fine with the cavemen in the snippet.", "length": "2:25", "fileDate": 16585344, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/6d625f99dc3295430d6bc30dc627b4ee/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/6d625f99dc3295430d6bc30dc627b4ee/play\", \"key\": \"All Day\", \"title\": \"\\ud83d\\uddd1\\ufe0f All Day\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"Ready To Die\", \"\\\"Caveman Song\\\"\"], \"description\": \"OG Filename: AllDay PBC x Astro 8.24.18 SR\\nA throwaway from the 'Whole Lotta Red' sessions. The reason why it's known as the \\\"caveman song\\\", is because the snippet previewed at the time (which was practically the entire song), had the leaker watching a movie of some sort, with cavemen in the background. Turning into some sort of a meme, because of how repetitive and lackluster the song is, and because its strange instrumental fitted just fine with the cavemen in the snippet.\", \"date\": 16585344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c3b83adac2c7dd0696fc47be15878038\", \"url\": \"https://api.pillowcase.su/api/download/c3b83adac2c7dd0696fc47be15878038\", \"size\": \"2.33 MB\", \"duration\": 145.61}", "aliases": ["Ready To Die", "\"Caveman Song\""], "size": "2.33 MB"}, {"id": "back-room", "name": "✨ Back Room", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: BackRoom PBC x Astro 8.22.18 SR\nLeaked on April 11, 2020.", "length": "3:00", "fileDate": 15865632, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/bed4862152775a57d6187c3fd99512ac/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/bed4862152775a57d6187c3fd99512ac/play\", \"key\": \"Back Room\", \"title\": \"\\u2728 Back Room\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>) \", \"aliases\": [\"Backroom\", \"Count That Dough\", \"Blackroom\"], \"description\": \"OG Filename: BackRoom PBC x Astro 8.22.18 SR\\nLeaked on April 11, 2020.\", \"date\": 15865632, \"available\": [\"OG File\", \"rgb(0, 0, 0)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4b98a57800664ca8c2e2e329ac3457a6\", \"url\": \"https://api.pillowcase.su/api/download/4b98a57800664ca8c2e2e329ac3457a6\", \"size\": \"3.68 MB\", \"duration\": 180.96}", "aliases": ["Backroom", "Count That <PERSON>", "Blackroom"], "size": "3.68 MB"}, {"id": "back-up", "name": "✨ Back Up [V2]", "artists": ["Offset"], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: Back Up 9.8.18\nA throwaway from the 'Whole Lotta Red' sessions. This version has new vocals from <PERSON>set. OGF surfaced on October 10, 2024.", "length": "2:56", "fileDate": 15670368, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/e60510a7dbcfeeaa9a6bd3b6ba586603/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e60510a7dbcfeeaa9a6bd3b6ba586603/play\", \"key\": \"Back Up\", \"title\": \"\\u2728 Back Up [V2]\", \"artists\": \"(feat. Offset) (prod. <PERSON><PERSON><PERSON>) \", \"aliases\": [\"Smokin Dope\"], \"description\": \"OG Filename: Back Up 9.8.18\\nA throwaway from the 'Whole Lotta Red' sessions. This version has new vocals from Offset. OGF surfaced on October 10, 2024.\", \"date\": 15670368, \"available\": [\"OG File\", \"rgb(0, 0, 0)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1b9c587862b475fee540f4e242982952\", \"url\": \"https://api.pillowcase.su/api/download/1b9c587862b475fee540f4e242982952\", \"size\": \"3.61 MB\", \"duration\": 176.4}", "aliases": ["<PERSON><PERSON><PERSON>"], "size": "3.61 MB"}, {"id": "bouldercrest", "name": "Bouldercrest [V1]", "artists": ["Offset"], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: Bouldercrest 9.8.18\nOriginal demo of \"Free PDE\". OGF surfaced on October 10, 2024.", "length": "2:49", "fileDate": 15494112, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/eb0dd703cb560ef2f4d6f3c1b3b0a450/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/eb0dd703cb560ef2f4d6f3c1b3b0a450/play\", \"key\": \"<PERSON>crest\", \"title\": \"<PERSON>crest [V1]\", \"artists\": \"(feat. Offset) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Free PDE\", \"Piru\"], \"description\": \"OG Filename: Bouldercrest 9.8.18\\nOriginal demo of \\\"Free PDE\\\". OGF surfaced on October 10, 2024.\", \"date\": 15494112, \"available\": [\"OG File\", \"rgb(0, 0, 0)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a0065f711b6e7a6f211888946e089ca2\", \"url\": \"https://api.pillowcase.su/api/download/a0065f711b6e7a6f211888946e089ca2\", \"size\": \"2.71 MB\", \"duration\": 169.13}", "aliases": ["Free PDE", "<PERSON><PERSON>"], "size": "2.71 MB"}, {"id": "butterfly-doors", "name": "✨ Butterfly Doors", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: Butterfly Doors 8.9.18\nPreviewed in the Leaked Die Lit/Whole Lotta Red session footage.", "length": "2:43", "fileDate": 15675552, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/bc02a2457c982f742eb91aba60bfa1ad/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/bc02a2457c982f742eb91aba60bfa1ad/play\", \"key\": \"Butterfly Doors\", \"title\": \"\\u2728 Butterfly Doors\", \"artists\": \"(prod. <PERSON>) \", \"aliases\": [\"War\", \"7AM\"], \"description\": \"OG Filename: Butterfly Doors 8.9.18\\nPreviewed in the Leaked Die Lit/Whole Lotta Red session footage.\", \"date\": 15675552, \"available\": [\"OG File\", \"rgb(0, 0, 0)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4af34717382ab8abca317cc555a5e235\", \"url\": \"https://api.pillowcase.su/api/download/4af34717382ab8abca317cc555a5e235\", \"size\": \"2.61 MB\", \"duration\": 163.15}", "aliases": ["War", "7AM"], "size": "2.61 MB"}, {"id": "canc-n", "name": "⭐ Cancún [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Cancun 9.12.18\nA throwaway from the 'Whole Lotta Red' sessions. One of the most famous unreleased songs by <PERSON><PERSON>.", "length": "2:22", "fileDate": 15674688, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/832844c455944c7343931a6d10e259b8/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/832844c455944c7343931a6d10e259b8/play\", \"key\": \"Canc\\u00fan\", \"title\": \"\\u2b50 Canc\\u00fan [V2]\", \"artists\": \"(prod. <PERSON><PERSON>er<PERSON>)\", \"aliases\": [\"Money Jumpin'\", \"My Stummy Hurt\"], \"description\": \"OG Filename: Cancun 9.12.18\\nA throwaway from the 'Whole Lotta Red' sessions. One of the most famous unreleased songs by <PERSON><PERSON>.\", \"date\": 15674688, \"available\": [\"OG File\", \"rgb(0, 0, 0)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9f9e161c76507deaed17924b9c090178\", \"url\": \"https://api.pillowcase.su/api/download/9f9e161c76507deaed17924b9c090178\", \"size\": \"3.07 MB\", \"duration\": 142.5}", "aliases": ["Money Jumpin'", "My Stummy Hurt"], "size": "3.07 MB"}, {"id": "fashion-killer", "name": "✨ Fashion Killer [V2]", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: FashionKiller Feature PBC x Stankonia 8.24.18 SR\nFeatures a verse from <PERSON> and new <PERSON><PERSON><PERSON> verse.", "length": "2:24", "fileDate": 16585344, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/fea6a3e734a65a555bf7126504d21844/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/fea6a3e734a65a555bf7126504d21844/play\", \"key\": \"Fashion Killer\", \"title\": \"\\u2728 Fashion Killer [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON>\", \"Choppa Go\"], \"description\": \"OG Filename: FashionKiller Feature PBC x Stankonia 8.24.18 SR\\nFeatures a verse from <PERSON>lock and new <PERSON><PERSON><PERSON> verse.\", \"date\": 16585344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ca57a38599c8d5550953334f8b581baf\", \"url\": \"https://api.pillowcase.su/api/download/ca57a38599c8d5550953334f8b581baf\", \"size\": \"3.1 MB\", \"duration\": 144.74}", "aliases": ["<PERSON>", "Choppa Go"], "size": "3.1 MB"}, {"id": "hellcat", "name": "Hellcat [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Leaked on May 29, 2019 and only contains the hooks with opens.", "length": "2:47", "fileDate": 15590880, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/96244403f8e06f8f23ee80a5e867e35f/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/96244403f8e06f8f23ee80a5e867e35f/play\", \"key\": \"Hellcat\", \"title\": \"Hellcat [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Leaked on May 29, 2019 and only contains the hooks with opens.\", \"date\": 15590880, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"0fa05821c1e1bf938312b071980e7094\", \"url\": \"https://api.pillowcase.su/api/download/0fa05821c1e1bf938312b071980e7094\", \"size\": \"2.68 MB\", \"duration\": 167.57}", "aliases": [], "size": "2.68 MB"}, {"id": "hellcat-9", "name": "Hellcat [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Leaked on May 29, 2019 with extra vocals, adlibs and an open verse.", "length": "2:47", "fileDate": 15590880, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/78cd4106c87eb9c129883d5104561a72/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/78cd4106c87eb9c129883d5104561a72/play\", \"key\": \"Hellcat\", \"title\": \"Hellcat [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Leaked on May 29, 2019 with extra vocals, adlibs and an open verse.\", \"date\": 15590880, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c664a5bfb6034b1e2f6b7477a5f5f9e3\", \"url\": \"https://api.pillowcase.su/api/download/c664a5bfb6034b1e2f6b7477a5f5f9e3\", \"size\": \"2.68 MB\", \"duration\": 167.57}", "aliases": [], "size": "2.68 MB"}, {"id": "hellcat-10", "name": "⭐ Hellcat [V4]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Leaked on May 26, 2019 and is said to be the most complete version.", "length": "3:40", "fileDate": 15588288, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/00df125b555ca397edffc92168669716/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/00df125b555ca397edffc92168669716/play\", \"key\": \"Hellcat\", \"title\": \"\\u2b50 Hellcat [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Leaked on May 26, 2019 and is said to be the most complete version.\", \"date\": 15588288, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7b408772891f75e80b82b9f9a565b43e\", \"url\": \"https://api.pillowcase.su/api/download/7b408772891f75e80b82b9f9a565b43e\", \"size\": \"3.54 MB\", \"duration\": 220.94}", "aliases": [], "size": "3.54 MB"}, {"id": "i-got-ur-back-slatt-i-promise-u", "name": "⭐️ i got ur back slatt, i promise u! [V1]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: PBC x Circle House 9.9.18-2\nLeaked on August 20, 2019.", "length": "", "fileDate": 15662592, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/280a2022396a1cb7fff157622604183f/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/280a2022396a1cb7fff157622604183f/play\", \"key\": \"i got ur back slatt, i promise u!\", \"title\": \"\\u2b50\\ufe0f i got ur back slatt, i promise u! [V1]\", \"artists\": \"(prod. 16yrold)\", \"aliases\": [\"Red On Red\", \"What You Talking Bout\", \"Talm Bout\", \"\\\"i got ur back slatt\", \"i promise u !\\\"\"], \"description\": \"OG Filename: PBC x Circle House 9.9.18-2\\nLeaked on August 20, 2019.\", \"date\": 15662592, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Red On Red", "What You Talking Bout", "<PERSON><PERSON>", "\"i got ur back slatt", "i promise u !\""], "size": ""}, {"id": "ronald", "name": "⭐ <PERSON><PERSON> - <PERSON> [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "notes": "OG Filename: <PERSON>ti x Juice 10.10.18\nTrack was never planned to be released. Uploaded to the producers SoundCloud in March 2019. Later removed for unknown reasons.", "length": "", "fileDate": 15460416, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/a94e59abca1fec9916ab48c75b5dc510/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a94e59abca1fec9916ab48c75b5dc510/play\", \"key\": \"<PERSON>\", \"title\": \"\\u2b50 Juice WRLD - <PERSON> [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Gezin & Nobu)\", \"aliases\": [\"Want To\"], \"description\": \"OG Filename: Carti x Juice 10.10.18\\nTrack was never planned to be released. Uploaded to the producers SoundCloud in March 2019. Later removed for unknown reasons.\", \"date\": 15460416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Want To"], "size": ""}, {"id": "that-s-a-50", "name": "✨ Juice WRLD - That's A 50 [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: Thats a 50 PBC 8.24.18\nThe second version of the song with vocals from <PERSON><PERSON><PERSON>. <PERSON><PERSON>'s vocals were taken from \"Racks\" with RX Peso. OGF leaked on Mar 30, 2024", "length": "", "fileDate": 15670368, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/ea3d715cad0e81c41cfc6f10bf6a4cbb/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/ea3d715cad0e81c41cfc6f10bf6a4cbb/play\", \"key\": \"That's A 50\", \"title\": \"\\u2728 Juice WRLD - That's A 50 [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Gezin)\", \"description\": \"OG Filename: Thats a 50 PBC 8.24.18\\nThe second version of the song with vocals from <PERSON><PERSON><PERSON>. <PERSON><PERSON>'s vocals were taken from \\\"Racks\\\" with RX Peso. OGF leaked on Mar 30, 2024\", \"date\": 15670368, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "get-dripped", "name": "<PERSON> - <PERSON>", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["EarlOnTheBeat", "MitchGoneMad"], "notes": "OG Filename: <PERSON><PERSON> x Boat1 8.21.18\nOriginal version of \"Get Dripped\" with some additional mumble on <PERSON><PERSON>'s verse.", "length": "", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/89eb51da8c28fe575b7f2b338cbe67fa/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/89eb51da8c28fe575b7f2b338cbe67fa/play\", \"key\": \"Get Dripped\", \"title\": \"<PERSON> Yachty - Get Dripped\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. EarlOnTheBeat & MitchGoneMad)\", \"description\": \"OG Filename: <PERSON>ti x Boat1 8.21.18\\nOriginal version of \\\"Get Dripped\\\" with some additional mumble on <PERSON><PERSON>'s verse.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "neon", "name": "Neon [V1]", "artists": [], "producers": ["Maaly Raw"], "notes": "Leaked on May 29, 2019. Recorded sometime before October 25th, 2018.", "length": "", "fileDate": 15590880, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/9ed1a347a3722842ba8f93528912cd12/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/9ed1a347a3722842ba8f93528912cd12/play\", \"key\": \"Neon\", \"title\": \"Neon [V1]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"New N3on\", \"N3on\", \"Whole Lotta Neon\", \"X10\", \"Whole Lotta Red\"], \"description\": \"Leaked on May 29, 2019. Recorded sometime before October 25th, 2018.\", \"date\": 15590880, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["New N3on", "N3on", "Whole Lotta Neon", "X10", "Whole Lotta Red"], "size": ""}, {"id": "neon-16", "name": "Neon [V2]", "artists": [], "producers": ["Maaly Raw"], "notes": "OG Filename: PBC x Means 10.25.18 MAALY\nHas three new mumble lines, different beat for the open verse, and new mixing.", "length": "3:17", "fileDate": 15726528, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/d39573656fee7caa923db5d4cc902061/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d39573656fee7caa923db5d4cc902061/play\", \"key\": \"Neon\", \"title\": \"Neon [V2]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"New N3on\", \"N3on\", \"Whole Lotta Neon\", \"X10\", \"Whole Lotta Red\"], \"description\": \"OG Filename: PBC x Means 10.25.18 MAALY\\nHas three new mumble lines, different beat for the open verse, and new mixing.\", \"date\": 15726528, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"596825c3f534325ef7bdf537376b189f\", \"url\": \"https://api.pillowcase.su/api/download/596825c3f534325ef7bdf537376b189f\", \"size\": \"3.16 MB\", \"duration\": 197.45}", "aliases": ["New N3on", "N3on", "Whole Lotta Neon", "X10", "Whole Lotta Red"], "size": "3.16 MB"}, {"id": "nine-nine", "name": "✨ Nine Nine", "artists": [], "producers": ["Metro Boomin"], "notes": "Leaked on November 10, 2020.", "length": "2:19", "fileDate": 16049664, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/d6ba175faeb8003d18248d495cafc3c2/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d6ba175faeb8003d18248d495cafc3c2/play\", \"key\": \"Nine Nine\", \"title\": \"\\u2728 Nine Nine\", \"artists\": \"(prod. <PERSON>in)\", \"aliases\": [\"Lies\", \"Nines\", \"One Of The Nines\"], \"description\": \"Leaked on November 10, 2020.\", \"date\": 16049664, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2c600d1e352c3053dc09b5f594414799\", \"url\": \"https://api.pillowcase.su/api/download/2c600d1e352c3053dc09b5f594414799\", \"size\": \"3.02 MB\", \"duration\": 139.86}", "aliases": ["Lies", "Nines", "One Of The Nines"], "size": "3.02 MB"}, {"id": "not-real", "name": "✨ Not Real [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Not Real\nOriginal version from October 2018 with an open verse. Said to be titled Not Real before WLR [V2] in 2020. On March 31, 2020, leakers revealed that alongside the name being called \"Not Real\" and it being the first track on the April 2019 WLR TL, they also mentioned that the OG Filename was also titled \"Not Real\". Song was recorded at Means St Studios sometime in October 2018.", "length": "2:37", "fileDate": 15597792, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/e7178a921fbdb8eb71783e82f735d706/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e7178a921fbdb8eb71783e82f735d706/play\", \"key\": \"Not Real\", \"title\": \"\\u2728 Not Real [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"molly.jpeg\", \"<PERSON>\", \"No Stylist\", \"molly.jpg\", \"Look At These Diamonds They Shine\"], \"description\": \"OG Filename: Not Real\\nOriginal version from October 2018 with an open verse. Said to be titled Not Real before WLR [V2] in 2020. On March 31, 2020, leakers revealed that alongside the name being called \\\"Not Real\\\" and it being the first track on the April 2019 WLR TL, they also mentioned that the OG Filename was also titled \\\"Not Real\\\". Song was recorded at Means St Studios sometime in October 2018.\", \"date\": 15597792, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1436a4037d3a282f647f3794358eeb06\", \"url\": \"https://api.pillowcase.su/api/download/1436a4037d3a282f647f3794358eeb06\", \"size\": \"3.47 MB\", \"duration\": 157.36}", "aliases": ["molly.jpeg", "<PERSON>", "No Stylist", "molly.jpg", "Look At These Diamonds They Shine"], "size": "3.47 MB"}, {"id": "one-day", "name": "✨ One Day (Remix)", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Played by <PERSON> on his \"Sound 42\" radio show on the same day \"Certified Lover Boy\" released. <PERSON> is said to have recorded around the same time as <PERSON> 1993.", "length": "2:55", "fileDate": 16306272, "leakDate": "", "labels": ["High Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/56f7da35bbe0931b889ebf06d84ef51f/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/56f7da35bbe0931b889ebf06d84ef51f/play\", \"key\": \"One Day (Remix)\", \"title\": \"\\u2728 One Day (Remix)\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Played by <PERSON> on his \\\"Sound 42\\\" radio show on the same day \\\"Certified Lover Boy\\\" released. <PERSON> is said to have recorded around the same time as <PERSON> 1993.\", \"date\": 16306272, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"43d47d9905c26c930a683af142b6d3ba\", \"url\": \"https://api.pillowcase.su/api/download/43d47d9905c26c930a683af142b6d3ba\", \"size\": \"2.8 MB\", \"duration\": 175.1}", "aliases": [], "size": "2.8 MB"}, {"id": "place", "name": "Place [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: <PERSON> & <PERSON>_121018_<PERSON>ti LA\nOG version of \"Place\" with no Pi'erre tag, different mixing and extra adlibs. OGF leaked on Apr 10, 2023.", "length": "1:57", "fileDate": 16521408, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/3e90c2c3859c7ac4f2b41404d8ae5e40/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/3e90c2c3859c7ac4f2b41404d8ae5e40/play\", \"key\": \"Place\", \"title\": \"Place [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Riding In Hell\"], \"description\": \"OG Filename: <PERSON> & <PERSON>_121018_<PERSON>ti LA\\nOG version of \\\"Place\\\" with no Pi'erre tag, different mixing and extra adlibs. OGF leaked on Apr 10, 2023.\", \"date\": 16521408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"362e93e365b35487596deb655621c4f3\", \"url\": \"https://api.pillowcase.su/api/download/362e93e365b35487596deb655621c4f3\", \"size\": \"1.88 MB\", \"duration\": 117.36}", "aliases": ["Riding In Hell"], "size": "1.88 MB"}, {"id": "pop-bottles", "name": "⭐ Pop Bottles [V2]", "artists": [], "producers": ["AJRuinedMyRecord"], "notes": "OG Filename: <PERSON> Bottles 8.12.18\nA throwaway from the 'Whole Lotta Red' sessions. This version has a new verse. Originally leaked in LQ on January 16, 2019, and later surfaced two months later in CDQ. OGF surfaced on October 10, 2024.", "length": "1:57", "fileDate": 15532992, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/012abce30101b3a71c1487d7acbde881/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/012abce30101b3a71c1487d7acbde881/play\", \"key\": \"Pop Bottles\", \"title\": \"\\u2b50 Pop Bottles [V2]\", \"artists\": \"(prod. AJRuinedMyRecord)\", \"description\": \"OG Filename: Pop Bottles 8.12.18\\nA throwaway from the 'Whole Lotta Red' sessions. This version has a new verse. Originally leaked in LQ on January 16, 2019, and later surfaced two months later in CDQ. OGF surfaced on October 10, 2024.\", \"date\": 15532992, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b8156924ebcdd16c36bb5c9926be2369\", \"url\": \"https://api.pillowcase.su/api/download/b8156924ebcdd16c36bb5c9926be2369\", \"size\": \"1.88 MB\", \"duration\": 117.26}", "aliases": [], "size": "1.88 MB"}, {"id": "racks", "name": "✨ Racks [Song 2]", "artists": ["<PERSON>"], "producers": ["<PERSON><PERSON>"], "notes": "Leaked on February 28, 2021.", "length": "2:47", "fileDate": 16144704, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/3c5e657c1f46475852c32e810520ab88/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/3c5e657c1f46475852c32e810520ab88/play\", \"key\": \"Racks\", \"title\": \"\\u2728 Racks [Song 2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON>)\", \"aliases\": [\"Bags\", \"Team\"], \"description\": \"Leaked on February 28, 2021.\", \"date\": 16144704, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ad7a5bf2c71ab0ffe3db3c8315ce0f86\", \"url\": \"https://api.pillowcase.su/api/download/ad7a5bf2c71ab0ffe3db3c8315ce0f86\", \"size\": \"3.46 MB\", \"duration\": 167.29}", "aliases": ["Bags", "Team"], "size": "3.46 MB"}, {"id": "reggie", "name": "<PERSON> [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Reggie 8.24.18\nHook only version of what would later become \"Fashion Killer\"", "length": "2:24", "fileDate": 16736544, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/106cc3b859f2b27ce800d6a8bb4b79ef/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/106cc3b859f2b27ce800d6a8bb4b79ef/play\", \"key\": \"<PERSON>\", \"title\": \"<PERSON> [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Fashion Killer\", \"Choppa Go\", \"Fashion Killa\"], \"description\": \"OG Filename: Reggie 8.24.18\\nHook only version of what would later become \\\"Fashion Killer\\\"\", \"date\": 16736544, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ea99779cff684ba793f5454f08da1936\", \"url\": \"https://api.pillowcase.su/api/download/ea99779cff684ba793f5454f08da1936\", \"size\": \"2.31 MB\", \"duration\": 144.14}", "aliases": ["Fashion Killer", "Choppa Go", "Fashion Killa"], "size": "2.31 MB"}, {"id": "reggie-24", "name": "⭐ Reggie [V5]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Reggie 9.9.18\nVersion of Fashion Killa with 2 carti verses and different mix. Seems like carti later went back to the <PERSON> title. Unknown if earlier or later than <PERSON><PERSON><PERSON> fashion killer", "length": "2:25", "fileDate": 15531264, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/57cdda03066e1cdab7d85f6b7cb61650/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/57cdda03066e1cdab7d85f6b7cb61650/play\", \"key\": \"<PERSON>\", \"title\": \"\\u2b50 Reggie [V5]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Fashion Killer\", \"Choppa Go\", \"Fashion Killa\"], \"description\": \"OG Filename: Reggie 9.9.18\\nVersion of Fashion Killa with 2 carti verses and different mix. Seems like carti later went back to the Reggie title. Unknown if earlier or later than <PERSON> Bibby fashion killer\", \"date\": 15531264, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5ee09721c943a0153464423039ea90ad\", \"url\": \"https://api.pillowcase.su/api/download/5ee09721c943a0153464423039ea90ad\", \"size\": \"3.11 MB\", \"duration\": 145.42}", "aliases": ["Fashion Killer", "Choppa Go", "Fashion Killa"], "size": "3.11 MB"}, {"id": "riri", "name": "RiRi* [V1]", "artists": [], "producers": ["DY Krazy", "Wheezy"], "notes": "Leaked on March 22, 2021 and has an open verse meant for <PERSON>. Formerly known as \"RAF\" but this name has been deconfirmed. Was later confirmed to be titled Means.st by @countingcaskets.", "length": "3:15", "fileDate": 16163712, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/88497b7ebc114fb6d7d1885deed67343/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/88497b7ebc114fb6d7d1885deed67343/play\", \"key\": \"RiRi*\", \"title\": \"RiRi* [V1]\", \"artists\": \"(prod. DY Krazy & Wheezy)\", \"aliases\": [\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", \"Duck Down\", \"Means\"], \"description\": \"Leaked on March 22, 2021 and has an open verse meant for <PERSON>. Formerly known as \\\"RAF\\\" but this name has been deconfirmed. Was later confirmed to be titled Means.st by @countingcaskets.\", \"date\": 16163712, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a8d7e13158c0d8c2332aff0ae51ebe39\", \"url\": \"https://api.pillowcase.su/api/download/a8d7e13158c0d8c2332aff0ae51ebe39\", \"size\": \"3.13 MB\", \"duration\": 195.77}", "aliases": ["RAF", "<PERSON><PERSON><PERSON>", "Duck Down", "Means"], "size": "3.13 MB"}, {"id": "on-top", "name": "<PERSON><PERSON> - On Top [V3]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Mexikodro", "StoopidXool"], "notes": "OG Filename: RUBI ROSE\nLeaked on May 15, 2022. Has adlibs on <PERSON><PERSON>'s part.", "length": "", "fileDate": 16525728, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/ecdac2c189f05d873f4b991e4e34ed46/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/ecdac2c189f05d873f4b991e4e34ed46/play\", \"key\": \"On Top\", \"title\": \"<PERSON><PERSON> Top [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Mexikodro & StoopidXool)\", \"description\": \"OG Filename: RUBI ROSE\\nLeaked on May 15, 2022. Has adlibs on <PERSON><PERSON>'s part.\", \"date\": 16525728, \"available\": [\"OG File\", \"rgb(0, 0, 0)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "money-over-all", "name": "RX Peso - Money Over All", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Money Over All 8.12.18\nLeaked on Febuary 1, 2020.", "length": "3:10", "fileDate": 15805152, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/d9076d1d31a3dbf8fa790ea2869f7581/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d9076d1d31a3dbf8fa790ea2869f7581/play\", \"key\": \"Money Over All\", \"title\": \"RX Peso - Money Over All\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>) \", \"aliases\": [\"All She Gotta Do Is Call\"], \"description\": \"OG Filename: Money Over All 8.12.18\\nLeaked on Febuary 1, 2020.\", \"date\": 15805152, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d9193cacdcf9cf511b62ede1083b96e3\", \"url\": \"https://api.pillowcase.su/api/download/d9193cacdcf9cf511b62ede1083b96e3\", \"size\": \"3.05 MB\", \"duration\": 190.44}", "aliases": ["All She Gotta Do Is Call"], "size": "3.05 MB"}, {"id": "skeleton", "name": "⭐ Skeleton [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Skeleton 8.9.18\nA throwaway from the 'Whole Lotta Red' sessions.\nFeatures an open verse. A finished version featuring <PERSON><PERSON> The Ruler exists.", "length": "3:03", "fileDate": 15687648, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/761bbdbfae73d9f1544588ab1fe10ac4/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/761bbdbfae73d9f1544588ab1fe10ac4/play\", \"key\": \"Skeleton\", \"title\": \"\\u2b50 Skeleton [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>) \", \"aliases\": [\"Skeletons\", \"Whole Lotta Red\", \"Off Red\"], \"description\": \"OG Filename: Skeleton 8.9.18\\nA throwaway from the 'Whole Lotta Red' sessions.\\nFeatures an open verse. A finished version featuring <PERSON><PERSON> Ruler exists.\", \"date\": 15687648, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3e4cc92e7b1687fef525f5e7ac3d4541\", \"url\": \"https://api.pillowcase.su/api/download/3e4cc92e7b1687fef525f5e7ac3d4541\", \"size\": \"3.73 MB\", \"duration\": 183.77}", "aliases": ["Skeletons", "Whole Lotta Red", "Off Red"], "size": "3.73 MB"}, {"id": "slick", "name": "Slick", "artists": ["RX Peso"], "producers": [], "notes": "OG Filename: Slick 8.9.18\nA throwaway from the 'Whole Lotta Red' sessions.", "length": "", "fileDate": 15775776, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/5916f42c574c2f8870c597aa2157d0b7/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/5916f42c574c2f8870c597aa2157d0b7/play\", \"key\": \"Slick\", \"title\": \"Slick\", \"artists\": \"(feat. <PERSON><PERSON>) (Prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Slick 8.9.18\\nA throwaway from the 'Whole Lotta Red' sessions.\", \"date\": 15775776, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "switching-lanes", "name": "✨ Switching Lanes [V1]", "artists": ["<PERSON><PERSON>erre <PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Was meant for <PERSON><PERSON> before he gave it to <PERSON><PERSON><PERSON><PERSON> for \"The Life Of Pi'erre 5\".", "length": "2:43", "fileDate": 15649632, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/1a61f427b36e3d4fdee41cd6b2c86e66/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/1a61f427b36e3d4fdee41cd6b2c86e66/play\", \"key\": \"Switching Lanes\", \"title\": \"\\u2728 Switching Lanes [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Was meant for <PERSON><PERSON> before he gave it to <PERSON><PERSON><PERSON><PERSON> for \\\"The Life Of Pi'erre 5\\\".\", \"date\": 15649632, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"bafb777f2762567028207dede825b238\", \"url\": \"https://api.pillowcase.su/api/download/bafb777f2762567028207dede825b238\", \"size\": \"2.62 MB\", \"duration\": 163.51}", "aliases": [], "size": "2.62 MB"}, {"id": "shopping-spree", "name": "✨ Shopping Spree", "artists": [], "producers": ["EarlOnTheBeat"], "notes": "OG Filename: Shopping Spree 11.22.18\nOG File Metadata: <PERSON><PERSON><PERSON> 11.22.18\nPreviewed in @countingcasket's Instagram Live. <PERSON> said it was on WLR v1.", "length": "1:39", "fileDate": 16535232, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/cf096fa04ff6c52dc7a2a31ec7bd3434/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/cf096fa04ff6c52dc7a2a31ec7bd3434/play\", \"key\": \"Shopping Spree\", \"title\": \"\\u2728 Shopping Spree\", \"artists\": \"(prod. <PERSON><PERSON>nTheBeat)\", \"aliases\": [\"Balmain Jeans\", \"Bags On Me\", \"500 Degrees\"], \"description\": \"OG Filename: Shopping Spree 11.22.18\\nOG File Metadata: Balmain Jeans 11.22.18\\nPreviewed in @countingcasket's Instagram Live. Von said it was on WLR v1.\", \"date\": 16535232, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2e6bca789d21d21257f3e137a2e42d70\", \"url\": \"https://api.pillowcase.su/api/download/2e6bca789d21d21257f3e137a2e42d70\", \"size\": \"2.38 MB\", \"duration\": 99.76}", "aliases": ["<PERSON><PERSON><PERSON>", "Bags On Me", "500 Degrees"], "size": "2.38 MB"}, {"id": "whole-lotta-red", "name": "Whole Lotta Red [V2]", "artists": ["Madmaxx602"], "producers": ["Maaly Raw"], "notes": "OG Filename: Whole Lotta Red Red Maxx\nLeaked on September 30, 2019.", "length": "1:03", "fileDate": 15698016, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/8262322c82985f168f95c6873cb14551/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/8262322c82985f168f95c6873cb14551/play\", \"key\": \"Whole Lotta Red\", \"title\": \"Whole Lotta Red [V2]\", \"artists\": \"(feat. <PERSON><PERSON>x602) (prod. <PERSON><PERSON>) \", \"aliases\": [\"Coupe\"], \"description\": \"OG Filename: Whole Lotta Red Red Maxx\\nLeaked on September 30, 2019.\", \"date\": 15698016, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3a029d7a42668049f8b2360a3747062c\", \"url\": \"https://api.pillowcase.su/api/download/3a029d7a42668049f8b2360a3747062c\", \"size\": \"1.02 MB\", \"duration\": 63.62}", "aliases": ["Coupe"], "size": "1.02 MB"}, {"id": "whole-lotta-red-33", "name": "⭐ Whole Lotta Red [V3]", "artists": ["Madmaxx602", "Smooky MarGielaa"], "producers": ["Maaly Raw"], "notes": "A throwaway from the 'Whole Lotta Red' sessions.", "length": "2:14", "fileDate": 15729120, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/499448ea7cf94119d763661cffffd9ce/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/499448ea7cf94119d763661cffffd9ce/play\", \"key\": \"Whole Lotta Red\", \"title\": \"\\u2b50 Whole Lotta Red [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>602 & <PERSON><PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON> Raw) \", \"aliases\": [\"Coupe\"], \"description\": \"A throwaway from the 'Whole Lotta Red' sessions.\", \"date\": 15729120, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7330a42bcba47643cebee06a1477e2fa\", \"url\": \"https://api.pillowcase.su/api/download/7330a42bcba47643cebee06a1477e2fa\", \"size\": \"2.15 MB\", \"duration\": 134.16}", "aliases": ["Coupe"], "size": "2.15 MB"}, {"id": "woah", "name": "Woah [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: PBC x Stankonia 8.20.18 SeanRiggins A Take\nOG File (Metadata): PBC x Stankonia 8.20.18 Sean<PERSON><PERSON>\nLeaked on November 7, 2020.", "length": "1:21", "fileDate": 16047072, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/aa03b9fd156b5797baf4933281240c66/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/aa03b9fd156b5797baf4933281240c66/play\", \"key\": \"Woah\", \"title\": \"Woah [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Keep Going\", \"My Slatt\"], \"description\": \"OG Filename: PBC x Stankonia 8.20.18 SeanRiggins A Take\\nOG File (Metadata): PBC x Stankonia 8.20.18 SeanRi\\nLeaked on November 7, 2020.\", \"date\": 16047072, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3f14d25a20410d2c516e89061c43beb3\", \"url\": \"https://api.pillowcase.su/api/download/3f14d25a20410d2c516e89061c43beb3\", \"size\": \"1.3 MB\", \"duration\": 81.24}", "aliases": ["Keep Going", "<PERSON>"], "size": "1.3 MB"}, {"id": "woah-35", "name": "Woah [V2] ", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: PBC x Stankonia 8.20.18 <PERSON><PERSON><PERSON><PERSON> B Take\nOG File (Metadata): PBC x Stankonia 8.20.18 <PERSON><PERSON><PERSON>\nLeaked on August 18, 2020 and has a different vocal take.", "length": "4:57", "fileDate": 15977088, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/0f5295b83a8af58a58e2e1ef35449f1b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/0f5295b83a8af58a58e2e1ef35449f1b/play\", \"key\": \"Woah\", \"title\": \"Woah [V2] \", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Keep Going\", \"My Slatt\"], \"description\": \"OG Filename: PBC x Stankonia 8.20.18 SeanRiggins B Take\\nOG File (Metadata): PBC x Stankonia 8.20.18 SeanRi\\nLeaked on August 18, 2020 and has a different vocal take.\", \"date\": 15977088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"264cdc28e0b55ccf7d4d2cd996d647e7\", \"url\": \"https://api.pillowcase.su/api/download/264cdc28e0b55ccf7d4d2cd996d647e7\", \"size\": \"4.77 MB\", \"duration\": 297.79}", "aliases": ["Keep Going", "<PERSON>"], "size": "4.77 MB"}, {"id": "woah-36", "name": "⭐ Woah [V3]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: PBC x Stankonia 8.20.18 Sean<PERSON>ig<PERSON> C Take\nOG File (Metadata): PBC x Stankonia 8.20.18 <PERSON><PERSON><PERSON>\nLeaked on March 22, 2021 and has a different vocal take.", "length": "", "fileDate": 16163712, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/37aa5c1c8f7737b30d7ed8464693f877/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/37aa5c1c8f7737b30d7ed8464693f877/play\", \"key\": \"Woah\", \"title\": \"\\u2b50 Woah [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Keep Going\", \"My Slatt\"], \"description\": \"OG Filename: PBC x Stankonia 8.20.18 SeanRiggins C Take\\nOG File (Metadata): PBC x Stankonia 8.20.18 SeanRi\\nLeaked on March 22, 2021 and has a different vocal take.\", \"date\": 16163712, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Keep Going", "<PERSON>"], "size": ""}, {"id": "bipolar", "name": "Ye - Bipolar [V2]", "artists": [], "producers": ["Wheezy"], "notes": "OG Filename: ye 'BIPOLARmp3 - d#m or Am - 147\nOriginal version of <PERSON><PERSON><PERSON>'s track, solo Ye confirmed by verified sources and A$AP Bari who confirmed it was given to <PERSON><PERSON> after he played it for him. Contains a rerecorded version of the \"In Abundance\" verse. According to trusted sources, it contains the same Wheezy production as the final song and was titled \"Bipolar\". File is dated to be December 2018, most likely from a reimport.", "length": "", "fileDate": 16845408, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/7d2fd111230ae3ac0088869a56c148a1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/7d2fd111230ae3ac0088869a56c148a1\", \"key\": \"Bipolar\", \"title\": \"Ye - Bipolar [V2]\", \"artists\": \"(prod. Wheezy)\", \"aliases\": [\"Go2DaMoon\"], \"description\": \"OG Filename: ye 'BIPOLARmp3 - d#m or Am - 147\\nOriginal version of <PERSON><PERSON><PERSON>'s track, solo Ye confirmed by verified sources and A$AP Bari who confirmed it was given to <PERSON><PERSON> after he played it for him. Contains a rerecorded version of the \\\"In Abundance\\\" verse. According to trusted sources, it contains the same Wheezy production as the final song and was titled \\\"Bipolar\\\". File is dated to be December 2018, most likely from a reimport.\", \"date\": 16845408, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"]}", "aliases": ["Go2DaMoon"], "size": ""}, {"id": "raf", "name": "✨ <PERSON> Jordan - RAF", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Nebu Kiniza"], "notes": "OG Filename: 01 <PERSON><PERSON> x <PERSON> RAF 10.10.18\nPreviewed by @Countingcaskets on Instagram Live and said it was not <PERSON>eb<PERSON> but <PERSON>'s song. Uses the same beat as <PERSON><PERSON><PERSON>'s \"<PERSON><PERSON>\", though <PERSON><PERSON> has some production differences. <PERSON> most likely later on gave the song to <PERSON><PERSON>, due to it's apperance as track 16 on the WLR TL from April 2019. Leaked September 7, 2022.", "length": "", "fileDate": 16625088, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/5f5941a3f28a92e486077f0a807fdba4/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/5f5941a3f28a92e486077f0a807fdba4/play\", \"key\": \"RAF\", \"title\": \"\\u2728 <PERSON>\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Hunna\"], \"description\": \"OG Filename: 01 Carti x Jordan RAF 10.10.18\\nPreviewed by @Countingcaskets on Instagram Live and said it was not Nebu but <PERSON>'s song. Uses the same beat as <PERSON><PERSON><PERSON>'s \\\"Hunna\\\", though <PERSON><PERSON> has some production differences. <PERSON> most likely later on gave the song to <PERSON><PERSON>, due to it's apperance as track 16 on the WLR TL from April 2019. Leaked September 7, 2022.\", \"date\": 16625088, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["<PERSON><PERSON>"], "size": ""}, {"id": "pissy-pamper", "name": "⭐ Young Nudy & <PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON> [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: NUDY_PIERRE_x_CARTI-_PISSY_PAMPER_03\nThe most famous <PERSON><PERSON><PERSON> leak, despite not being his own song. Was planned to release on <PERSON> Nudy and <PERSON><PERSON><PERSON><PERSON>'s collaborative album \"Sli'merre\" but was removed due to sample clearance issues.", "length": "", "fileDate": 15555456, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/73e0c76663dfea4448d6403bc8a60bc9/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/73e0c76663dfea4448d6403bc8a60bc9/play\", \"key\": \"Pissy Pamper\", \"title\": \"\\u2b50 Young Nudy & <PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON> [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Kid Cudi\", \"<PERSON> Carti\", \"They Tryna Be Cray\"], \"description\": \"OG Filename: NUDY_PIERRE_x_CARTI-_PISSY_PAMPER_03\\nThe most famous <PERSON><PERSON><PERSON> leak, despite not being his own song. Was planned to release on Young Nudy and <PERSON><PERSON><PERSON>'s collaborative album \\\"Sli'merre\\\" but was removed due to sample clearance issues.\", \"date\": 15555456, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["<PERSON>", "<PERSON>", "They Tryna Be Cray"], "size": ""}, {"id": "pissy-pamper-40", "name": "Young Nudy & Pi'er<PERSON> - <PERSON><PERSON> [V3]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: 01YoungNudy-PISSYPAMPER_CLEARX\nClean version of \"Pissy Pamper\" with an extra beat drop on <PERSON><PERSON><PERSON>'s verse. Likely the version that was going to be dropped before being declined.", "length": "", "fileDate": 16581888, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/23372c2d5b7a7addbc0dc80dddeb1440", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/23372c2d5b7a7addbc0dc80dddeb1440\", \"key\": \"Pissy Pamper\", \"title\": \"<PERSON> Nudy & <PERSON>'er<PERSON> - <PERSON><PERSON> [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Kid Cudi\", \"<PERSON> Carti\", \"They Tryna Be Cray\"], \"description\": \"OG Filename: 01YoungNudy-PISSYPAMPER_CLEARX\\nClean version of \\\"Pissy Pamper\\\" with an extra beat drop on <PERSON><PERSON><PERSON>'s verse. Likely the version that was going to be dropped before being declined.\", \"date\": 16581888, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"]}", "aliases": ["<PERSON>", "<PERSON>", "They Tryna Be Cray"], "size": ""}, {"id": "baguettes-in-the-face", "name": "Mustard - Baguettes in the Face", "artists": ["NAV", "<PERSON><PERSON><PERSON>", "A Boogie Wit da Hoodie"], "producers": ["Mustard", "<PERSON>"], "notes": "OG Filename: 04 Mustard_Baguettes in the Face (feat. <PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON> <PERSON> wit da Hoodie)_07\nOGF for \"Baguettes In The Face\".", "length": "", "fileDate": 16589664, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/01f3cfdf99f0c43585aeef1eb9cf8d78/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/01f3cfdf99f0c43585aeef1eb9cf8d78/play\", \"key\": \"Baguettes in the Face\", \"title\": \"Mustard - Baguettes in the Face\", \"artists\": \"(feat. <PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON> Boogie Wit da Hoodie) (prod. <PERSON>ard & Larry <PERSON>)\", \"description\": \"OG Filename: 04 Mustard_Baguettes in the Face (feat. <PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON> Boogie wit da Hoodie)_07\\nOGF for \\\"Baguettes In The Face\\\".\", \"date\": 16589664, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "wake-up", "name": "<PERSON> - <PERSON> Up", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON><PERSON><PERSON>"], "notes": "OG Filename: Wake Up 10.9.18\nOGF for \"Wake Up\" by <PERSON>.", "length": "", "fileDate": 16655328, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/05cc59e600e54fd0440f50631eb88204/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/05cc59e600e54fd0440f50631eb88204/play\", \"key\": \"Wake Up\", \"title\": \"<PERSON> Jordan - Wake Up\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Wake Up 10.9.18\\nOGF for \\\"Wake Up\\\" by <PERSON>.\", \"date\": 16655328, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "paramount", "name": "🥇 Paramount", "artists": [], "producers": ["DP Beats"], "notes": "Would surface 3/14/25. Long snippet played by <PERSON><PERSON>'s friend <PERSON><PERSON><PERSON><PERSON> once he realized he was double sold songs", "length": "", "fileDate": 17419104, "leakDate": "", "labels": ["High Quality", "Partial"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/d0b1e7dc1d74f24b04b590acf12fc9f0/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d0b1e7dc1d74f24b04b590acf12fc9f0/play\", \"key\": \"Paramount\", \"title\": \"\\ud83e\\udd47 Paramount\", \"artists\": \"(prod. DP Beats)\", \"description\": \"Would surface 3/14/25. Long snippet played by <PERSON><PERSON>'s friend <PERSON><PERSON><PERSON><PERSON> once he realized he was double sold songs\", \"date\": 17419104, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}, {"id": "canc-n-44", "name": "<PERSON><PERSON><PERSON> [V3]", "artists": ["<PERSON><PERSON> Coldhearted"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Later version of Can<PERSON><PERSON> featuring <PERSON><PERSON>. Said by a redditor that <PERSON><PERSON>'s verse is a seperate song unrelated to <PERSON><PERSON>'s version, but this is not confirmed.", "length": "", "fileDate": 15147648, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/video/f9817bf407cd414da6eef8ea6d0b2077/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/video/f9817bf407cd414da6eef8ea6d0b2077/play\", \"key\": \"Canc\\u00fan\", \"title\": \"Canc\\u00fan [V3]\", \"artists\": \"(feat. <PERSON><PERSON>hearted) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Money Jumpin'\"], \"description\": \"Later version of Canc\\u00fan featuring <PERSON><PERSON>hearted. Said by a redditor that <PERSON><PERSON>'s verse is a seperate song unrelated to <PERSON><PERSON>'s version, but this is not confirmed.\", \"date\": 15147648, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Money Jumpin'"], "size": ""}, {"id": "fuck-on-these-hoes", "name": "Fuck On These Hoes", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "<PERSON><PERSON><PERSON><PERSON> with a beat that resembles \"Hellcat\".", "length": "", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/230754604a4404c1c21f95a3922f6404/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/230754604a4404c1c21f95a3922f6404/play\", \"key\": \"Fuck On These Hoes\", \"title\": \"Fuck On These Hoes\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Throwaway with a beat that resembles \\\"Hellcat\\\".\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "geeked", "name": "🥇 Geeked [V2]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "Previewed in @nineninesixteensixteen's Instagram Live. Uses the same beat and similar vocals as No Smoke Freestyle.", "length": "", "fileDate": 15463008, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/8ff644dcb8a737faefe02271f7e8b203/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/8ff644dcb8a737faefe02271f7e8b203/play\", \"key\": \"Geeked\", \"title\": \"\\ud83e\\udd47 Geeked [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Previewed in @nineninesixteensixteen's Instagram Live. Uses the same beat and similar vocals as No Smoke Freestyle.\", \"date\": 15463008, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "let-s-get-it", "name": "🏆 Let's Get It", "artists": [], "producers": ["DatKidChrisOnDaTrak"], "notes": "A community grail. Said to be recorded on Oct 31, 2018. Likely Lost.", "length": "", "fileDate": 15778368, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/eeb3e9909b88f9766ecbd31c72ee00ae/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/eeb3e9909b88f9766ecbd31c72ee00ae/play\", \"key\": \"Let's Get It\", \"title\": \"\\ud83c\\udfc6 Let's Get It\", \"artists\": \"(prod. DatKidChrisOnDaTrak)\", \"aliases\": [\"<PERSON><PERSON>\"], \"description\": \"A community grail. Said to be recorded on Oct 31, 2018. Likely Lost.\", \"date\": 15778368, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["<PERSON><PERSON>"], "size": ""}, {"id": "proud-of-you", "name": "🥇 Proud of You [V1]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "notes": "An OG version featuring different vocals. Reportedly lost.", "length": "", "fileDate": 15448320, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/06345e3ca59fe48b49bf31aee47a1975/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/06345e3ca59fe48b49bf31aee47a1975/play\", \"key\": \"Proud of You\", \"title\": \"\\ud83e\\udd47 Proud of You [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Pac\", \"Pop\"], \"description\": \"An OG version featuring different vocals. Reportedly lost.\", \"date\": 15448320, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Pac", "Pop"], "size": ""}, {"id": "no-smoke-freestyle", "name": "🥇 No Smoke Freestyle", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "A freestyle preformed on <PERSON><PERSON>'s IG live.", "length": "2:57", "fileDate": 15526080, "leakDate": "", "labels": ["Low Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/e67564ec10ccca5f64cc7ad0f65c7667/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e67564ec10ccca5f64cc7ad0f65c7667/play\", \"key\": \"No Smoke Freestyle\", \"title\": \"\\ud83e\\udd47 No Smoke Freestyle\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>) \", \"aliases\": [\"Geeked\"], \"description\": \"A freestyle preformed on <PERSON><PERSON>'s IG live.\", \"date\": 15526080, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"af2f069484862ee26188372d16f3acf9\", \"url\": \"https://api.pillowcase.su/api/download/af2f069484862ee26188372d16f3acf9\", \"size\": \"2.84 MB\", \"duration\": 177.34}", "aliases": ["Geeked"], "size": "2.84 MB"}, {"id": "party-with-her", "name": "🏆 Party With Her", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "A voice memo of <PERSON><PERSON> over the beat later used for <PERSON>'s \"Loot\". The voice memo has been recorded on Jan 2, 2019", "length": "", "fileDate": 16904160, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/a09aa94211e899d31578f04bd8f5a538/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a09aa94211e899d31578f04bd8f5a538/play\", \"key\": \"Party With Her\", \"title\": \"\\ud83c\\udfc6 Party With Her\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Loot\"], \"description\": \"A voice memo of <PERSON><PERSON> over the beat later used for <PERSON>'s \\\"Loot\\\". The voice memo has been recorded on Jan 2, 2019\", \"date\": 16904160, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Loot"], "size": ""}, {"id": "riri-51", "name": "🥇 RiRi [V2]", "artists": ["<PERSON>"], "producers": ["DY Krazy", "Wheezy"], "notes": "Later version with <PERSON>.", "length": "", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/b2bc1bcb9dd60c0555bd05fad087b8c4/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/b2bc1bcb9dd60c0555bd05fad087b8c4/play\", \"key\": \"RiRi\", \"title\": \"\\ud83e\\udd47 RiRi [V2]\", \"artists\": \"(feat. <PERSON>) (prod. DY Krazy & Wheezy)\", \"aliases\": [\"RAF\", \"<PERSON><PERSON><PERSON>\", \"Duck Down\"], \"description\": \"Later version with <PERSON>.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["RAF", "<PERSON><PERSON><PERSON>", "Duck Down"], "size": ""}, {"id": "reggie-52", "name": "<PERSON> [V4]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "A demo for <PERSON> containing an alternative verse. Unknown if dated before <PERSON> Bibby one.", "length": "", "fileDate": 16725312, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/0d0ddd56198c3b76f538040cbc7df141/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/0d0ddd56198c3b76f538040cbc7df141/play\", \"key\": \"<PERSON>\", \"title\": \"<PERSON> [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Fashion Killer\", \"Choppa Go\"], \"description\": \"A demo for <PERSON> containing an alternative verse. Unknown if dated before Lil Bibby one.\", \"date\": 16725312, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Fashion Killer", "Choppa Go"], "size": ""}, {"id": "skeleton-53", "name": "Skeleton [V2] ", "artists": ["<PERSON><PERSON> The Ruler"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Finished version of \"Skeleton\" with <PERSON><PERSON> The Ruler featuring over the open verse.", "length": "", "fileDate": 17445024, "leakDate": "", "labels": ["High Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/4b49e631fbb46ef954b831f38a513a87", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/4b49e631fbb46ef954b831f38a513a87\", \"key\": \"Skeleton\", \"title\": \"Skeleton [V2] \", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Finished version of \\\"Skeleton\\\" with <PERSON><PERSON>r featuring over the open verse.\", \"date\": 17445024, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}, {"id": "skeleton-54", "name": "Skeleton [V2] ", "artists": ["<PERSON><PERSON> The Ruler"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Finished version of \"Skeleton\" with <PERSON><PERSON> The Ruler featuring over the open verse.", "length": "", "fileDate": 17445024, "leakDate": "", "labels": ["High Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/9176c3997aa1f77093b5b0c37fa00054", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/9176c3997aa1f77093b5b0c37fa00054\", \"key\": \"Skeleton\", \"title\": \"Skeleton [V2] \", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Finished version of \\\"Skeleton\\\" with <PERSON><PERSON>r featuring over the open verse.\", \"date\": 17445024, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}, {"id": "walk-with-bags", "name": "Walk With Bags", "artists": [], "producers": ["Wheezy"], "notes": "<PERSON><PERSON><PERSON> mentioned the song on twitter \"<PERSON> <PERSON> <PERSON><PERSON> going crazy\" the same day he previewed the snippet.", "length": "", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/6fdd2feeac0dbf9d64977434ae1f179c/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/6fdd2feeac0dbf9d64977434ae1f179c/play\", \"key\": \"Walk With Bags\", \"title\": \"Walk With Bags\", \"artists\": \"(prod. <PERSON>hee<PERSON>)\", \"description\": \"<PERSON><PERSON><PERSON> mentioned the song on twitter \\\"<PERSON> and <PERSON><PERSON> going crazy\\\" the same day he previewed the snippet.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "whole-lotta-red-56", "name": "Whole Lotta Red [V1]", "artists": ["Madmaxx602"], "producers": ["Maaly Raw"], "notes": "A version featuring slightly different chorus and different mix.", "length": "", "fileDate": 17005248, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/6cf5f10c4b044f37b56027e619176ab4/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/6cf5f10c4b044f37b56027e619176ab4/play\", \"key\": \"Whole Lotta Red\", \"title\": \"Whole Lotta Red [V1]\", \"artists\": \"(feat. <PERSON><PERSON>x602) (prod. <PERSON><PERSON>)\", \"description\": \"A version featuring slightly different chorus and different mix.\", \"date\": 17005248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "whole-lotta-red-57", "name": "Whole Lotta Red [V1]", "artists": ["Madmaxx602"], "producers": ["Maaly Raw"], "notes": "A version featuring slightly different chorus and different mix.", "length": "", "fileDate": 17005248, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/1712e9e7d607aaf7c77e45f4b540ffae/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/1712e9e7d607aaf7c77e45f4b540ffae/play\", \"key\": \"Whole Lotta Red\", \"title\": \"Whole Lotta Red [V1]\", \"artists\": \"(feat. <PERSON><PERSON>x602) (prod. <PERSON><PERSON>)\", \"description\": \"A version featuring slightly different chorus and different mix.\", \"date\": 17005248, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "", "name": "???", "artists": [], "producers": ["Forza"], "notes": "Beat previewed by <PERSON><PERSON> when <PERSON><PERSON> was still working with him. Unknown if ever recorded on it.", "length": "", "fileDate": 15147648, "leakDate": "", "labels": ["Low Quality", "Beat Only"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/video/7adbc38da65c2dc322efa6ac713053e4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/video/7adbc38da65c2dc322efa6ac713053e4\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"Beat previewed by <PERSON><PERSON> when <PERSON><PERSON> was still working with him. Unknown if ever recorded on it.\", \"date\": 15147648, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "-59", "name": "???", "artists": [], "producers": ["<PERSON>"], "notes": "It is unknown if this is a full song, but there is a beat in the background of the snippet, so it potentially could be.", "length": "", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/378eb1cf69279c2bdb78d1fb5fbc36c7/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/378eb1cf69279c2bdb78d1fb5fbc36c7/play\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"<PERSON> Tag Song\"], \"description\": \"It is unknown if this is a full song, but there is a beat in the background of the snippet, so it potentially could be.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["<PERSON>"], "size": ""}, {"id": "2-6-boy", "name": "🏆 2.6 Boy", "artists": [], "producers": ["<PERSON>"], "notes": "\"2.6 Boy\" is a community grail and throwaway from \"Whole Lotta Red\" that was considered to feature on the album, but was never on any tracklist. It was previewed in a currently unreleased Whole Lotta Red 'documentary'. A 2:30 minute LQ voice memo leaked on May 20, 2023, alongside a higher quality voice memo of the song leaking in 2024, though that version is shorter and cuts off at the 2:22 mark. Many believe this song was made around the same time as <PERSON> (Before June 2019), but the original beat is dated July 6, 2019, meaning the song was recorded much later.", "length": "", "fileDate": 16845408, "leakDate": "", "labels": ["Low Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/cf44c6f3a87801868fcabfc0fc9909a8/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/cf44c6f3a87801868fcabfc0fc9909a8/play\", \"key\": \"2.6 Boy\", \"title\": \"\\ud83c\\udfc6 2.6 Boy\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Bitch Boy\"], \"description\": \"\\\"2.6 Boy\\\" is a community grail and throwaway from \\\"Whole Lotta Red\\\" that was considered to feature on the album, but was never on any tracklist. It was previewed in a currently unreleased Whole Lotta Red 'documentary'. A 2:30 minute LQ voice memo leaked on May 20, 2023, alongside a higher quality voice memo of the song leaking in 2024, though that version is shorter and cuts off at the 2:22 mark. Many believe this song was made around the same time as <PERSON> (Before June 2019), but the original beat is dated July 6, 2019, meaning the song was recorded much later.\", \"date\": 16845408, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Bitch Boy"], "size": ""}, {"id": "2-6-boy-61", "name": "🏆 2.6 Boy", "artists": [], "producers": ["<PERSON>"], "notes": "\"2.6 Boy\" is a community grail and throwaway from \"Whole Lotta Red\" that was considered to feature on the album, but was never on any tracklist. It was previewed in a currently unreleased Whole Lotta Red 'documentary'. A 2:30 minute LQ voice memo leaked on May 20, 2023, alongside a higher quality voice memo of the song leaking in 2024, though that version is shorter and cuts off at the 2:22 mark. Many believe this song was made around the same time as <PERSON> (Before June 2019), but the original beat is dated July 6, 2019, meaning the song was recorded much later.", "length": "", "fileDate": 16845408, "leakDate": "", "labels": ["Low Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/f108b346caa0bd8fa0dde5d962fa7d22/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/f108b346caa0bd8fa0dde5d962fa7d22/play\", \"key\": \"2.6 Boy\", \"title\": \"\\ud83c\\udfc6 2.6 Boy\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Bitch Boy\"], \"description\": \"\\\"2.6 Boy\\\" is a community grail and throwaway from \\\"Whole Lotta Red\\\" that was considered to feature on the album, but was never on any tracklist. It was previewed in a currently unreleased Whole Lotta Red 'documentary'. A 2:30 minute LQ voice memo leaked on May 20, 2023, alongside a higher quality voice memo of the song leaking in 2024, though that version is shorter and cuts off at the 2:22 mark. Many believe this song was made around the same time as <PERSON> (Before June 2019), but the original beat is dated July 6, 2019, meaning the song was recorded much later.\", \"date\": 16845408, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Bitch Boy"], "size": ""}, {"id": "celine", "name": "Celine", "artists": ["Capo DTE"], "producers": ["<PERSON><PERSON><PERSON><PERSON>"], "notes": "OG Filename: 01 celine\nLeaked on December 6, 2022. From May-June 2019.", "length": "4:26", "fileDate": 16702848, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://pillowcase.su/f/23e1e9ce4f1021185061a2c848b8cafa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/23e1e9ce4f1021185061a2c848b8cafa\", \"key\": \"Celine\", \"title\": \"<PERSON><PERSON>\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>) \", \"aliases\": [\"Celine Baby\"], \"description\": \"OG Filename: 01 celine\\nLeaked on December 6, 2022. From May-June 2019.\", \"date\": 16702848, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"23a56d99c8301e97e308f236b5cadcd0\", \"url\": \"https://api.pillowcase.su/api/download/23a56d99c8301e97e308f236b5cadcd0\", \"size\": \"4.36 MB\", \"duration\": 266.74}", "aliases": ["<PERSON><PERSON>"], "size": "4.36 MB"}, {"id": "designer-shoes", "name": "✨ Designer Shoes [V1]", "artists": [], "producers": ["Chinatown"], "notes": "OG Filename: carti china bring a friend\nShares the beat with the 2017 throwaway \"Brand New\". This version shares the same title as the beat, as bring a friend is not this versions official name. Leaked on Nov 14, 2023. Seen on a V1 tracklist from April.", "length": "", "fileDate": 16999200, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/0903deef20736709e74f09a006c1782c/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/0903deef20736709e74f09a006c1782c/play\", \"key\": \"Designer Shoes\", \"title\": \"\\u2728 Designer Shoes [V1]\", \"artists\": \"(prod. Chinatown)\", \"aliases\": [\"Bring a Friend\"], \"description\": \"OG Filename: carti china bring a friend\\nShares the beat with the 2017 throwaway \\\"Brand New\\\". This version shares the same title as the beat, as bring a friend is not this versions official name. Leaked on Nov 14, 2023. Seen on a V1 tracklist from April.\", \"date\": 16999200, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Bring a Friend"], "size": ""}, {"id": "designer-shoes-64", "name": "⭐ Designer Shoes [V2]", "artists": [], "producers": ["Chinatown"], "notes": "OG Filename: carti china designer shoes\nHas a different verse compared to the original and an instrumental break at the end. Leaked on May 31, 2022. Was seen on a V1 tracklist from April.", "length": "", "fileDate": 16539552, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/09242ab7bb7b5fd799c93d16ef17e7e4/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/09242ab7bb7b5fd799c93d16ef17e7e4/play\", \"key\": \"Designer Shoes\", \"title\": \"\\u2b50 Designer Shoes [V2]\", \"artists\": \"(prod. Chinatown)\", \"aliases\": [\"Pop My Shit\", \"Brand New Bih V2\", \"Bring A Friend\"], \"description\": \"OG Filename: carti china designer shoes\\nHas a different verse compared to the original and an instrumental break at the end. Leaked on May 31, 2022. Was seen on a V1 tracklist from April.\", \"date\": 16539552, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Pop My Shit", "Brand New Bih V2", "Bring A Friend"], "size": ""}, {"id": "don-t-worry", "name": "✨ Don't Worry", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON><PERSON><PERSON>", "Maaly Raw"], "notes": "A throwaway from the 'Whole Lotta Red' sessions.", "length": "", "fileDate": 15642720, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/6c105f54fc2d4fb8695ed149e3210944/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/6c105f54fc2d4fb8695ed149e3210944/play\", \"key\": \"Don't Worry\", \"title\": \"\\u2728 Don't Worry\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. B<PERSON>n Amir & Maaly Raw)\", \"description\": \"A throwaway from the 'Whole Lotta Red' sessions.\", \"date\": 15642720, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "free-pde", "name": "⭐ Free PDE [V2]", "artists": ["Offset"], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "OG Filename: Free PDE 2.12.19\nA throwaway from the 'Whole Lotta Red' sessions. Has a different line on the hook, \"<PERSON><PERSON> die lit on that piru\". Seen on a V1 tracklist from April.", "length": "", "fileDate": 15793920, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/530164b11d5ccd9875a51e56ba8cd535/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/530164b11d5ccd9875a51e56ba8cd535/play\", \"key\": \"Free PDE\", \"title\": \"\\u2b50 Free PDE [V2]\", \"artists\": \"(feat. Offset) (prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON>crest\", \"<PERSON>ru\"], \"description\": \"OG Filename: Free PDE 2.12.19\\nA throwaway from the 'Whole Lotta Red' sessions. Has a different line on the hook, \\\"Ima die lit on that piru\\\". Seen on a V1 tracklist from April.\", \"date\": 15793920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Bouldercrest", "<PERSON><PERSON>"], "size": ""}, {"id": "friends", "name": "⭐ Friends", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: FRIENDS\n\"Friends\" is a unreleased song which was originally on \"Whole Lotta Red\" and was meant to be released as the paperwork was filed for it's release. Additionally, it previewed in a currently unreleased Whole Lotta Red 'documentary'. Privately purchased & leaked for free by Hells on March 22, 2023. Recorded before June 2019, and present on tracklists with songs from WLR v1 era. Supposed \"OG File\" leaked d on October 9th, 2024 as a bonus for a MUSIC era blind GB.", "length": "", "fileDate": 16794432, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/c6c419c1ba0b66c360c98f035830866a/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/c6c419c1ba0b66c360c98f035830866a/play\", \"key\": \"Friends\", \"title\": \"\\u2b50 Friends\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON><PERSON>\", \"Came In That Bih\"], \"description\": \"OG Filename: FRIENDS\\n\\\"Friends\\\" is a unreleased song which was originally on \\\"Whole Lotta Red\\\" and was meant to be released as the paperwork was filed for it's release. Additionally, it previewed in a currently unreleased Whole Lotta Red 'documentary'. Privately purchased & leaked for free by Hells on March 22, 2023. Recorded before June 2019, and present on tracklists with songs from WLR v1 era. Supposed \\\"OG File\\\" leaked d on October 9th, 2024 as a bonus for a MUSIC era blind GB.\", \"date\": 16794432, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Friendz", "Came In That Bih"], "size": ""}, {"id": "not-real-68", "name": "✨ Not Real [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Reworked version of the song with added a new verse over the open. Said to be still titled Not Real in 2019. Song was seen on a V2 tracklist, however, this song was recorded during the V1 'transition' era. A release ready mix was set to release on May 31, 2019 as a single. Has worse vocal mixing on the second verse. But sounds better than V3.", "length": "", "fileDate": 17165952, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/0237e47f58126c057a64d74c8f5bf2a0/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/0237e47f58126c057a64d74c8f5bf2a0/play\", \"key\": \"Not Real\", \"title\": \"\\u2728 Not Real [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON>\", \"No Stylist\", \"molly.jpeg\", \"Diamonds\"], \"description\": \"Reworked version of the song with added a new verse over the open. Said to be still titled Not Real in 2019. Song was seen on a V2 tracklist, however, this song was recorded during the V1 'transition' era. A release ready mix was set to release on May 31, 2019 as a single. Has worse vocal mixing on the second verse. But sounds better than V3.\", \"date\": 17165952, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["<PERSON>", "No Stylist", "molly.jpeg", "Diamonds"], "size": ""}, {"id": "not-real-69", "name": "⭐ Not Real [V3] ", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Not Real 5.23.19 \nThis version is the release ready mix of Not Real. Though it does fix some of version 2's issues, it isn't really much different. Leaked by a random during the reopening of the new Carti Tracker Discord server. The mixing is rough so the quality is affected from the distortion. The beat is higher quality than the other two versions tho. This version was intended to be released on May 31, 2019.", "length": "", "fileDate": 17418240, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/a275bc7ca13b8748ea5c3805596dc5d1/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a275bc7ca13b8748ea5c3805596dc5d1/play\", \"key\": \"Not Real\", \"title\": \"\\u2b50 Not Real [V3] \", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON>\", \"No Stylist\", \"molly.jpg\", \"Diamonds\", \"molly.jpeg\"], \"description\": \"OG Filename: Not Real 5.23.19 \\nThis version is the release ready mix of Not Real. Though it does fix some of version 2's issues, it isn't really much different. Leaked by a random during the reopening of the new Carti Tracker Discord server. The mixing is rough so the quality is affected from the distortion. The beat is higher quality than the other two versions tho. This version was intended to be released on May 31, 2019.\", \"date\": 17418240, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["<PERSON>", "No Stylist", "molly.jpg", "Diamonds", "molly.jpeg"], "size": ""}, {"id": "proud-of-you-70", "name": "Proud of You [V2]", "artists": [], "producers": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "notes": "OG Filename: PBC - Proud of You\nInitially leaked with an Oogie tag and an artificially extended open verse.\nOGF leaked by wokeupdreadful in OG File Hub on Oct 8, 2022. File can be seen dated as 2019-01-01 when opened in a program like mp3tag.", "length": "", "fileDate": 16534368, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/87f5bcdcf75d0fd4052b49fd2bb3f889/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/87f5bcdcf75d0fd4052b49fd2bb3f889/play\", \"key\": \"Proud of You\", \"title\": \"Proud of You [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Pop\", \"Pac\", \"<PERSON><PERSON> is Proud of You\"], \"description\": \"OG Filename: PBC - Proud of You\\nInitially leaked with an Oogie tag and an artificially extended open verse.\\nOGF leaked by wokeupdreadful in OG File Hub on Oct 8, 2022. File can be seen dated as 2019-01-01 when opened in a program like mp3tag.\", \"date\": 16534368, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Pop", "Pac", "<PERSON><PERSON> is Proud of You"], "size": ""}, {"id": "pvris", "name": "⭐️ Pvris", "artists": [], "producers": ["EarlOnTheBeat", "MitchGoneMad"], "notes": "OG Filename: PBC X 02.12.19 x A Room x AJ 1\nPreviewed by @twoninehundred on December 8, 2020. Leaked by @countingcaskets on April 28, 2022. Song was originally believed to be produced by AJRuinedMyRecord but was disproven by MitchGoneMad in an Instagram comment. According to <PERSON> (Carti Associate), <PERSON><PERSON> isn't saying \"Movie Time, Let's Get The Mac\", but \"<PERSON>, Let's Get The Money\" Seen on a V1 tracklist from April. Later put on a Deluxe tracklist.", "length": "", "fileDate": 16511040, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/64b9e2783d545eb7f3233b799f165654/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/64b9e2783d545eb7f3233b799f165654/play\", \"key\": \"Pvris\", \"title\": \"\\u2b50\\ufe0f Pvris\", \"artists\": \"(prod. EarlOnTheBeat & MitchGoneMad)\", \"aliases\": [\"<PERSON>\", \"Movie Time\", \"Let's Get The Mac\", \"Places\"], \"description\": \"OG Filename: PBC X 02.12.19 x A Room x AJ 1\\nPreviewed by @twoninehundred on December 8, 2020. Leaked by @countingcaskets on April 28, 2022. Song was originally believed to be produced by AJRuinedMyRecord but was disproven by MitchGoneMad in an Instagram comment. According to <PERSON> (Carti Associate), <PERSON><PERSON> isn't saying \\\"Movie Time, Let's Get The Mac\\\", but \\\"<PERSON>, Let's Get The Money\\\" Seen on a V1 tracklist from April. Later put on a Deluxe tracklist.\", \"date\": 16511040, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["<PERSON>", "Movie Time", "Let's Get The Mac", "Places"], "size": ""}, {"id": "red-ice", "name": "✨ Red Ice", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: carti 3 means st\nRecorded in a session with Rockstar Tracks, Not Real [V2], Xan, and VVV. Considered to be the first WLR V2 session.", "length": "", "fileDate": 16824672, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/b07e9973af183a9ddc79f56450a00c31/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/b07e9973af183a9ddc79f56450a00c31/play\", \"key\": \"Red Ice\", \"title\": \"\\u2728 Red Ice\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: carti 3 means st\\nRecorded in a session with Rockstar Tracks, Not Real [V2], Xan, and VVV. Considered to be the first WLR V2 session.\", \"date\": 16824672, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}, {"id": "rockstar-tracks", "name": "✨ Rockstar Tracks", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: carti 5 means st\nRecorded in a session with Red Ice, Not Real [V2], Xan, and VVV. Considered to be the first WLR V2 Session.", "length": "", "fileDate": 16824672, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/e25417d302013aedeae9d10879a9d827/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/e25417d302013aedeae9d10879a9d827/play\", \"key\": \"Rockstar Tracks\", \"title\": \"\\u2728 Rockstar Tracks\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"I'm A Rockstar\"], \"description\": \"OG Filename: carti 5 means st\\nRecorded in a session with Red Ice, Not Real [V2], <PERSON>an, and VVV. Considered to be the first WLR V2 Session.\", \"date\": 16824672, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": ["I'm A Rockstar"], "size": ""}, {"id": "v-westwood", "name": "⭐ <PERSON><PERSON> [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "A throwaway from the 'Whole Lotta Red' sessions. Seen on a V1 tracklist from April", "length": "", "fileDate": 15619392, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/8bb085ebaaa3fc62dc682ca23bc9ea37/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/8bb085ebaaa3fc62dc682ca23bc9ea37/play\", \"key\": \"<PERSON><PERSON>\", \"title\": \"\\u2b50 <PERSON><PERSON> [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\"], \"description\": \"A throwaway from the 'Whole Lotta Red' sessions. Seen on a V1 tracklist from April\", \"date\": 15619392, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "size": ""}, {"id": "v-westwood-75", "name": "✨ <PERSON><PERSON> [V3]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: 01 V Westwood 4.4.19\nAlternate mix of <PERSON><PERSON><PERSON> dated April 4th, 2019. It is unknown whether it is earlier or later than the second version. <PERSON> later used the beat for his song \"Peephole\".", "length": "3:53", "fileDate": 16660512, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/3501d58a62ee26444e3c2ebe7678b794/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/3501d58a62ee26444e3c2ebe7678b794/play\", \"key\": \"<PERSON><PERSON>\", \"title\": \"\\u2728 <PERSON><PERSON> [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\"], \"description\": \"OG Filename: 01 V Westwood 4.4.19\\nAlternate mix of <PERSON><PERSON><PERSON> dated April 4th, 2019. It is unknown whether it is earlier or later than the second version. <PERSON> later used the beat for his song \\\"Peephole\\\".\", \"date\": 16660512, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"01dcc9687a7d2fb4a176e61d76c00272\", \"url\": \"https://api.pillowcase.su/api/download/01dcc9687a7d2fb4a176e61d76c00272\", \"size\": \"3.74 MB\", \"duration\": 233.44}", "aliases": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "size": "3.74 MB"}, {"id": "vvv", "name": "✨ VVV", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: carti means st 2\nRecorded in a session with Red Ice, Not Real [V2], Xan, and Rockstar Tracks. Considered to be the first WLR V2 Session. Leaked by <PERSON>w.", "length": "3:01", "fileDate": 16892928, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/8f95b025b17e62afeed091cd466d4669/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/8f95b025b17e62afeed091cd466d4669/play\", \"key\": \"VVV\", \"title\": \"\\u2728 VVV\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Triple V\"], \"description\": \"OG Filename: carti means st 2\\nRecorded in a session with Red Ice, Not Real [V2], Xan, and Rockstar Tracks. Considered to be the first WLR V2 Session. Leaked by Cow.\", \"date\": 16892928, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"3c224a3639ca44c740552ad1b5cb3092\", \"url\": \"https://api.pillowcase.su/api/download/3c224a3639ca44c740552ad1b5cb3092\", \"size\": \"2.91 MB\", \"duration\": 181.93}", "aliases": ["Triple V"], "size": "2.91 MB"}, {"id": "worried-bout-shit", "name": "Worried <PERSON> [V1]", "artists": [], "producers": ["XOnDaBeat"], "notes": "OG Filename: <PERSON><PERSON> bout Sht\nPreviewed in @countingcasket's Instagram Live. The open verse is for <PERSON> Herbo.", "length": "3:03", "fileDate": 16539552, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/f0db6e47644c7134581a1e25bd4b97c0/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/f0db6e47644c7134581a1e25bd4b97c0/play\", \"key\": \"Worried Bout Shit\", \"title\": \"Worried Bout Shit [V1]\", \"artists\": \"(prod. XOnDaBeat)\", \"aliases\": [\"Stunna Shit\"], \"description\": \"OG Filename: <PERSON><PERSON> bout Sht\\nPreviewed in @countingcasket's Instagram Live. The open verse is for G Herbo.\", \"date\": 16539552, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a91a1722dd74da18e41ee19c6beb77e4\", \"url\": \"https://api.pillowcase.su/api/download/a91a1722dd74da18e41ee19c6beb77e4\", \"size\": \"2.93 MB\", \"duration\": 183.13}", "aliases": ["<PERSON><PERSON><PERSON>"], "size": "2.93 MB"}, {"id": "xan", "name": "✨ Xan", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: carti means 1\nRecorded in a session with Red Ice, Not Real [V2], Rockstar Tracks, and VVV. Considered to be the first WLR V2 Song", "length": "3:23", "fileDate": 16832448, "leakDate": "", "labels": ["High Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/4bfc6d24593795625b25d2df10c90165/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/4bfc6d24593795625b25d2df10c90165/play\", \"key\": \"Xan\", \"title\": \"\\u2728 Xan\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Popped A Xan\"], \"description\": \"OG Filename: carti means 1\\nRecorded in a session with Red Ice, Not Real [V2], Rockstar Tracks, and VVV. Considered to be the first WLR V2 Song\", \"date\": 16832448, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"05dc2f3c186dd48d8d70c7597970a5e2\", \"url\": \"https://api.pillowcase.su/api/download/05dc2f3c186dd48d8d70c7597970a5e2\", \"size\": \"3.26 MB\", \"duration\": 203.88}", "aliases": ["Popped <PERSON>"], "size": "3.26 MB"}, {"id": "pain-1993", "name": "Pain 1993 [V1] ", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Pain 1993 Noel Ref 1 \nOriginal version of Pain 1993 with <PERSON><PERSON> first verse and chorus. Rest of the song is an open for <PERSON><PERSON>.", "length": "", "fileDate": 16733952, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/a97bb6b47f7af2c2a7f541eec697dd88", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a97bb6b47f7af2c2a7f541eec697dd88\", \"key\": \"Pain 1993\", \"title\": \"Pain 1993 [V1] \", \"artists\": \"(ref. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Pain 1993 Noel Ref 1 \\nOriginal version of Pain 1993 with <PERSON><PERSON> first verse and chorus. Rest of the song is an open for <PERSON><PERSON>.\", \"date\": 16733952, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "lyft", "name": "Diego Money - LYFT ", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "Remix of <PERSON><PERSON><PERSON>'s \"i promise u\"", "length": "", "fileDate": 15665184, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://www.youtube.com/watch?v=GTp0um8d9ao&ab_channel=DiegoMoney", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.youtube.com/watch?v=GTp0um8d9ao&ab_channel=DiegoMoney\", \"key\": \"LYFT\", \"title\": \"Diego Money - LYFT \", \"artists\": \"(prod. 16yrold) \", \"aliases\": [\"i got ur back slatt\", \"i promise u!\", \"* i promise you!\", \"Red On Red\"], \"description\": \"Remix of <PERSON><PERSON><PERSON>'s \\\"i promise u\\\"\", \"date\": 15665184, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["i got ur back slatt", "i promise u!", "* i promise you!", "Red On Red"], "size": ""}, {"id": "percs", "name": "<PERSON><PERSON><PERSON> ", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Remix of \"Not Real\" from the Whole Lotta Red sessions.", "length": "", "fileDate": 15645312, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://soundcloud.com/tafkaine/percmf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://soundcloud.com/tafkaine/percmf\", \"key\": \"Percs\", \"title\": \"<PERSON><PERSON><PERSON> \", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>) \", \"aliases\": [\"Not Real\", \"<PERSON>\", \"No Stylist\"], \"description\": \"Remix of \\\"Not Real\\\" from the Whole Lotta Red sessions.\", \"date\": 15645312, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Not Real", "<PERSON>", "No Stylist"], "size": ""}, {"id": "dats-lite", "name": "<PERSON> $horty - Dats Lite ", "artists": [], "producers": [], "notes": "Remix of the beat switch on <PERSON>ud of You. This version is based on the Instagram snippet.", "length": "", "fileDate": 15682464, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://soundcloud.com/1lilshorty/dats-lite", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://soundcloud.com/1lilshorty/dats-lite\", \"key\": \"Dats Lite\", \"title\": \"<PERSON> <PERSON>horty - Dats Lite \", \"artists\": \"(reprod. elias) \", \"aliases\": [\"Proud of You\", \"Pop\", \"<PERSON>ti so Proud!\"], \"description\": \"Remix of the beat switch on Proud of You. This version is based on the Instagram snippet.\", \"date\": 15682464, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Proud of You", "Pop", "<PERSON><PERSON> so <PERSON>ud!"], "size": ""}, {"id": "pop-our-pills", "name": "🏆 Pop Our Pills", "artists": [], "producers": ["Juberlee", "<PERSON><PERSON><PERSON>"], "notes": "Previewed by <PERSON><PERSON> on his spam IG. \"Glock 19\" by <PERSON> uses a very similar beat (prod <PERSON><PERSON><PERSON>). Recorded in 2019. Contrary to popular belief, this is not lost. Held by <PERSON>s", "length": "0:09", "fileDate": 15778368, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/78e64acc01a2785d771cb9841d60a1d9/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/78e64acc01a2785d771cb9841d60a1d9/play\", \"key\": \"Pop Our Pills\", \"title\": \"\\ud83c\\udfc6 Pop Our Pills\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Pop Up\", \"Pop Out\", \"Popstar\"], \"description\": \"Previewed by <PERSON><PERSON> on his spam IG. \\\"Glock 19\\\" by <PERSON> uses a very similar beat (prod <PERSON><PERSON><PERSON>). Recorded in 2019. Contrary to popular belief, this is not lost. Held by Hells\", \"date\": 15778368, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"1a67eb9f77606ca46b05d042101060c7\", \"url\": \"https://api.pillowcase.su/api/download/1a67eb9f77606ca46b05d042101060c7\", \"size\": \"154 kB\", \"duration\": 9.6}", "aliases": ["Pop Up", "Pop Out", "Popstar"], "size": "154 kB"}, {"id": "tryna-get-down", "name": "🏆🥇 <PERSON><PERSON> Get Down", "artists": [], "producers": ["<PERSON>"], "notes": "Previewed by @metalgearsolid in an Instagram Story. <PERSON> confirmed the song was releasing on Whole Lotta Red weeks before the album's release. The song was scrapped from the project right before its release, and producer <PERSON> still teases it on Twitter. It is often confused with the name \"U Kan Get It,\" but that's an entirely different unheard song. One of the biggest grails in the Carti community. <PERSON> leaked the beat in the Richie Grail zip, confirming the name as \"Tryna Get Down.\" The voice memo is dated April 13, 2019, so it's most likely from early April, though the original beat was made on February 9, 2019, meaning it could've been made anytime between February 9 and April 13. Technically part of the WLR V1 era due to being recorded before \"Designer Shoes.\"", "length": "0:15", "fileDate": 15928704, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/cdef4db9525543efbb5bcefd315dca5b/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/cdef4db9525543efbb5bcefd315dca5b/play\", \"key\": \"Tryna Get Down\", \"title\": \"\\ud83c\\udfc6\\ud83e\\udd47 Tryna Get Down\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"U Kan Get It\"], \"description\": \"Previewed by @metalgearsolid in an Instagram Story. <PERSON> confirmed the song was releasing on Whole Lotta Red weeks before the album's release. The song was scrapped from the project right before its release, and producer <PERSON> still teases it on Twitter. It is often confused with the name \\\"U Kan Get It,\\\" but that's an entirely different unheard song. One of the biggest grails in the Carti community. <PERSON> leaked the beat in the Richie Grail zip, confirming the name as \\\"Tryna Get Down.\\\" The voice memo is dated April 13, 2019, so it's most likely from early April, though the original beat was made on February 9, 2019, meaning it could've been made anytime between February 9 and April 13. Technically part of the WLR V1 era due to being recorded before \\\"Designer Shoes.\\\"\", \"date\": 15928704, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"af183f2e9d9a15382dbd8f9bb382bf18\", \"url\": \"https://api.pillowcase.su/api/download/af183f2e9d9a15382dbd8f9bb382bf18\", \"size\": \"241 kB\", \"duration\": 15.07}", "aliases": ["U Kan Get It"], "size": "241 kB"}, {"id": "too", "name": "🥇 Too", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON>"], "notes": "Previewed in @keedollas and @kkanii1 on Instagram Stories. <PERSON><PERSON> feature is apparently shit.", "length": "0:07", "fileDate": 15566688, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/3173e24c74e51ab280ddfd5d8a9be7bb/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/3173e24c74e51ab280ddfd5d8a9be7bb/play\", \"key\": \"Too\", \"title\": \"\\ud83e\\udd47 Too\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON>) \", \"aliases\": [\"Things Too\"], \"description\": \"Previewed in @keedollas and @kkanii1 on Instagram Stories. <PERSON><PERSON> feature is apparently shit.\", \"date\": 15566688, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"19e0f5b246391d3d737e2c2408d142d7\", \"url\": \"https://api.pillowcase.su/api/download/19e0f5b246391d3d737e2c2408d142d7\", \"size\": \"118 kB\", \"duration\": 7.37}", "aliases": ["Things Too"], "size": "118 kB"}, {"id": "too-86", "name": "🥇 Too", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON>"], "notes": "Previewed in @keedollas and @kkanii1 on Instagram Stories. <PERSON><PERSON> feature is apparently shit.", "length": "", "fileDate": 15566688, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://soundcloud.com/samuel-babatunde-746446319/things-too-playboi-carti", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://soundcloud.com/samuel-babatunde-746446319/things-too-playboi-carti\", \"key\": \"Too\", \"title\": \"\\ud83e\\udd47 Too\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON>) \", \"aliases\": [\"Things Too\"], \"description\": \"Previewed in @keedollas and @kkanii1 on Instagram Stories. <PERSON><PERSON> feature is apparently shit.\", \"date\": 15566688, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["Things Too"], "size": ""}, {"id": "v-westwood-87", "name": "🥇 <PERSON><PERSON> [V1]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Earlier version than the leaked with alternate lyrics.", "length": "0:06", "fileDate": 15463872, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/44c65d395aa7ad3590a815cfa9cb1147/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/44c65d395aa7ad3590a815cfa9cb1147/play\", \"key\": \"<PERSON><PERSON>\", \"title\": \"\\ud83e\\udd47 <PERSON><PERSON> [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\"], \"description\": \"Earlier version than the leaked with alternate lyrics.\", \"date\": 15463872, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"7a3102bf4052d4ebfd69773b9a05f757\", \"url\": \"https://api.pillowcase.su/api/download/7a3102bf4052d4ebfd69773b9a05f757\", \"size\": \"97.5 kB\", \"duration\": 6.09}", "aliases": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "size": "97.5 kB"}, {"id": "pain-1993-88", "name": "Drake - Pain 1993 [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Previewed by <PERSON> in a tweet on June 7, 2019 and song was three seconds longer than the final version. A snippet of an alternate <PERSON><PERSON> verse surfaced before the release of the final. Reportedly not bounced, meaning it's lost", "length": "0:15", "fileDate": 15598656, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/962aa1e7893394950f0a294feded1965/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/962aa1e7893394950f0a294feded1965/play\", \"key\": \"Pain 1993\", \"title\": \"Drake - Pain 1993 [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Previewed by <PERSON> in a tweet on June 7, 2019 and song was three seconds longer than the final version. A snippet of an alternate <PERSON><PERSON> verse surfaced before the release of the final. Reportedly not bounced, meaning it's lost\", \"date\": 15598656, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"96d32515d2efd0601a293b1d54c4f4e2\", \"url\": \"https://api.pillowcase.su/api/download/96d32515d2efd0601a293b1d54c4f4e2\", \"size\": \"242 kB\", \"duration\": 15.1}", "aliases": [], "size": "242 kB"}, {"id": "pain-1993-89", "name": "Drake - Pain 1993 [V3]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Another version of \"Pain 1993\" is said to exist with an alternate verse from <PERSON><PERSON><PERSON> according to waterfalls. The link provided might be it.", "length": "0:15", "fileDate": 15463008, "leakDate": "", "labels": ["Recording", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/59eda306fba8057545d5633ef0d3f9cb/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/59eda306fba8057545d5633ef0d3f9cb/play\", \"key\": \"Pain 1993\", \"title\": \"Drake - Pain 1993 [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Another version of \\\"Pain 1993\\\" is said to exist with an alternate verse from <PERSON><PERSON><PERSON> according to waterfalls. The link provided might be it.\", \"date\": 15463008, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"49aee3be95ccf5253a931965b24e268d\", \"url\": \"https://api.pillowcase.su/api/download/49aee3be95ccf5253a931965b24e268d\", \"size\": \"241 kB\", \"duration\": 15.05}", "aliases": [], "size": "241 kB"}, {"id": "pissy-pamper-90", "name": "🗑️ Young Nudy & <PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON> [V4]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Another alternate version of \"Pissy Pamper\" featuring alternate production previewed on <PERSON><PERSON><PERSON><PERSON>'s Instagram Story. Likely made after the sample version was denied clearance so <PERSON><PERSON><PERSON><PERSON> attempted to rework the beat with no sample. The song completely loses one of the things that made it so good due to this", "length": "", "fileDate": 15589152, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/video/1d199dbbbe189a9747361150165d3ff0/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/video/1d199dbbbe189a9747361150165d3ff0/play\", \"key\": \"Pissy Pamper\", \"title\": \"\\ud83d\\uddd1\\ufe0f <PERSON> Nudy & <PERSON><PERSON><PERSON> - <PERSON><PERSON> [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"Kid Cudi\", \"<PERSON> Carti\", \"They Tryna Be Cray\"], \"description\": \"Another alternate version of \\\"Pissy Pamper\\\" featuring alternate production previewed on <PERSON><PERSON><PERSON><PERSON>'s Instagram Story. Likely made after the sample version was denied clearance so <PERSON><PERSON><PERSON><PERSON> attempted to rework the beat with no sample. The song completely loses one of the things that made it so good due to this\", \"date\": 15589152, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["<PERSON>", "<PERSON>", "They Tryna Be Cray"], "size": ""}, {"id": "earfquake", "name": "<PERSON>, The Creator - EARFQUAKE (Live Version) [V1]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON>", "The Creator"], "notes": "Leaked backing track for the live version of EARFQUAKE. Has missing piano parts.", "length": "3:17", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/7fe505be89e184bbd43bbaf4e094c3dc/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/7fe505be89e184bbd43bbaf4e094c3dc/play\", \"key\": \"EARFQUAKE (Live Version)\", \"title\": \"<PERSON>, The Creator - EARFQ<PERSON><PERSON><PERSON> (Live Version) [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>, The Creator)\", \"description\": \"Leaked backing track for the live version of EARFQUAKE. Has missing piano parts.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"99444a88951157210cb7c02f467a0359\", \"url\": \"https://api.pillowcase.su/api/download/99444a88951157210cb7c02f467a0359\", \"size\": \"3.16 MB\", \"duration\": 197.54}", "aliases": [], "size": "3.16 MB"}, {"id": "earfquake-92", "name": "<PERSON>, The Creator - EARFQUAKE (Live Version) [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON>", "The Creator"], "notes": "A version with piano intro. Played at 2019 Camp Flog Gnaw.", "length": "", "fileDate": "", "leakDate": "", "labels": ["Performance", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://www.youtube.com/watch?v=1qv84drJn6g&t=144s", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.youtube.com/watch?v=1qv84drJn6g&t=144s\", \"key\": \"EARFQUAKE (Live Version)\", \"title\": \"<PERSON>, The Creator - EARFQUAKE (Live Version) [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod<PERSON> <PERSON>, The Creator)\", \"description\": \"A version with piano intro. Played at 2019 Camp Flog Gnaw.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Performance\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"]}", "aliases": [], "size": ""}, {"id": "long-time", "name": "Long Time (2019 Bay Arena Version)", "artists": [], "producers": [], "notes": "A version of Long Time with extended Outro. Played by <PERSON><PERSON> on Bay Arena on September 30, 2019.", "length": "3:59", "fileDate": 15698016, "leakDate": "", "labels": ["Performance", "Full"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/ff25b3b63d2307d433ef3ec6b93f7361/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/ff25b3b63d2307d433ef3ec6b93f7361/play\", \"key\": \"Long Time (2019 Bay Arena Version)\", \"title\": \"Long Time (2019 Bay Arena Version)\", \"description\": \"A version of Long Time with extended Outro. Played by <PERSON><PERSON> on Bay Arena on September 30, 2019.\", \"date\": 15698016, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Performance\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"b2363471457cc7d0c38c5241deb3eb17\", \"url\": \"https://api.pillowcase.su/api/download/b2363471457cc7d0c38c5241deb3eb17\", \"size\": \"3.84 MB\", \"duration\": 239.95}", "aliases": [], "size": "3.84 MB"}, {"id": "tony-montana", "name": "✨ <PERSON><PERSON> - <PERSON>", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["?"], "notes": "OG Filename: PBC X KANI <PERSON> 2.11.19 \nOG Filename (Metadata): PBC X KANI <PERSON> 2.11.1\nOGF for <PERSON>", "length": "2:32", "fileDate": 16569792, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://music.froste.lol/song/d159bcb78c4a913975f538f23aa09ba3/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d159bcb78c4a913975f538f23aa09ba3/play\", \"key\": \"<PERSON>\", \"title\": \"\\u2728 <PERSON><PERSON>\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. ?)\", \"description\": \"OG Filename: PBC X KANI Tony Montana 2.11.19 \\nOG Filename (Metadata): PBC X KANI Tony <PERSON> 2.11.1\\nOGF for <PERSON>\", \"date\": 16569792, \"available\": [\"OG File\", \"rgb(0, 0, 0)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9d76b406941ac131ceac1dd2491ddc71\", \"url\": \"https://api.pillowcase.su/api/download/9d76b406941ac131ceac1dd2491ddc71\", \"size\": \"2.44 MB\", \"duration\": 152.06}", "aliases": [], "size": "2.44 MB"}, {"id": "few-times", "name": "✨ Few Times", "artists": [], "producers": ["London on da Track"], "notes": "Song was scrapped and no official bounces were created; The leaked file is a fanmade completed mix.", "length": "3:56", "fileDate": 16827264, "leakDate": "", "labels": ["Lossless", "Self-Bo<PERSON><PERSON>"], "links": [], "eraId": "whole-lotta-red-v1", "originalUrl": "https://pillowcase.su/f/3ecd6d3076c15c2146f31fe0b69c9a1f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3ecd6d3076c15c2146f31fe0b69c9a1f\", \"key\": \"Few Times\", \"title\": \"\\u2728 Few Times\", \"artists\": \"(prod. London on da Track)\", \"aliases\": [\"<PERSON>\", \"Selena\"], \"description\": \"Song was scrapped and no official bounces were created; The leaked file is a fanmade completed mix.\", \"date\": 16827264, \"available\": [\"Self-Bounce\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"2b5ec1c68b2fcc39167bde7e62f5afb7\", \"url\": \"https://api.pillowcase.su/api/download/2b5ec1c68b2fcc39167bde7e62f5afb7\", \"size\": \"4.15 MB\", \"duration\": 236.56}", "aliases": ["Dreams", "<PERSON>"], "size": "4.15 MB"}]}