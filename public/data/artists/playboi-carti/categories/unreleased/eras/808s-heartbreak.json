{"id": "808s-heartbreak", "name": "808s & Heartbreak", "description": "Following the death of his mother due to complications after cosmetic surgery, his relationship with fiancé <PERSON> finally ending for good, and a struggle to adapt to his celebrity status, <PERSON><PERSON><PERSON> felt emotionally drained and lost. <PERSON><PERSON><PERSON> dealt with his pain by channeling it into a sonically stripped-down album, one dominated by his use of the titular Roland TR-808 drum machine and Auto-Tune. This album significantly influenced future hip-hop music, having influenced <PERSON>, <PERSON>, <PERSON>, and more.", "backgroundColor": "rgb(195, 205, 204)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17Gy2vzbWLroiGirahQIihhUCs6V9tCGMp0yIdZipH14M1baI1E8bid1HhFeyfhL6jNtPbSPCM9zPYQeM0ov5LbQ9fR_CeXPC05VS7mIjQXBL_JxwYUJ7Z7aApPue0yFs9HHTqgJlJRWDVBI460?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "amazing", "name": "✨ Amazing [V2]", "artists": [], "producers": [], "notes": "The-Dream reference track for the song. Leaked via a unofficial mixtape entitled LoveTape: The Demo's.", "length": "233.09", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/5bec67ae0b46d784f6c2d15a568f1825", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5bec67ae0b46d784f6c2d15a568f1825\", \"key\": \"Amazing\", \"title\": \"\\u2728 Amazing [V2]\", \"artists\": \"(ref. The-Dream)\", \"description\": \"The-Dream reference track for the song. Leaked via a unofficial mixtape entitled LoveTape: The Demo's.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"460a245af28d71fa912c848bf87d9ebb\", \"url\": \"https://api.pillowcase.su/api/download/460a245af28d71fa912c848bf87d9ebb\", \"size\": \"4.09 MB\", \"duration\": 233.09}", "aliases": [], "size": "4.09 MB"}, {"id": "amazing-2", "name": "Amazing [V3]", "artists": ["<PERSON>ezy", "Mr <PERSON>", "The WRLDFMS <PERSON>"], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: amazing-vlado-master-11_6b\nOG file for \"Amazing\". Also linked is the same file but found on <PERSON><PERSON><PERSON>'s blog, with more metadata.", "length": "238.42", "fileDate": 16046208, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/1c90bf977d093092b7b1a7af0a576961", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1c90bf977d093092b7b1a7af0a576961\", \"key\": \"Amazing\", \"title\": \"Amazing [V3]\", \"artists\": \"(feat. <PERSON>, <PERSON> & The WRLDFMS <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: amazing-vlado-master-11_6b\\nOG file for \\\"Amazing\\\". Also linked is the same file but found on <PERSON><PERSON><PERSON>'s blog, with more metadata.\", \"date\": 16046208, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"98c10892d13dee3c2634a9d4042e3e9f\", \"url\": \"https://api.pillowcase.su/api/download/98c10892d13dee3c2634a9d4042e3e9f\", \"size\": \"4.18 MB\", \"duration\": 238.42}", "aliases": [], "size": "4.18 MB"}, {"id": "amazing-3", "name": "Amazing [V3]", "artists": ["<PERSON>ezy", "Mr <PERSON>", "The WRLDFMS <PERSON>"], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: amazing-vlado-master-11_6b\nOG file for \"Amazing\". Also linked is the same file but found on <PERSON><PERSON><PERSON>'s blog, with more metadata.", "length": "238.42", "fileDate": 16046208, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/6e8367339444c618ca321607af3ed804", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6e8367339444c618ca321607af3ed804\", \"key\": \"Amazing\", \"title\": \"Amazing [V3]\", \"artists\": \"(feat. <PERSON>, <PERSON> & The WRLDFMS <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: amazing-vlado-master-11_6b\\nOG file for \\\"Amazing\\\". Also linked is the same file but found on <PERSON><PERSON><PERSON>'s blog, with more metadata.\", \"date\": 16046208, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1a50cdeb0854c1731793266908446d87\", \"url\": \"https://api.pillowcase.su/api/download/1a50cdeb0854c1731793266908446d87\", \"size\": \"4.18 MB\", \"duration\": 238.42}", "aliases": [], "size": "4.18 MB"}, {"id": "anyway", "name": "Anyway [V1]", "artists": ["<PERSON>"], "producers": [], "notes": "OG Filename: Anyway.1 RUFF ye vox\nEarly version of \"Paranoid\".", "length": "281.74", "fileDate": 17068320, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/7e754b84098c3a3ee931dda3fa529f83", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7e754b84098c3a3ee931dda3fa529f83\", \"key\": \"Anyway\", \"title\": \"Anyway [V1]\", \"artists\": \"(feat. <PERSON>)\", \"aliases\": [\"Paranoid\"], \"description\": \"OG Filename: Anyway.1 RUFF ye vox\\nEarly version of \\\"Paranoid\\\".\", \"date\": 17068320, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"fcbdc8c2f1390d88fc466ce7dccb6a57\", \"url\": \"https://api.pillowcase.su/api/download/fcbdc8c2f1390d88fc466ce7dccb6a57\", \"size\": \"4.87 MB\", \"duration\": 281.74}", "aliases": ["Paranoid"], "size": "4.87 MB"}, {"id": "anyway-5", "name": "Anyway [V5]", "artists": ["<PERSON>"], "producers": [], "notes": "OG Filename: Anyway 88 REF 05\n88-Keys reference track for \"Anyway\". Just contains a partial verse.", "length": "108.52", "fileDate": 17074368, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/d90ef9ccedf259a7b5bd72ba7b67d20a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d90ef9ccedf259a7b5bd72ba7b67d20a\", \"key\": \"Anyway\", \"title\": \"Anyway [V5]\", \"artists\": \"(ref. 88-<PERSON>) (feat. <PERSON>)\", \"aliases\": [\"Paranoid\"], \"description\": \"OG Filename: Anyway 88 REF 05\\n88-Keys reference track for \\\"Anyway\\\". Just contains a partial verse.\", \"date\": 17074368, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6565fd82d402511750a4bcfdb31bf046\", \"url\": \"https://api.pillowcase.su/api/download/6565fd82d402511750a4bcfdb31bf046\", \"size\": \"2.1 MB\", \"duration\": 108.52}", "aliases": ["Paranoid"], "size": "2.1 MB"}, {"id": "anyway-6", "name": "Anyway [V7]", "artists": ["Mr <PERSON>", "<PERSON>"], "producers": [], "notes": "OG Filename: Anyway.07 quence rev verses 09\nConsequence stated he \"did the verses on the song\". Also features no <PERSON><PERSON><PERSON> autotune on the intro. The .07 in the filename may mean there are other mixes/versions.", "length": "293.75", "fileDate": 17044128, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/a89f7f0a3db15709ef1f64b0007ce9ce", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a89f7f0a3db15709ef1f64b0007ce9ce\", \"key\": \"Anyway\", \"title\": \"Anyway [V7]\", \"artists\": \"(ref. Consequence) (feat. <PERSON> & <PERSON>)\", \"aliases\": [\"Paranoid\"], \"description\": \"OG Filename: Anyway.07 quence rev verses 09\\nConsequence stated he \\\"did the verses on the song\\\". Also features no Kanye autotune on the intro. The .07 in the filename may mean there are other mixes/versions.\", \"date\": 17044128, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"b6a21abd06b284e1059191b15302c0bf\", \"url\": \"https://api.pillowcase.su/api/download/b6a21abd06b284e1059191b15302c0bf\", \"size\": \"5.06 MB\", \"duration\": 293.75}", "aliases": ["Paranoid"], "size": "5.06 MB"}, {"id": "anyway-7", "name": "Anyway [V8]", "artists": ["<PERSON>", "Mr <PERSON>"], "producers": [], "notes": "OG Filename: Anyway Ref3 Main Vladobounce\nOriginal version of \"Paranoid\" untagged. Mixing is very different, sounds like some different vocal takes. Less autotune and different intro on the verses.", "length": "278.02", "fileDate": 16043616, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/f185c018ce952fd2b65acad75ab4b7f4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f185c018ce952fd2b65acad75ab4b7f4\", \"key\": \"Anyway\", \"title\": \"Anyway [V8]\", \"artists\": \"(feat. <PERSON> & <PERSON>)\", \"aliases\": [\"Paranoid\"], \"description\": \"OG Filename: Anyway Ref3 Main Vladobounce\\nOriginal version of \\\"Paranoid\\\" untagged. Mixing is very different, sounds like some different vocal takes. Less autotune and different intro on the verses.\", \"date\": 16043616, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"40c962b4b5519424fe432ebdea0624cf\", \"url\": \"https://api.pillowcase.su/api/download/40c962b4b5519424fe432ebdea0624cf\", \"size\": \"4.81 MB\", \"duration\": 278.02}", "aliases": ["Paranoid"], "size": "4.81 MB"}, {"id": "frozen-winter", "name": "Frozen Winter", "artists": [], "producers": ["Kanye West", "No I.D.", "<PERSON>"], "notes": "OG Filename: Frozen Winter.1 RUFF\nEarly version of \"Coldest Winter\" with alternate lines, including punch-ins where <PERSON><PERSON><PERSON> changes the \"coldest\" in \"coldest winter\" to \"frozen\", for some reason. Otherwise similar production-wise to release. Leaked after a groupbuy.", "length": "164.78", "fileDate": 17413920, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/ff5a1e089b94d4dc62c2010cfb747601", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ff5a1e089b94d4dc62c2010cfb747601\", \"key\": \"Frozen Winter\", \"title\": \"Frozen Winter\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"aliases\": [\"Coldest Winter\"], \"description\": \"OG Filename: Frozen Winter.1 RUFF\\nEarly version of \\\"Coldest Winter\\\" with alternate lines, including punch-ins where <PERSON><PERSON><PERSON> changes the \\\"coldest\\\" in \\\"coldest winter\\\" to \\\"frozen\\\", for some reason. Otherwise similar production-wise to release. Leaked after a groupbuy.\", \"date\": 17413920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4f1b0d4d57da71fa32f7849a270fd4cc\", \"url\": \"https://api.pillowcase.su/api/download/4f1b0d4d57da71fa32f7849a270fd4cc\", \"size\": \"3 MB\", \"duration\": 164.78}", "aliases": ["Coldest Winter"], "size": "3 MB"}, {"id": "heartless", "name": "Heartless [V2]", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON> 1st and 2nd Verse RUFF\nEarly version of \"Heartless\" with way rougher mixing, no 'Hey' sound effects on the second verse and mumble on the third verse.", "length": "211.41", "fileDate": 17080416, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/8f1e652c7f6e84f6cd42c90521b209ff", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8f1e652c7f6e84f6cd42c90521b209ff\", \"key\": \"Heartless\", \"title\": \"Heartless [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Hartless\"], \"description\": \"OG Filename: Hartless 1st and 2nd Verse RUFF\\nEarly version of \\\"Heartless\\\" with way rougher mixing, no 'Hey' sound effects on the second verse and mumble on the third verse.\", \"date\": 17080416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5c066584ed5612c568dc079a3f58c96d\", \"url\": \"https://api.pillowcase.su/api/download/5c066584ed5612c568dc079a3f58c96d\", \"size\": \"3.75 MB\", \"duration\": 211.41}", "aliases": ["<PERSON><PERSON>"], "size": "3.75 MB"}, {"id": "heartless-10", "name": "Heartless [V3]", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON>"], "notes": "OG Filename: 1-01-heartless\nUnmastered leak of \"Heartless\".", "length": "212.38", "fileDate": 16044480, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/76776aaa843bb313967c8024477beb05", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/76776aaa843bb313967c8024477beb05\", \"key\": \"Heartless\", \"title\": \"Heartless [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & E. <PERSON>)\", \"description\": \"OG Filename: 1-01-heartless\\nUnmastered leak of \\\"Heartless\\\".\", \"date\": 16044480, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a9d2872ed8b0b081cef5dc44f7d95356\", \"url\": \"https://api.pillowcase.su/api/download/a9d2872ed8b0b081cef5dc44f7d95356\", \"size\": \"3.76 MB\", \"duration\": 212.38}", "aliases": [], "size": "3.76 MB"}, {"id": "heartless-11", "name": "Heartless [V4]", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON> - <PERSON><PERSON> 11_6b\nOG mastered file of the released version of \"Heartless\". Leaked as part of the \"Can U Be\" groupbuy. Also linked is the same file found on <PERSON><PERSON><PERSON>'s blog, with different metadata.", "length": "211.1", "fileDate": 17166816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/32e4deffeac01822a70e27dc121a5759", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/32e4deffeac01822a70e27dc121a5759\", \"key\": \"Heartless\", \"title\": \"Heartless [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & E. <PERSON>)\", \"description\": \"OG Filename: <PERSON>less - <PERSON><PERSON> 11_6b\\nOG mastered file of the released version of \\\"Heartless\\\". Leaked as part of the \\\"Can U Be\\\" groupbuy. Also linked is the same file found on <PERSON><PERSON><PERSON>'s blog, with different metadata.\", \"date\": 17166816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a768d48ad7bd6dd3c2ec4741a9cbf18f\", \"url\": \"https://api.pillowcase.su/api/download/a768d48ad7bd6dd3c2ec4741a9cbf18f\", \"size\": \"3.74 MB\", \"duration\": 211.1}", "aliases": [], "size": "3.74 MB"}, {"id": "heartless-12", "name": "Heartless [V4]", "artists": [], "producers": ["Kanye West", "<PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON> - <PERSON><PERSON> 11_6b\nOG mastered file of the released version of \"Heartless\". Leaked as part of the \"Can U Be\" groupbuy. Also linked is the same file found on <PERSON><PERSON><PERSON>'s blog, with different metadata.", "length": "", "fileDate": 17166816, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/0f9157bee4a50bc09802b9f9f604f26e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0f9157bee4a50bc09802b9f9f604f26e\", \"key\": \"Heartless\", \"title\": \"Heartless [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: Heartless - <PERSON><PERSON> 11_6b\\nOG mastered file of the released version of \\\"Heartless\\\". Leaked as part of the \\\"Can U Be\\\" groupbuy. Also linked is the same file found on <PERSON><PERSON><PERSON>'s blog, with different metadata.\", \"date\": 17166816, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": [], "size": ""}, {"id": "hey-mama", "name": "Hey <PERSON> (Grammy Remix)", "artists": [], "producers": [], "notes": "Version of \"Hey Mama\" with orchestral production and re-recorded studio vocals that seemingly never got released. Not to be confused with the 2008 Grammy live performance version. Leaked on HipHopDX.", "length": "174.77", "fileDate": 12029472, "leakDate": "", "availableLength": "Tagged", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/8e557e5a4cc12fa1be13e7d44a6bd801", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8e557e5a4cc12fa1be13e7d44a6bd801\", \"key\": \"Hey <PERSON> (Grammy Remix)\", \"title\": \"Hey <PERSON> (Grammy Remix)\", \"description\": \"Version of \\\"Hey Mama\\\" with orchestral production and re-recorded studio vocals that seemingly never got released. Not to be confused with the 2008 Grammy live performance version. Leaked on HipHopDX.\", \"date\": 12029472, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"5406e2a3e4ded06e37dc2350795c2d79\", \"url\": \"https://api.pillowcase.su/api/download/5406e2a3e4ded06e37dc2350795c2d79\", \"size\": \"3.16 MB\", \"duration\": 174.77}", "aliases": [], "size": "3.16 MB"}, {"id": "hot-sauce", "name": "Hot Sauce", "artists": ["<PERSON>"], "producers": [], "notes": "Mumble demo worked on in the 808s & Heartbreak sessions. Was described by Consequence as having \"W mumble\". Small snippets of this fairly unknown track were played on Consequence's IG streams multiple times.", "length": "21.24", "fileDate": 17224704, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/0df1f2586f931db9a89582905513b691", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0df1f2586f931db9a89582905513b691\", \"key\": \"Hot Sauce\", \"title\": \"Hot Sauce\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"Mumble demo worked on in the 808s & Heartbreak sessions. Was described by Consequence as having \\\"W mumble\\\". Small snippets of this fairly unknown track were played on Consequence's IG streams multiple times.\", \"date\": 17224704, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"e4baa7fad182df880dee879e299faab9\", \"url\": \"https://api.pillowcase.su/api/download/e4baa7fad182df880dee879e299faab9\", \"size\": \"703 kB\", \"duration\": 21.24}", "aliases": [], "size": "703 kB"}, {"id": "hot-sauce-15", "name": "Hot Sauce", "artists": ["<PERSON>"], "producers": [], "notes": "Mumble demo worked on in the 808s & Heartbreak sessions. Was described by Consequence as having \"W mumble\". Small snippets of this fairly unknown track were played on Consequence's IG streams multiple times.", "length": "", "fileDate": 17224704, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/3620cac93f30477ec4acde044acef03c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3620cac93f30477ec4acde044acef03c\", \"key\": \"Hot Sauce\", \"title\": \"Hot Sauce\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"Mumble demo worked on in the 808s & Heartbreak sessions. Was described by Consequence as having \\\"W mumble\\\". Small snippets of this fairly unknown track were played on Consequence's IG streams multiple times.\", \"date\": 17224704, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "hot-sauce-16", "name": "Hot Sauce", "artists": ["<PERSON>"], "producers": [], "notes": "Mumble demo worked on in the 808s & Heartbreak sessions. Was described by Consequence as having \"W mumble\". Small snippets of this fairly unknown track were played on Consequence's IG streams multiple times.", "length": "47.28", "fileDate": 17224704, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/201115392cfa946880d4fb251013d513", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/201115392cfa946880d4fb251013d513\", \"key\": \"Hot Sauce\", \"title\": \"Hot Sauce\", \"artists\": \"(feat. <PERSON>)\", \"description\": \"Mumble demo worked on in the 808s & Heartbreak sessions. Was described by Consequence as having \\\"W mumble\\\". Small snippets of this fairly unknown track were played on Consequence's IG streams multiple times.\", \"date\": 17224704, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"3fcfe1f919bb6d17541840e2c4c8c5eb\", \"url\": \"https://api.pillowcase.su/api/download/3fcfe1f919bb6d17541840e2c4c8c5eb\", \"size\": \"742 kB\", \"duration\": 47.28}", "aliases": [], "size": "742 kB"}, {"id": "live-to-tell", "name": "Live To Tell", "artists": [], "producers": ["Kanye West"], "notes": "OG Filename: Live To Tell - <PERSON><PERSON><PERSON> - R<PERSON>. REF For Madonna\nSubmission for a demo instrumental which samples \"Live To Tell\" by <PERSON>, it was sent to her in 2007 during the Hard Candy sessions. Unknown if <PERSON> or <PERSON> recorded anything. Made in October 2007, making it the first known song made after <PERSON><PERSON><PERSON>'s release but before <PERSON><PERSON>'s death.", "length": "233.12", "fileDate": 17387136, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/7b1369ed18ce543418a9c5b02aaa3789", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7b1369ed18ce543418a9c5b02aaa3789\", \"key\": \"Live To Tell\", \"title\": \"Live To Tell\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Live To Tell - Ka<PERSON><PERSON> - <PERSON>. REF For <PERSON>\\nSubmission for a demo instrumental which samples \\\"Live To Tell\\\" by <PERSON>, it was sent to her in 2007 during the Hard Candy sessions. Unknown if <PERSON> or <PERSON> recorded anything. Made in October 2007, making it the first known song made after <PERSON><PERSON><PERSON>'s release but before <PERSON><PERSON>'s death.\", \"date\": 17387136, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"53bdf135ee443e62be5df001112595f3\", \"url\": \"https://api.pillowcase.su/api/download/53bdf135ee443e62be5df001112595f3\", \"size\": \"4.09 MB\", \"duration\": 233.12}", "aliases": [], "size": "4.09 MB"}, {"id": "love-lockdown", "name": "Love Lockdown [V3]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: LoveLockdown_V2ADMix-Main_9-15-08\nVery close to release ready, but metronome can still be heard. Unmastered.", "length": "270.92", "fileDate": 16045344, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/ab8b1e97ed1d208d7c0d144d58332306", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ab8b1e97ed1d208d7c0d144d58332306\", \"key\": \"Love Lockdown\", \"title\": \"Love Lockdown [V3]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: LoveLockdown_V2ADMix-Main_9-15-08\\nVery close to release ready, but metronome can still be heard. Unmastered.\", \"date\": 16045344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"57da070cbc4a86caeb6d7f338673a62e\", \"url\": \"https://api.pillowcase.su/api/download/57da070cbc4a86caeb6d7f338673a62e\", \"size\": \"4.7 MB\", \"duration\": 270.92}", "aliases": [], "size": "4.7 MB"}, {"id": "love-lockdown-19", "name": "Love Lockdown [V4]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: Love Lockdown - <PERSON><PERSON> 11_6b\nOG file of an early master for the released version of \"Love Lockdown\". Leaked in the Tracker discord. Also linked is the same file but found on <PERSON><PERSON><PERSON>'s blog, which is slightly higher quality and has more metadata.", "length": "270.84", "fileDate": 16045344, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/a702da75d70fc1449d5f7ad7f42c6adc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a702da75d70fc1449d5f7ad7f42c6adc\", \"key\": \"Love Lockdown\", \"title\": \"Love Lockdown [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: Love Lockdown - Vlado Master 11_6b\\nOG file of an early master for the released version of \\\"Love Lockdown\\\". Leaked in the Tracker discord. Also linked is the same file but found on <PERSON><PERSON><PERSON>'s blog, which is slightly higher quality and has more metadata.\", \"date\": 16045344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"40d8e4d2f7d97d6a768a937404a3c680\", \"url\": \"https://api.pillowcase.su/api/download/40d8e4d2f7d97d6a768a937404a3c680\", \"size\": \"4.7 MB\", \"duration\": 270.84}", "aliases": [], "size": "4.7 MB"}, {"id": "love-lockdown-20", "name": "Love Lockdown [V4]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: Love Lockdown - <PERSON><PERSON> 11_6b\nOG file of an early master for the released version of \"Love Lockdown\". Leaked in the Tracker discord. Also linked is the same file but found on <PERSON><PERSON><PERSON>'s blog, which is slightly higher quality and has more metadata.", "length": "270.84", "fileDate": 16045344, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/23f0271c6b26f03bf67d7e9988669e18", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/23f0271c6b26f03bf67d7e9988669e18\", \"key\": \"Love Lockdown\", \"title\": \"Love Lockdown [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: Love Lockdown - Vlado Master 11_6b\\nOG file of an early master for the released version of \\\"Love Lockdown\\\". Leaked in the Tracker discord. Also linked is the same file but found on <PERSON><PERSON><PERSON>'s blog, which is slightly higher quality and has more metadata.\", \"date\": 16045344, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4d918392dd8e1abde1eba4fbd178eaa4\", \"url\": \"https://api.pillowcase.su/api/download/4d918392dd8e1abde1eba4fbd178eaa4\", \"size\": \"4.7 MB\", \"duration\": 270.84}", "aliases": [], "size": "4.7 MB"}, {"id": "real-bad-news", "name": "Real Bad News", "artists": [], "producers": [], "notes": "Original version of \"Bad News\".", "length": "241.24", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/55e26ab72111415db11505dba73d6551", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/55e26ab72111415db11505dba73d6551\", \"key\": \"Real Bad News\", \"title\": \"Real Bad News\", \"aliases\": [\"Bad News\"], \"description\": \"Original version of \\\"Bad News\\\".\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"80a6f78f7000fb8a066760a820b6e8a8\", \"url\": \"https://api.pillowcase.su/api/download/80a6f78f7000fb8a066760a820b6e8a8\", \"size\": \"4.22 MB\", \"duration\": 241.24}", "aliases": ["Bad News"], "size": "4.22 MB"}, {"id": "robocop", "name": "RoboCop [V1]", "artists": [], "producers": ["Kanye West", "A-Trak", "<PERSON>"], "notes": "OG Filename: Robocop RUFF ye REF\nMumble version of \"RoboCop\". Has a section where <PERSON><PERSON><PERSON> interpolates \"Technologic\" by Daft Punk. Original snippet leaked June 7th, 2024.", "length": "233.18", "fileDate": 17207424, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/a7f84fd3f85eb4fe7691ef0627a2ca34", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a7f84fd3f85eb4fe7691ef0627a2ca34\", \"key\": \"RoboCop\", \"title\": \"RoboCop [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & Larry <PERSON>)\", \"description\": \"OG Filename: Robocop RUFF ye REF\\nMumble version of \\\"RoboCop\\\". Has a section where <PERSON><PERSON><PERSON> interpolates \\\"Technologic\\\" by Daft Punk. Original snippet leaked June 7th, 2024.\", \"date\": 17207424, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"0adbc02a0e909b5d4d6ff537a125cfff\", \"url\": \"https://api.pillowcase.su/api/download/0adbc02a0e909b5d4d6ff537a125cfff\", \"size\": \"4.09 MB\", \"duration\": 233.18}", "aliases": [], "size": "4.09 MB"}, {"id": "robocop-23", "name": "RoboCop [V4]", "artists": [], "producers": ["Kanye West", "A-Trak", "<PERSON>"], "notes": "OG Filename: RobocopR<PERSON>-<PERSON><PERSON>_.Main_mp3\nEarly version of \"RoboCop\" that leaked on the 12th anniversary of the album. Earlier instrumental than V5, but has mumble.", "length": "231.6", "fileDate": 16061760, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/8782e42ddcbf1be638a0015ae8942a0b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8782e42ddcbf1be638a0015ae8942a0b\", \"key\": \"RoboCop\", \"title\": \"RoboCop [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: RobocopR<PERSON>-<PERSON><PERSON>_.Main_mp3\\nEarly version of \\\"RoboCop\\\" that leaked on the 12th anniversary of the album. Earlier instrumental than V5, but has mumble.\", \"date\": 16061760, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"be8596b00bbfe617a102ee691d89be94\", \"url\": \"https://api.pillowcase.su/api/download/be8596b00bbfe617a102ee691d89be94\", \"size\": \"4.07 MB\", \"duration\": 231.6}", "aliases": [], "size": "4.07 MB"}, {"id": "robocop-24", "name": "RoboCop [V5]", "artists": [], "producers": ["Kanye West", "A-Trak", "<PERSON>"], "notes": "Same as <PERSON><PERSON> besides new vocals at 2:00-2:10, tagged.", "length": "233.48", "fileDate": 12248928, "leakDate": "", "availableLength": "Tagged", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/fb8ef70dac331abc63d3991975712062", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fb8ef70dac331abc63d3991975712062\", \"key\": \"RoboCop\", \"title\": \"RoboCop [V5]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Same as V4 besides new vocals at 2:00-2:10, tagged.\", \"date\": 12248928, \"available\": [\"Tagged\", \"rgb(255, 255, 255)\", \"rgb(39, 78, 19)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"45545d734ecde86bcc5694aa282006cd\", \"url\": \"https://api.pillowcase.su/api/download/45545d734ecde86bcc5694aa282006cd\", \"size\": \"4.1 MB\", \"duration\": 233.48}", "aliases": [], "size": "4.1 MB"}, {"id": "robocop-25", "name": "RoboCop [V6]", "artists": [], "producers": ["Kanye West", "A-Trak", "<PERSON>"], "notes": "Percussion is mixed differently and the outro verse is absent.", "length": "206.13", "fileDate": 12248064, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/78123d38b13d638e3e240644b8ad4753", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/78123d38b13d638e3e240644b8ad4753\", \"key\": \"RoboCop\", \"title\": \"RoboCop [V6]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Percussion is mixed differently and the outro verse is absent.\", \"date\": 12248064, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4a401e6b571ff3ff2dac64e76d4b383b\", \"url\": \"https://api.pillowcase.su/api/download/4a401e6b571ff3ff2dac64e76d4b383b\", \"size\": \"3.66 MB\", \"duration\": 206.13}", "aliases": [], "size": "3.66 MB"}, {"id": "robocop-26", "name": "RoboCop [V7]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: ROBOCOP.10 AK MIX 1.1\nVersion of \"RoboCop\" with production from <PERSON> that is extremely distorted. Missing the \"spoiled little LA girl\" outro.", "length": "132.62", "fileDate": 17343936, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/f8c1c4a53b232babd48ee8933cb33e43", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f8c1c4a53b232babd48ee8933cb33e43\", \"key\": \"RoboCop\", \"title\": \"RoboCop [V7]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: ROBOCOP.10 AK MIX 1.1\\nVersion of \\\"RoboCop\\\" with production from <PERSON> that is extremely distorted. Missing the \\\"spoiled little <PERSON> girl\\\" outro.\", \"date\": 17343936, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4d57e0503476414a892a9ac857dd1a61\", \"url\": \"https://api.pillowcase.su/api/download/4d57e0503476414a892a9ac857dd1a61\", \"size\": \"2.49 MB\", \"duration\": 132.62}", "aliases": [], "size": "2.49 MB"}, {"id": "robocop-27", "name": "RoboCop [V8]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: RoboCop New Mix - VLAD Outro 1221\nVersion of \"RoboCop\" same as release but with extra strings added onto the outro.", "length": "304.08", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/c1603be760f28331917028d896212dc3", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c1603be760f28331917028d896212dc3\", \"key\": \"RoboCop\", \"title\": \"RoboCop [V8]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: RoboCop New Mix - VLAD Outro 1221\\nVersion of \\\"RoboCop\\\" same as release but with extra strings added onto the outro.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5c93d9fd0fa96a5e9c77117425c26561\", \"url\": \"https://api.pillowcase.su/api/download/5c93d9fd0fa96a5e9c77117425c26561\", \"size\": \"5.23 MB\", \"duration\": 304.08}", "aliases": [], "size": "5.23 MB"}, {"id": "robocop-28", "name": "RoboCop (Remix)", "artists": ["The WRLDFMS <PERSON>"], "producers": [], "notes": "OG Filename: Robocop Remix.3\nUnreleased version of the song made for the music video. This version has different drums and additional <PERSON> vocals. Was made post 808s & Heartbreak's release, likely made as a \"New Mix\" version of the song like other 808s & Heartbreak's tracks. Was scrapped after <PERSON> and <PERSON><PERSON><PERSON> broke up.", "length": "245.88", "fileDate": 17284320, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/19468c23446ed5ae83133f749293d250", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/19468c23446ed5ae83133f749293d250\", \"key\": \"<PERSON><PERSON><PERSON><PERSON> (Remix)\", \"title\": \"<PERSON><PERSON><PERSON><PERSON> (Remix)\", \"artists\": \"(feat. The WRLDFMS <PERSON>)\", \"description\": \"OG Filename: Robocop Remix.3\\nUnreleased version of the song made for the music video. This version has different drums and additional <PERSON> vocals. Was made post 808s & Heartbreak's release, likely made as a \\\"New Mix\\\" version of the song like other 808s & Heartbreak's tracks. Was scrapped after <PERSON> and <PERSON><PERSON><PERSON> broke up.\", \"date\": 17284320, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a032f16dceb03efec4a8b8b184f133aa\", \"url\": \"https://api.pillowcase.su/api/download/a032f16dceb03efec4a8b8b184f133aa\", \"size\": \"4.3 MB\", \"duration\": 245.88}", "aliases": [], "size": "4.3 MB"}, {"id": "say-you-will", "name": "Say You Will [V4]", "artists": [], "producers": [], "notes": "Version with both <PERSON><PERSON><PERSON> and <PERSON> vocals. Snippet leaked May 31st, 2024.", "length": "15.5", "fileDate": 17171136, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/e0b5671c52e9a872c6fb36ca8432b0a7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e0b5671c52e9a872c6fb36ca8432b0a7\", \"key\": \"Say You Will\", \"title\": \"Say You Will [V4]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Version with both <PERSON><PERSON><PERSON> and <PERSON> vocals. Snippet leaked May 31st, 2024.\", \"date\": 17171136, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"f3cb76e8bb753ec019fa298eb6272b74\", \"url\": \"https://api.pillowcase.su/api/download/f3cb76e8bb753ec019fa298eb6272b74\", \"size\": \"612 kB\", \"duration\": 15.5}", "aliases": [], "size": "612 kB"}, {"id": "say-you-will-30", "name": "Say You Will [V5]", "artists": [], "producers": [], "notes": "Has a shorter outro and is missing the transition into \"Welcome To Heartbreak\".", "length": "333.22", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/5b0954cb877abcb7d1a8f131c2f501eb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5b0954cb877abcb7d1a8f131c2f501eb\", \"key\": \"Say You Will\", \"title\": \"Say You Will [V5]\", \"description\": \"Has a shorter outro and is missing the transition into \\\"Welcome To Heartbreak\\\".\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"2b8b5f783908e79766985bd554d12bfa\", \"url\": \"https://api.pillowcase.su/api/download/2b8b5f783908e79766985bd554d12bfa\", \"size\": \"5.69 MB\", \"duration\": 333.22}", "aliases": [], "size": "5.69 MB"}, {"id": "see-you-in-my-nightmares", "name": "See You in My Nightmares (Remix)", "artists": ["<PERSON>"], "producers": ["Squeak E. Clean"], "notes": "Unreleased remix from the short film We Were Once A Fairytale. Referred to as \"See You In My Nightmares (Bumptempo Squeak E. Clean Remix)\". Only a low quality snippet from the movie was available for some time.", "length": "251.19", "fileDate": 16120512, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/0b87dee5745ebaa7369600dfa15ca66d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0b87dee5745ebaa7369600dfa15ca66d\", \"key\": \"See You in My Nightmares (Remix)\", \"title\": \"See You in My Nightmares (Remix)\", \"artists\": \"(feat. <PERSON>) (prod. Squeak E. Clean)\", \"description\": \"Unreleased remix from the short film We Were Once A Fairytale. Referred to as \\\"See You In My Nightmares (Bumptempo Squeak E. Clean Remix)\\\". Only a low quality snippet from the movie was available for some time.\", \"date\": 16120512, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"6bd95075972be2e0ad7860ea6cd8e8be\", \"url\": \"https://api.pillowcase.su/api/download/6bd95075972be2e0ad7860ea6cd8e8be\", \"size\": \"4.38 MB\", \"duration\": 251.19}", "aliases": [], "size": "4.38 MB"}, {"id": "smoked-up", "name": "Smoked Up [V1]", "artists": [], "producers": ["Justice"], "notes": "Initial freestyle. All freestyles were recorded September 25th, 2008, however the beat is dated September 23rd, 2008. Leaked after a successful Soakbuy.", "length": "237.41", "fileDate": 17403552, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/7e2ec17b63637963d3731bf5c88ef341", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7e2ec17b63637963d3731bf5c88ef341\", \"key\": \"Smoked Up\", \"title\": \"Smoked Up [V1]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Justice Beat\"], \"description\": \"Initial freestyle. All freestyles were recorded September 25th, 2008, however the beat is dated September 23rd, 2008. Leaked after a successful Soakbuy.\", \"date\": 17403552, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e08422bc37398857368b37d4bac99ea2\", \"url\": \"https://api.pillowcase.su/api/download/e08422bc37398857368b37d4bac99ea2\", \"size\": \"4.16 MB\", \"duration\": 237.41}", "aliases": ["Justice Beat"], "size": "4.16 MB"}, {"id": "smoked-up-33", "name": "Smoked Up [V2]", "artists": [], "producers": ["Justice"], "notes": "Second freestyle. Has further along production. Leaked after a successful Soakbuy.", "length": "237.41", "fileDate": 17403552, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/9c0686ab53b5b4f339a5d4c9403d12a9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9c0686ab53b5b4f339a5d4c9403d12a9\", \"key\": \"Smoked Up\", \"title\": \"Smoked Up [V2]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Justice Beat\"], \"description\": \"Second freestyle. Has further along production. Leaked after a successful Soakbuy.\", \"date\": 17403552, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"77a612498fa0c3c349d5289df11a945b\", \"url\": \"https://api.pillowcase.su/api/download/77a612498fa0c3c349d5289df11a945b\", \"size\": \"4.16 MB\", \"duration\": 237.41}", "aliases": ["Justice Beat"], "size": "4.16 MB"}, {"id": "smoked-up-34", "name": "Smoked Up [V3]", "artists": [], "producers": ["Justice"], "notes": "Third freestyle. Leaked after a successful Soakbuy. <PERSON><PERSON><PERSON> mentions jews in this freestyle near the start.", "length": "238.09", "fileDate": 17403552, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/37046df0649a6b7d86d0abf9c747b9f7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/37046df0649a6b7d86d0abf9c747b9f7\", \"key\": \"Smoked Up\", \"title\": \"Smoked Up [V3]\", \"artists\": \"(prod. Justice)\", \"aliases\": [\"Justice Beat\"], \"description\": \"Third freestyle. Leaked after a successful Soakbuy. <PERSON><PERSON><PERSON> mentions jews in this freestyle near the start.\", \"date\": 17403552, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"48d6fd52a2060c30d5730042a8c9097d\", \"url\": \"https://api.pillowcase.su/api/download/48d6fd52a2060c30d5730042a8c9097d\", \"size\": \"4.17 MB\", \"duration\": 238.09}", "aliases": ["Justice Beat"], "size": "4.17 MB"}, {"id": "smoked-up-35", "name": "Smoked Up [V4]", "artists": [], "producers": ["Justice"], "notes": "Fourth freestyle. Initially thought to be from Good Ass Job, later found to originate from 808s & Heartbreak era. Has a hard electronic sound, stemming from the Justice production. Mostly mumble, at one point <PERSON><PERSON><PERSON> references <PERSON>. Leaked after a successful Soakbuy. This and all previous versions were intended to have additional production from <PERSON> and <PERSON> alongside the Justice produced beat, although those tracks are empty in the session.", "length": "238.24", "fileDate": 17403552, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/6397f0de834c381350744cabfcfe2d8f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6397f0de834c381350744cabfcfe2d8f\", \"key\": \"Smoked Up\", \"title\": \"Smoked Up [V4]\", \"artists\": \"(prod. <PERSON>)\", \"aliases\": [\"Justice Beat\"], \"description\": \"Fourth freestyle. Initially thought to be from Good Ass Job, later found to originate from 808s & Heartbreak era. Has a hard electronic sound, stemming from the Justice production. Mostly mumble, at one point <PERSON><PERSON><PERSON> references <PERSON>. Leaked after a successful Soakbuy. This and all previous versions were intended to have additional production from <PERSON> and <PERSON> alongside the Justice produced beat, although those tracks are empty in the session.\", \"date\": 17403552, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b739ed840ceb273fce149bccb36b3fe4\", \"url\": \"https://api.pillowcase.su/api/download/b739ed840ceb273fce149bccb36b3fe4\", \"size\": \"4.18 MB\", \"duration\": 238.24}", "aliases": ["Justice Beat"], "size": "4.18 MB"}, {"id": "smoked-up-36", "name": "Smoked Up [V5]", "artists": [], "producers": ["Justice"], "notes": "Latest version found in the ProTools sessions. Has the same freestyle as the fourth version however the vocals are mixed differently.", "length": "238.24", "fileDate": 17403552, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/ca1a11e77c20cedc1f28432e4f3df8b1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ca1a11e77c20cedc1f28432e4f3df8b1\", \"key\": \"Smoked Up\", \"title\": \"Smoked Up [V5]\", \"artists\": \"(prod. Justice)\", \"aliases\": [\"Justice Beat\"], \"description\": \"Latest version found in the ProTools sessions. Has the same freestyle as the fourth version however the vocals are mixed differently.\", \"date\": 17403552, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"fbdf0d2ff71f709d18a8161e59bafe96\", \"url\": \"https://api.pillowcase.su/api/download/fbdf0d2ff71f709d18a8161e59bafe96\", \"size\": \"4.18 MB\", \"duration\": 238.24}", "aliases": ["Justice Beat"], "size": "4.18 MB"}, {"id": "streetlights", "name": "Streetlights [V1]", "artists": [], "producers": [], "notes": "OG Filename: STreetlights.1_RUFF\nEarly solo version of \"Streetlights\" of <PERSON><PERSON><PERSON>'s finished verse.", "length": "100.32", "fileDate": 17076960, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/964d8c1816bc25236cb6c251eebd11fd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/964d8c1816bc25236cb6c251eebd11fd\", \"key\": \"Streetlights\", \"title\": \"Streetlights [V1]\", \"aliases\": [\"Street Lights\"], \"description\": \"OG Filename: STreetlights.1_RUFF\\nEarly solo version of \\\"Streetlights\\\" of <PERSON><PERSON><PERSON>'s finished verse.\", \"date\": 17076960, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4485bb3f61a46232a8b1ce57d80000dd\", \"url\": \"https://api.pillowcase.su/api/download/4485bb3f61a46232a8b1ce57d80000dd\", \"size\": \"1.97 MB\", \"duration\": 100.32}", "aliases": ["Street Lights"], "size": "1.97 MB"}, {"id": "street-lights", "name": "Street Lights [V2]", "artists": ["The WRLDFMS <PERSON>", "<PERSON><PERSON>", "Mr <PERSON>"], "producers": ["Mr <PERSON>", "Kanye West", "<PERSON>"], "notes": "Music video version of the song, contains a different mix, less autotune and additional <PERSON> vocals.", "length": "194.69", "fileDate": 16128288, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/2cc42e341a40121f942c588be7285db1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2cc42e341a40121f942c588be7285db1\", \"key\": \"Street Lights\", \"title\": \"Street Lights [V2]\", \"artists\": \"(feat. The WRLDFMS <PERSON>, <PERSON><PERSON> & <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Music video version of the song, contains a different mix, less autotune and additional <PERSON> vocals.\", \"date\": 16128288, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"45b47a10d7fa26949b2c172b0c705033\", \"url\": \"https://api.pillowcase.su/api/download/45b47a10d7fa26949b2c172b0c705033\", \"size\": \"3.48 MB\", \"duration\": 194.69}", "aliases": [], "size": "3.48 MB"}, {"id": "takin-off", "name": "Takin' Off [V1]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "According to <PERSON>, his collaboration with <PERSON><PERSON><PERSON>, \"Supernova\", was originally <PERSON><PERSON><PERSON>'s song. Confirmed to have been titled \"Takin Off\" and to be unfinished.", "length": "273.9", "fileDate": 16992288, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/03e5e961ef8aac69f3d28524cea0ce17", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/03e5e961ef8aac69f3d28524cea0ce17\", \"key\": \"Takin' Off\", \"title\": \"Takin' Off [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Supernova\"], \"description\": \"According to <PERSON>, his collaboration with <PERSON><PERSON><PERSON>, \\\"Supernova\\\", was originally <PERSON><PERSON><PERSON>'s song. Confirmed to have been titled \\\"Takin Off\\\" and to be unfinished.\", \"date\": 16992288, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"63ea61fdc210d94a13aba602bac9936f\", \"url\": \"https://api.pillowcase.su/api/download/63ea61fdc210d94a13aba602bac9936f\", \"size\": \"4.75 MB\", \"duration\": 273.9}", "aliases": ["Supernova"], "size": "4.75 MB"}, {"id": "supernova", "name": "Supernova [V2]", "artists": [], "producers": ["Kanye West", "<PERSON>"], "notes": "OG Filename: SUPERNOVA RUFF W Ye REF\nVersion of \"Supernova\" that is further along than the previous version, with a slightly more fleshed-out hook idea. Has open near the end. Leaked after a groupbuy.", "length": "273.85", "fileDate": 17413920, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/cb1e8e335f27bfa582f3065482828915", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cb1e8e335f27bfa582f3065482828915\", \"key\": \"Supernova\", \"title\": \"Supernova [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Takin' Off\"], \"description\": \"OG Filename: SUPERNOVA RUFF W Ye REF\\nVersion of \\\"Supernova\\\" that is further along than the previous version, with a slightly more fleshed-out hook idea. Has open near the end. Leaked after a groupbuy.\", \"date\": 17413920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8761f4ac41b390bf026a75f18aea83fb\", \"url\": \"https://api.pillowcase.su/api/download/8761f4ac41b390bf026a75f18aea83fb\", \"size\": \"4.75 MB\", \"duration\": 273.85}", "aliases": ["Takin' Off"], "size": "4.75 MB"}, {"id": "takin-off-41", "name": "Takin' Off [V3]", "artists": ["Mr <PERSON>"], "producers": ["Kanye West", "<PERSON>"], "notes": "Has someone (most likely <PERSON><PERSON><PERSON>) doing vocals as a reference or feature. Unknown if <PERSON> is a feature or it was his song. Snippet leaked May 31st, 2024.", "length": "18", "fileDate": 17171136, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/b95d4803788cc94c1791c840b6377ad0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b95d4803788cc94c1791c840b6377ad0\", \"key\": \"Takin' Off\", \"title\": \"Takin' Off [V3]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Supernova\"], \"description\": \"Has someone (most likely <PERSON><PERSON><PERSON>) doing vocals as a reference or feature. Unknown if <PERSON> is a feature or it was his song. Snippet leaked May 31st, 2024.\", \"date\": 17171136, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"24451be72a345db95cdd45433cdf9982\", \"url\": \"https://api.pillowcase.su/api/download/24451be72a345db95cdd45433cdf9982\", \"size\": \"652 kB\", \"duration\": 18}", "aliases": ["Supernova"], "size": "652 kB"}, {"id": "tell-everybody-that-you-know", "name": "Tell Everybody That You Know [V2]", "artists": [], "producers": ["No I.D.", "Kanye West", "<PERSON>"], "notes": "OG Filename: TELL EVERYBODY RUFF WITH YE\nEarly mumble version of \"See You In My Nightmares\", with just the hook idea present. Leaked after a groupbuy.", "length": "281.22", "fileDate": 17413920, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/2435c83ded140f9d43f2844451228810", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2435c83ded140f9d43f2844451228810\", \"key\": \"Tell Everybody That You Know\", \"title\": \"Tell Everybody That You Know [V2]\", \"artists\": \"(prod. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"See You In My Nightmares\"], \"description\": \"OG Filename: TELL EVERYBODY RUFF WITH YE\\nEarly mumble version of \\\"See You In My Nightmares\\\", with just the hook idea present. Leaked after a groupbuy.\", \"date\": 17413920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"85c4216f18841158595ce471eafb57df\", \"url\": \"https://api.pillowcase.su/api/download/85c4216f18841158595ce471eafb57df\", \"size\": \"4.86 MB\", \"duration\": 281.22}", "aliases": ["See You In My Nightmares"], "size": "4.86 MB"}, {"id": "tell-everybody-that-you-know-43", "name": "Tell Everybody That You Know [V3]", "artists": ["<PERSON>"], "producers": ["No I.D.", "Kanye West", "<PERSON>"], "notes": "Version of \"Tell Everybody That You Know\" that only repeats the final note once.", "length": "260.94", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/e17c3724be1c8087727fd546abc74565", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e17c3724be1c8087727fd546abc74565\", \"key\": \"Tell Everybody That You Know\", \"title\": \"Tell Everybody That You Know [V3]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"See You In My Nightmares\"], \"description\": \"Version of \\\"Tell Everybody That You Know\\\" that only repeats the final note once.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"a637988ee650df6cb214efbcdb490f16\", \"url\": \"https://api.pillowcase.su/api/download/a637988ee650df6cb214efbcdb490f16\", \"size\": \"4.54 MB\", \"duration\": 260.94}", "aliases": ["See You In My Nightmares"], "size": "4.54 MB"}, {"id": "tell-everybody-that-you-know-44", "name": "Tell Everybody That You Know [V4]", "artists": ["<PERSON>"], "producers": ["No I.D.", "Kanye West", "<PERSON>"], "notes": "OG Filename: Tell -Everybody-That-You-Know-Final\nSeemingly the version that is released, but <PERSON> is not censored. The mixing and mastering is also not the same as released.", "length": "259.37", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/ec8e81e12c6b6dbd36ee1fdef03d2dd7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ec8e81e12c6b6dbd36ee1fdef03d2dd7\", \"key\": \"Tell Everybody That You Know\", \"title\": \"Tell Everybody That You Know [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"See You In My Nightmares\"], \"description\": \"OG Filename: Tell -Everybody-That-You-Know-Final\\nSeemingly the version that is released, but <PERSON> is not censored. The mixing and mastering is also not the same as released.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7c071e8394fa4b26d3b1490b31329564\", \"url\": \"https://api.pillowcase.su/api/download/7c071e8394fa4b26d3b1490b31329564\", \"size\": \"4.51 MB\", \"duration\": 259.37}", "aliases": ["See You In My Nightmares"], "size": "4.51 MB"}, {"id": "welcome-to-heartbreak", "name": "Welcome To Heartbreak [V2]", "artists": ["T-Pain"], "producers": ["Kanye West", "<PERSON>", "Plain Pat"], "notes": "Early version of \"Welcome To Heartbreak\" with chorus vocals from <PERSON><PERSON><PERSON>. Snippet leaked May 31st, 2024.", "length": "8.5", "fileDate": 17171136, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/c9c49a21b09e0d8fe9628672f3274444", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c9c49a21b09e0d8fe9628672f3274444\", \"key\": \"Welcome To Heartbreak\", \"title\": \"Welcome To Heartbreak [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON> Pat)\", \"description\": \"Early version of \\\"Welcome To Heartbreak\\\" with chorus vocals from <PERSON><PERSON><PERSON>. Snippet leaked May 31st, 2024.\", \"date\": 17171136, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2c78c7754e662604418f01c0ecc47f12\", \"url\": \"https://api.pillowcase.su/api/download/2c78c7754e662604418f01c0ecc47f12\", \"size\": \"500 kB\", \"duration\": 8.5}", "aliases": [], "size": "500 kB"}, {"id": "cookin", "name": "Cookin' [V3]", "artists": ["Mr. <PERSON>"], "producers": ["Kanye West", "<PERSON>", "Plain Pat"], "notes": "OG Filename: Cookin' V2.2 Ruff mix\nEarly version of \"Wecome To Heartbreak\" with Mr<PERSON> replacing what would later be <PERSON>'s part. Missing the verse where <PERSON><PERSON><PERSON> actually says \"welcome to heartbreak\". Leaked after a groupbuy.", "length": "248.75", "fileDate": 17413920, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/33cc4722ef3165fa563cbe65f265babb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/33cc4722ef3165fa563cbe65f265babb\", \"key\": \"Cookin'\", \"title\": \"Cookin' [V3]\", \"artists\": \"(feat. Mr. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON>)\", \"aliases\": [\"Welcome To Heartbreak\"], \"description\": \"OG Filename: Cookin' V2.2 Ruff mix\\nEarly version of \\\"Wecome To Heartbreak\\\" with Mr<PERSON> replacing what would later be <PERSON>'s part. Missing the verse where <PERSON><PERSON><PERSON> actually says \\\"welcome to heartbreak\\\". Leaked after a groupbuy.\", \"date\": 17413920, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"1ecdcbca049636e30d462a036a0cebbd\", \"url\": \"https://api.pillowcase.su/api/download/1ecdcbca049636e30d462a036a0cebbd\", \"size\": \"4.34 MB\", \"duration\": 248.75}", "aliases": ["Welcome To Heartbreak"], "size": "4.34 MB"}, {"id": "welcome-to-heartbreak-47", "name": "Welcome To Heartbreak [V4]", "artists": ["<PERSON>"], "producers": ["Kanye West", "<PERSON>", "Plain Pat"], "notes": "Multiple beat differences, including the synths replacing the piano\nand different drums.", "length": "262.24", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/f1e10406c7e778351e58e832c47bf18d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f1e10406c7e778351e58e832c47bf18d\", \"key\": \"Welcome To Heartbreak\", \"title\": \"Welcome To Heartbreak [V4]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>, <PERSON> & <PERSON> Pat)\", \"description\": \"Multiple beat differences, including the synths replacing the piano\\nand different drums.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"49b02f0689a1ffacd780bd07f5d6e9bf\", \"url\": \"https://api.pillowcase.su/api/download/49b02f0689a1ffacd780bd07f5d6e9bf\", \"size\": \"4.56 MB\", \"duration\": 262.24}", "aliases": [], "size": "4.56 MB"}, {"id": "what-it-is", "name": "What It Is", "artists": [], "producers": ["T-Pain"], "notes": "OG Filename: What It is <PERSON>uff Comp girls and ye NO AT\nDemo with an unfinished second verse, was likely made during the 808s & Heartbreak sessions and later given to <PERSON>", "length": "176.78", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/c3694323e7001af2486b42c1e2c642d8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c3694323e7001af2486b42c1e2c642d8\", \"key\": \"What It Is\", \"title\": \"What It Is\", \"artists\": \"(prod. T-<PERSON>)\", \"description\": \"OG Filename: What It is Ruff Comp girls and ye NO AT\\nDemo with an unfinished second verse, was likely made during the 808s & Heartbreak sessions and later given to <PERSON>\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ada7e372bf87833ed98aab1115c6ce7e\", \"url\": \"https://api.pillowcase.su/api/download/ada7e372bf87833ed98aab1115c6ce7e\", \"size\": \"3.19 MB\", \"duration\": 176.78}", "aliases": [], "size": "3.19 MB"}, {"id": "grammy-family", "name": "Consequence - Grammy Family (Remix)", "artists": ["<PERSON>"], "producers": ["Kanye West"], "notes": "Seemingly unreleased remix of \"Grammy Family\" with verses from <PERSON>. Likely a remix and not a prior version as <PERSON> had only begun working with <PERSON><PERSON><PERSON> during the sessions for 808s & Heartbreak. Snippet leaked January 2nd, 2025.", "length": "9.14", "fileDate": 17357760, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/e090efd1a960d7532d1a76932efc49ff", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e090efd1a960d7532d1a76932efc49ff\", \"key\": \"Grammy Family (Remix)\", \"title\": \"Consequence - Grammy Family (Remix)\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Seemingly unreleased remix of \\\"Grammy Family\\\" with verses from <PERSON>. Likely a remix and not a prior version as <PERSON> had only begun working with <PERSON><PERSON><PERSON> during the sessions for 808s & Heartbreak. Snippet leaked January 2nd, 2025.\", \"date\": 17357760, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"89da92e7c41856ea8848b803c4b6c7e6\", \"url\": \"https://api.pillowcase.su/api/download/89da92e7c41856ea8848b803c4b6c7e6\", \"size\": \"510 kB\", \"duration\": 9.14}", "aliases": [], "size": "510 kB"}, {"id": "arms-race", "name": "Fall Out Boy - Arms Race (Remix)", "artists": ["Kanye West", "The WRLDFMS <PERSON>"], "producers": ["Fall Out Boy", "<PERSON>"], "notes": "OG Filename: <PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Verse_2.12.08-M3NT_04-04\nSimilar to the released version, but has an alternate mix.", "length": "244.12", "fileDate": "", "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/a5eeef7ee8fc59a5d17fb3287db3759f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a5eeef7ee8fc59a5d17fb3287db3759f\", \"key\": \"Arms Race (Remix)\", \"title\": \"Fall Out Boy - Arms Race (Remix)\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & The WRLDFMS <PERSON>) (prod. Fall Out Boy & <PERSON>)\", \"aliases\": [\"This Ain't A Scene\", \"It's An Arms Race\"], \"description\": \"OG Filename: Arms_Race_RMX_Kanye_Verse_2.12.08-M3NT_04-04\\nSimilar to the released version, but has an alternate mix.\", \"date\": null, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"fc7ed7bd8f5357d7a35af71c8f2ea284\", \"url\": \"https://api.pillowcase.su/api/download/fc7ed7bd8f5357d7a35af71c8f2ea284\", \"size\": \"5.96 MB\", \"duration\": 244.12}", "aliases": ["This Ain't A Scene", "It's An Arms Race"], "size": "5.96 MB"}, {"id": "bring-it-back", "name": "<PERSON><PERSON><PERSON> Offishall - Bring It Back [V1]", "artists": [], "producers": ["Boi-1da"], "notes": "\"Forever\" was originally a Kardinal Offishall song with Rock City in early 2008.", "length": "232.01", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "Low Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/7fecc4a4585898235147dd2ff6c10b28", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7fecc4a4585898235147dd2ff6c10b28\", \"key\": \"Bring It Back\", \"title\": \"Kardinal Offishall - Bring It Back [V1]\", \"artists\": \"(prod. Boi-1da)\", \"aliases\": [\"Forever\", \"I Want This Forever\"], \"description\": \"\\\"Forever\\\" was originally a Kardinal Offishall song with Rock City in early 2008.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"4073d168607d629e8d77cadb4dec4641\", \"url\": \"https://api.pillowcase.su/api/download/4073d168607d629e8d77cadb4dec4641\", \"size\": \"4.08 MB\", \"duration\": 232.01}", "aliases": ["Forever", "I Want This Forever"], "size": "4.08 MB"}, {"id": "forever", "name": "Ka<PERSON>nal Offishall - Forever [V2]", "artists": ["<PERSON>"], "producers": ["Boi-1da"], "notes": "\"Forever\" was originally a Kardinal Offishall song with <PERSON> in early 2008. Has no verse from <PERSON>, a different line in the hook, and a very different sounding hook. Was probably given to <PERSON> for either So Far Gone or the More Than A Game soundtrack.", "length": "225.82", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/7cc4c4661c2b3a6272ce631fafb8d4e0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7cc4c4661c2b3a6272ce631fafb8d4e0\", \"key\": \"Forever\", \"title\": \"Kardinal Offishall - Forever [V2]\", \"artists\": \"(feat. <PERSON>) (prod. Boi-1da)\", \"aliases\": [\"Bring It Back\", \"I Want This Forever\"], \"description\": \"\\\"Forever\\\" was originally a Kardinal Offishall song with <PERSON> in early 2008. Has no verse from <PERSON>, a different line in the hook, and a very different sounding hook. Was probably given to <PERSON> for either So Far Gone or the More Than A Game soundtrack.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"c8495c37618da2ae19fee5c816429d8c\", \"url\": \"https://api.pillowcase.su/api/download/c8495c37618da2ae19fee5c816429d8c\", \"size\": \"3.98 MB\", \"duration\": 225.82}", "aliases": ["Bring It Back", "I Want This Forever"], "size": "3.98 MB"}, {"id": "forever-53", "name": "Drake - Forever [V3]", "artists": ["<PERSON>", "<PERSON><PERSON>"], "producers": ["Boi-1da"], "notes": "Has a different vocal take from <PERSON>, different verses from <PERSON> and\n <PERSON>, differences in the instrumental and a verse from <PERSON><PERSON>.", "length": "274.52", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://pillowcase.su/f/2dca82d5d6f71b55a6e024ff5d2934d8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2dca82d5d6f71b55a6e024ff5d2934d8\", \"key\": \"Forever\", \"title\": \"Drake - Forever [V3]\", \"artists\": \"(feat. <PERSON>) (prod. Boi-1da)\", \"aliases\": [\"Bring It Back\", \"I Want This Forever\"], \"description\": \"Has a different vocal take from <PERSON>, different verses from <PERSON> and\\n <PERSON>, differences in the instrumental and a verse from <PERSON><PERSON>.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"7dacb5f3e3c7e9cb3386e0628b3001ba\", \"url\": \"https://api.pillowcase.su/api/download/7dacb5f3e3c7e9cb3386e0628b3001ba\", \"size\": \"4.76 MB\", \"duration\": 274.52}", "aliases": ["Bring It Back", "I Want This Forever"], "size": "4.76 MB"}, {"id": "everybody-nose", "name": "N.E.R.D. - Everybody Nose (All The Girls Standing In The Line For The Bathroom) (Remix) ", "artists": ["Kanye West", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "producers": ["The Neptunes"], "notes": "Earlier version of the \"Everybody Nose (Remix)\", shown in a recording of the session by <PERSON><PERSON><PERSON>. The \"on fire\" line is missing from the chorus and the synth is missing from <PERSON><PERSON><PERSON>'s verse.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "808s-heartbreak", "originalUrl": "https://www.youtube.com/watch?v=2ZY-8Botk28&ab_channel=ConstotheQuence", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.youtube.com/watch?v=2ZY-8Botk28&ab_channel=ConstotheQuence\", \"key\": \"Everybody Nose (All The Girls Standing In The Line For The Bathroom) (Remix)\", \"title\": \"N.E.R.D. - Everybody Nose (All The Girls Standing In The Line For The Bathroom) (Remix) \", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON><PERSON> & <PERSON>ush<PERSON>) (prod. The Neptunes)\", \"description\": \"Earlier version of the \\\"Everybody Nose (Remix)\\\", shown in a recording of the session by Consequence. The \\\"on fire\\\" line is missing from the chorus and the synth is missing from <PERSON><PERSON><PERSON>'s verse.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}]}