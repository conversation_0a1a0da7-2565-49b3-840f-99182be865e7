{"id": "watch-the-throne", "name": "Watch The Throne", "description": "Considered one of the most legendary collab albums of all time, Watch the Throne puts together two of the most legendary figures in music history for a full studio album. <PERSON><PERSON><PERSON> teams up with his big brother, <PERSON><PERSON><PERSON><PERSON><PERSON>, for an album, focused primarily on luxury, black excellence, and the American dream. The album's production also reflects that, and having been recorded by two future billionaires primarily in New York City's Tribeca Grand Hotel, how could it not?", "backgroundColor": "rgb(132, 108, 71)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17FeLUT_PMgXiQgR8wLPI516bqmRdh7BEbXlPM-wP7HnL5TV1YzuVMRCTf39taaKL1CRjbDBYYoE5OmKeOoNRu_nz1KiRZJ_AH75jU7dWX2Xsk56I0wcgdeCpcvyPdV-_R0ITKYs6WVgfe7Qcj8?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "all-the-time", "name": "All The Time [V4]", "artists": ["<PERSON>"], "producers": ["No I.D."], "notes": "OG Filename: All the Time SSL RUFF\nVersion, containing only a <PERSON><PERSON><PERSON> hook and open verses. Features <PERSON> doing backing vocals in the hook.", "length": "221.15", "fileDate": 16731360, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/da4b9c6173676dc0c4c6a1bb936483d1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/da4b9c6173676dc0c4c6a1bb936483d1\", \"key\": \"All The Time\", \"title\": \"All The Time [V4]\", \"artists\": \"(feat. <PERSON>) (prod. No I.D.)\", \"aliases\": [\"She's Like That All The Time\"], \"description\": \"OG Filename: All the Time SSL RUFF\\nVersion, containing only a Kany<PERSON> hook and open verses. Features <PERSON> doing backing vocals in the hook.\", \"date\": 16731360, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"37a370081805cad1d985d914bd1688d1\", \"url\": \"https://api.pillowcase.su/api/download/37a370081805cad1d985d914bd1688d1\", \"size\": \"8.39 MB\", \"duration\": 221.15}", "aliases": ["She's Like That All The Time"], "size": "8.39 MB"}, {"id": "interlude", "name": "Interlude", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: KL Interlude 30 seconds Mono-14\nOG file of the interlude that plays through the album. Bounced after the album was released, possibly for tour use.", "length": "37.72", "fileDate": 16731360, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/a3f2cf18c5afed22e1fd3b64f2bea49b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/a3f2cf18c5afed22e1fd3b64f2bea49b\", \"key\": \"Interlude\", \"title\": \"Interlude\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: KL Interlude 30 seconds Mono-14\\nOG file of the interlude that plays through the album. Bounced after the album was released, possibly for tour use.\", \"date\": 16731360, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b88c084a9e18680f636cfa08b61d99bb\", \"url\": \"https://api.pillowcase.su/api/download/b88c084a9e18680f636cfa08b61d99bb\", \"size\": \"5.45 MB\", \"duration\": 37.72}", "aliases": [], "size": "5.45 MB"}, {"id": "living-so-italian", "name": "🏆 Living So Italian", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Originally the 4th track on Watch The Throne, which sampled \"Por Ti Volare\" by <PERSON>, but the sample could not be cleared, so it was cut. Includes some mumble Kany<PERSON> vocals. Has the unheard line, \"I'm about to say something crazy / <PERSON>.\" One of the holy grails of unreleased Kanye tracks. The beat was given to Lil B. LQ snippet was posted in May 2019. Three more LQ snippets of <PERSON><PERSON><PERSON>'s verse leaked and then the full verse leaked in LQ on November 13th, 2023. <PERSON>'s verse was later reused on \"Fuckwitmeyouknowigotit\".", "length": "84.82", "fileDate": 16998336, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/9240ae05e89f306945273624580bf725", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9240ae05e89f306945273624580bf725\", \"key\": \"Living So Italian\", \"title\": \"\\ud83c\\udfc6 Living So Italian\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"Italian Living\", \"Like The Mob\", \"So Italian\"], \"description\": \"Originally the 4th track on Watch The Throne, which sampled \\\"Por Ti Volare\\\" by <PERSON>, but the sample could not be cleared, so it was cut. Includes some mumble <PERSON><PERSON><PERSON> vocals. Has the unheard line, \\\"I'm about to say something crazy / <PERSON>.\\\" One of the holy grails of unreleased Kanye tracks. The beat was given to <PERSON>. <PERSON> snippet was posted in May 2019. Three more LQ snippets of <PERSON><PERSON><PERSON>'s verse leaked and then the full verse leaked in LQ on November 13th, 2023. <PERSON>'s verse was later reused on \\\"Fuckwitmeyouknowigotit\\\".\", \"date\": 16998336, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"3136d90c67b5cd5ab15139f97f57e838\", \"url\": \"https://api.pillowcase.su/api/download/3136d90c67b5cd5ab15139f97f57e838\", \"size\": \"6.21 MB\", \"duration\": 84.82}", "aliases": ["Italian Living", "Like The Mob", "So Italian"], "size": "6.21 MB"}, {"id": "living-so-italian-4", "name": "🏆 Living So Italian", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Originally the 4th track on Watch The Throne, which sampled \"Por Ti Volare\" by <PERSON>, but the sample could not be cleared, so it was cut. Includes some mumble Kany<PERSON> vocals. Has the unheard line, \"I'm about to say something crazy / <PERSON>.\" One of the holy grails of unreleased Kanye tracks. The beat was given to Lil B. LQ snippet was posted in May 2019. Three more LQ snippets of <PERSON><PERSON><PERSON>'s verse leaked and then the full verse leaked in LQ on November 13th, 2023. <PERSON>'s verse was later reused on \"Fuckwitmeyouknowigotit\".", "length": "63.72", "fileDate": 16998336, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/4040f610195e58c1c48caac2cc8b8f90", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4040f610195e58c1c48caac2cc8b8f90\", \"key\": \"Living So Italian\", \"title\": \"\\ud83c\\udfc6 Living So Italian\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"Italian Living\", \"Like The Mob\", \"So Italian\"], \"description\": \"Originally the 4th track on Watch The Throne, which sampled \\\"Por Ti Volare\\\" by <PERSON>, but the sample could not be cleared, so it was cut. Includes some mumble <PERSON><PERSON><PERSON> vocals. Has the unheard line, \\\"I'm about to say something crazy / <PERSON>.\\\" One of the holy grails of unreleased Kanye tracks. The beat was given to <PERSON> snippet was posted in May 2019. Three more LQ snippets of <PERSON><PERSON><PERSON>'s verse leaked and then the full verse leaked in LQ on November 13th, 2023. <PERSON>'s verse was later reused on \\\"Fuckwitmeyouknowigotit\\\".\", \"date\": 16998336, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"462943cc9697a45036406978f56c4968\", \"url\": \"https://api.pillowcase.su/api/download/462943cc9697a45036406978f56c4968\", \"size\": \"5.36 MB\", \"duration\": 63.72}", "aliases": ["Italian Living", "Like The Mob", "So Italian"], "size": "5.36 MB"}, {"id": "living-so-italian-5", "name": "🏆 Living So Italian", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Originally the 4th track on Watch The Throne, which sampled \"Por Ti Volare\" by <PERSON>, but the sample could not be cleared, so it was cut. Includes some mumble Kany<PERSON> vocals. Has the unheard line, \"I'm about to say something crazy / <PERSON>.\" One of the holy grails of unreleased Kanye tracks. The beat was given to Lil B. LQ snippet was posted in May 2019. Three more LQ snippets of <PERSON><PERSON><PERSON>'s verse leaked and then the full verse leaked in LQ on November 13th, 2023. <PERSON>'s verse was later reused on \"Fuckwitmeyouknowigotit\".", "length": "1.9300000000000002", "fileDate": 16998336, "leakDate": "", "availableLength": "Partial", "quality": "Low Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/7a7ecf79bdd9e58561de3c6e59c3334c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7a7ecf79bdd9e58561de3c6e59c3334c\", \"key\": \"Living So Italian\", \"title\": \"\\ud83c\\udfc6 Living So Italian\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"aliases\": [\"Italian Living\", \"Like The Mob\", \"So Italian\"], \"description\": \"Originally the 4th track on Watch The Throne, which sampled \\\"Por Ti Volare\\\" by <PERSON>, but the sample could not be cleared, so it was cut. Includes some mumble <PERSON><PERSON><PERSON> vocals. Has the unheard line, \\\"I'm about to say something crazy / <PERSON>.\\\" One of the holy grails of unreleased Kanye tracks. The beat was given to <PERSON> snippet was posted in May 2019. Three more LQ snippets of <PERSON><PERSON><PERSON>'s verse leaked and then the full verse leaked in LQ on November 13th, 2023. <PERSON>'s verse was later reused on \\\"Fuckwitmeyouknowigotit\\\".\", \"date\": 16998336, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"4a9f97a970e519d0317183714277d0d8\", \"url\": \"https://api.pillowcase.su/api/download/4a9f97a970e519d0317183714277d0d8\", \"size\": \"4.88 MB\", \"duration\": 1.9300000000000002}", "aliases": ["Italian Living", "Like The Mob", "So Italian"], "size": "4.88 MB"}, {"id": "mannequin", "name": "Mannequin", "artists": ["<PERSON>"], "producers": [], "notes": "Reference made for Watch The Throne by <PERSON><PERSON><PERSON>. Originally thought to be <PERSON><PERSON><PERSON> and for My Beautiful Dark Twisted Fantasy. Was being sold for $500. Unknown if Kanye version exists.", "length": "19.8", "fileDate": 16083360, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/86b3f2eae975a93a384a0acdab97def4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/86b3f2eae975a93a384a0acdab97def4\", \"key\": \"Manne<PERSON>\", \"title\": \"Mann<PERSON><PERSON>\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON>)\", \"description\": \"Reference made for Watch The Throne by <PERSON><PERSON><PERSON>. Originally thought to be <PERSON><PERSON><PERSON> and for My Beautiful Dark Twisted Fantasy. Was being sold for $500. Unknown if Kanye version exists.\", \"date\": 16083360, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"fa647eaba131770e64ea5b809b7d8ee0\", \"url\": \"https://api.pillowcase.su/api/download/fa647eaba131770e64ea5b809b7d8ee0\", \"size\": \"5.17 MB\", \"duration\": 19.8}", "aliases": [], "size": "5.17 MB"}, {"id": "niggas-in-paris", "name": "Niggas In Paris [V4]", "artists": [], "producers": ["MIKE DEAN", "Hit-Boy", "<PERSON>"], "notes": "OG Filename: KW 2_01 & KW VOX Verse End\nIn his \"Mix With The Masters\" episode, <PERSON> previewed a mix of \"Niggas In Paris\" with a slightly different vocal take from <PERSON>. \"Look, you need to crawl 'fore you ball\" is instead \"Bitch you need to crawl 'fore you ball\", and the \"going gorillas\" line is replaced with \"take a [???] head that got me aching from <PERSON><PERSON>\". Both vocal stem filenames were shown on screen.", "length": "7.34", "fileDate": 17311968, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/7d6c94e8b8b42aacf312779d937e7f68", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7d6c94e8b8b42aacf312779d937e7f68\", \"key\": \"Niggas In Paris\", \"title\": \"Niggas In Paris [V4]\", \"artists\": \"(prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: KW 2_01 & KW VOX Verse End\\nIn his \\\"Mix With The Masters\\\" episode, <PERSON> previewed a mix of \\\"Niggas In Paris\\\" with a slightly different vocal take from <PERSON>. \\\"Look, you need to crawl 'fore you ball\\\" is instead \\\"Bitch you need to crawl 'fore you ball\\\", and the \\\"going gorillas\\\" line is replaced with \\\"take a [???] head that got me aching from <PERSON><PERSON>\\\". Both vocal stem filenames were shown on screen.\", \"date\": 17311968, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5d53f70527d90d9634cd14020dc35a8e\", \"url\": \"https://api.pillowcase.su/api/download/5d53f70527d90d9634cd14020dc35a8e\", \"size\": \"4.97 MB\", \"duration\": 7.34}", "aliases": [], "size": "4.97 MB"}, {"id": "niggas-in-paris-8", "name": "Niggas In Paris [V4]", "artists": [], "producers": ["MIKE DEAN", "Hit-Boy", "<PERSON>"], "notes": "OG Filename: KW 2_01 & KW VOX Verse End\nIn his \"Mix With The Masters\" episode, <PERSON> previewed a mix of \"Niggas In Paris\" with a slightly different vocal take from <PERSON>. \"Look, you need to crawl 'fore you ball\" is instead \"Bitch you need to crawl 'fore you ball\", and the \"going gorillas\" line is replaced with \"take a [???] head that got me aching from <PERSON><PERSON>\". Both vocal stem filenames were shown on screen.", "length": "3.94", "fileDate": 17311968, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/ceaf2be16855506c290e4fa98b2da3ea", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ceaf2be16855506c290e4fa98b2da3ea\", \"key\": \"Niggas In Paris\", \"title\": \"Niggas In Paris [V4]\", \"artists\": \"(prod. <PERSON><PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: KW 2_01 & KW VOX Verse End\\nIn his \\\"Mix With The Masters\\\" episode, <PERSON> previewed a mix of \\\"Niggas In Paris\\\" with a slightly different vocal take from <PERSON>. \\\"Look, you need to crawl 'fore you ball\\\" is instead \\\"Bitch you need to crawl 'fore you ball\\\", and the \\\"going gorillas\\\" line is replaced with \\\"take a [???] head that got me aching from <PERSON><PERSON>\\\". Both vocal stem filenames were shown on screen.\", \"date\": 17311968, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"96de862507f30d3d71618b105cc72099\", \"url\": \"https://api.pillowcase.su/api/download/96de862507f30d3d71618b105cc72099\", \"size\": \"4.91 MB\", \"duration\": 3.94}", "aliases": [], "size": "4.91 MB"}, {"id": "niggas-in-paris-9", "name": "Niggas In Paris (Reprise)", "artists": [], "producers": [], "notes": "A cinematic style remix featured on an episode of Keeping Up With the Kardashians. No further evidence supports whether it is an edit made solely for the show or an unused remix. Community member <PERSON><PERSON><PERSON><PERSON><PERSON> created an almost perfect replica, which can be confused as a real version.", "length": "53.26", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/20bd208155d6c94fa91160ef33e0aa6a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/20bd208155d6c94fa91160ef33e0aa6a\", \"key\": \"Niggas In Paris (Reprise)\", \"title\": \"Niggas In Paris (Reprise)\", \"description\": \"A cinematic style remix featured on an episode of Keeping Up With the Kardashians. No further evidence supports whether it is an edit made solely for the show or an unused remix. Community member DJTroublesome created an almost perfect replica, which can be confused as a real version.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"d6c0fe081a2403bce41e878191205056\", \"url\": \"https://api.pillowcase.su/api/download/d6c0fe081a2403bce41e878191205056\", \"size\": \"5.7 MB\", \"duration\": 53.26}", "aliases": [], "size": "5.7 MB"}, {"id": "no-church", "name": "No Church [V2]", "artists": ["<PERSON>", "The-Dream"], "producers": ["88-<PERSON>", "Kanye West", "MIKE DEAN"], "notes": "OG Filename: B - No Church Voc Dn\nVersion with a different Ka<PERSON><PERSON> vocal take and two alternate lines towards the end of the verse, and one alternate line from The-Dream. Was called \"No Church\", right up until release.", "length": "244.41", "fileDate": 16765056, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/37a3a5b0d0f8ca4e13688ca15453c909", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/37a3a5b0d0f8ca4e13688ca15453c909\", \"key\": \"No Church\", \"title\": \"No Church [V2]\", \"artists\": \"(feat. <PERSON> & <PERSON>-<PERSON>) (prod. 88-<PERSON>, <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"No Church In The Wild\"], \"description\": \"OG Filename: B - No Church Voc Dn\\nVersion with a different Kanye vocal take and two alternate lines towards the end of the verse, and one alternate line from The-Dream. <PERSON> called \\\"No Church\\\", right up until release.\", \"date\": 16765056, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"dabc7a5bf7ca20989583b293b3e9312e\", \"url\": \"https://api.pillowcase.su/api/download/dabc7a5bf7ca20989583b293b3e9312e\", \"size\": \"8.76 MB\", \"duration\": 244.41}", "aliases": ["No Church In The Wild"], "size": "8.76 MB"}, {"id": "not-enough-time", "name": "<PERSON> - Not Enough Time [V1]", "artists": [], "producers": ["Hit-Boy"], "notes": "OG Filename: NotEnoughTime_Kanye Voc Ideas\nRough version, leaked before WTT dropped. Was originally a John Legend song, as confirmed in filenames from the session, which leaked in 2022.", "length": "162.25", "fileDate": 12990240, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/4d3118583c0912b242a06228b617b9bd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4d3118583c0912b242a06228b617b9bd\", \"key\": \"Not Enough Time\", \"title\": \"<PERSON> Legend - Not Enough Time [V1]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (prod. Hit-Boy)\", \"aliases\": [\"Whole Lifetime\"], \"description\": \"OG Filename: NotEnoughTime_Kanye Voc Ideas\\nRough version, leaked before WTT dropped. Was originally a John Legend song, as confirmed in filenames from the session, which leaked in 2022.\", \"date\": 12990240, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8ad73567a5ab6e80d8496a6e2fa322bb\", \"url\": \"https://api.pillowcase.su/api/download/8ad73567a5ab6e80d8496a6e2fa322bb\", \"size\": \"7.45 MB\", \"duration\": 162.25}", "aliases": ["Whole Lifetime"], "size": "7.45 MB"}, {"id": "whole-lifetime", "name": "🏆 Whole Lifetime [V2]", "artists": ["???"], "producers": ["Hit-Boy"], "notes": "Song listed on an early Watch The Throne tracklist given out by music insider blog \"The DOTR\". Nothing was known about the track until Insurge played Auger96 a significant portion of it during a livestream on August 26th 2024. Features an unknown female vocalist.", "length": "87.64", "fileDate": 17247168, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/272baeddfbd0703a3b2ab1e24aae3fa5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/272baeddfbd0703a3b2ab1e24aae3fa5\", \"key\": \"Whole Lifetime\", \"title\": \"\\ud83c\\udfc6 Whole Lifetime [V2]\", \"artists\": \"(feat. ???) (prod. Hit-Boy)\", \"aliases\": [\"Not Enough Time\"], \"description\": \"Song listed on an early Watch The Throne tracklist given out by music insider blog \\\"The DOTR\\\". Nothing was known about the track until Insurge played Auger96 a significant portion of it during a livestream on August 26th 2024. Features an unknown female vocalist.\", \"date\": 17247168, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f5167c2ea3c1eecb73475e4f88acbb56\", \"url\": \"https://api.pillowcase.su/api/download/f5167c2ea3c1eecb73475e4f88acbb56\", \"size\": \"6.25 MB\", \"duration\": 87.64}", "aliases": ["Not Enough Time"], "size": "6.25 MB"}, {"id": "not-the-same", "name": "Not The Same [V2] ", "artists": [], "producers": ["Honorable C.N.O.T.E."], "notes": "Version with no <PERSON> vocals.", "length": "248.9", "fileDate": 16886016, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/2d8a5bd4d4f3f35996ab29e37c2ac730", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2d8a5bd4d4f3f35996ab29e37c2ac730\", \"key\": \"Not The Same\", \"title\": \"Not The Same [V2] \", \"artists\": \"(prod. Honorable C.N.O.T.E.)\", \"aliases\": [\"Black Bruce Wayne\", \"We Are Not The Same\"], \"description\": \"Version with no <PERSON> vocals.\", \"date\": 16886016, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"305d72f9dcd7f029652b41bd0e9b39c5\", \"url\": \"https://api.pillowcase.su/api/download/305d72f9dcd7f029652b41bd0e9b39c5\", \"size\": \"8.83 MB\", \"duration\": 248.9}", "aliases": ["<PERSON>", "We Are Not The Same"], "size": "8.83 MB"}, {"id": "not-the-same-14", "name": "Not The Same [V3] ", "artists": ["Shire"], "producers": ["Honorable C.N.O.T.E."], "notes": "OG Filename: 8.Not_The_Same_for_<PERSON><PERSON>e_West_1 \nVersion from 2011. Features the artist <PERSON> singing hooks and backing vocals. Includes two open verses, co-written by <PERSON>. Filename indicates the song was meant for use by <PERSON><PERSON><PERSON>, possibly for WTT. Metadata also mentions \"Paragon Film Music\".", "length": "262.1", "fileDate": 16886016, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/8b65639fb6840891ac866695b164df32", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8b65639fb6840891ac866695b164df32\", \"key\": \"Not The Same\", \"title\": \"Not The Same [V3] \", \"artists\": \"(feat. <PERSON>) (prod. Honorable C.N.O.T.E.)\", \"aliases\": [\"Black Bruce Wayne\", \"We Are Not The Same\"], \"description\": \"OG Filename: 8.Not_The_Same_for_Kanye_West_1 \\nVersion from 2011. Features the artist <PERSON> singing hooks and backing vocals. Includes two open verses, co-written by <PERSON>. Filename indicates the song was meant for use by <PERSON><PERSON><PERSON>, possibly for WTT. Metadata also mentions \\\"Paragon Film Music\\\".\", \"date\": 16886016, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"b7c09fce3fd2e9ba1fac9dcc335ec544\", \"url\": \"https://api.pillowcase.su/api/download/b7c09fce3fd2e9ba1fac9dcc335ec544\", \"size\": \"9.04 MB\", \"duration\": 262.1}", "aliases": ["<PERSON>", "We Are Not The Same"], "size": "9.04 MB"}, {"id": "not-the-same-15", "name": "✨ Not The Same [V4] ", "artists": ["Shire"], "producers": ["Honorable C.N.O.T.E."], "notes": "OG Filename: not the same (gangsta) version\nAnother version, this time with different <PERSON> vocals.", "length": "256.03", "fileDate": 16886016, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/5bbafc401762d43e53ebeaed499bb185", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5bbafc401762d43e53ebeaed499bb185\", \"key\": \"Not The Same\", \"title\": \"\\u2728 Not The Same [V4] \", \"artists\": \"(feat. <PERSON>) (prod. Honorable C.N.O.T.E.)\", \"aliases\": [\"Black Bruce Wayne\", \"We Are Not The Same\"], \"description\": \"OG Filename: not the same (gangsta) version\\nAnother version, this time with different <PERSON> vocals.\", \"date\": 16886016, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"44e2e670c0686956b471a1962cb4f41f\", \"url\": \"https://api.pillowcase.su/api/download/44e2e670c0686956b471a1962cb4f41f\", \"size\": \"8.95 MB\", \"duration\": 256.03}", "aliases": ["<PERSON>", "We Are Not The Same"], "size": "8.95 MB"}, {"id": "not-the-same-16", "name": "Pizzle - Not The Same [V5] ", "artists": ["Kanye West"], "producers": ["Honorable C.N.O.T.E."], "notes": "Version made after the song was given to <PERSON><PERSON>.", "length": "46.47", "fileDate": 13998528, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/dc04c20ce0696ac48667ebf5ec2dfe07", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dc04c20ce0696ac48667ebf5ec2dfe07\", \"key\": \"Not The Same\", \"title\": \"Pizzle - Not The Same [V5] \", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Honorable C.N.O.T.E.)\", \"aliases\": [\"Black Bruce Wayne\", \"We Are Not The Same\"], \"description\": \"Version made after the song was given to <PERSON><PERSON>.\", \"date\": 13998528, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a908a11426b1e1bdcc1c323ddbf56ba0\", \"url\": \"https://api.pillowcase.su/api/download/a908a11426b1e1bdcc1c323ddbf56ba0\", \"size\": \"5.59 MB\", \"duration\": 46.47}", "aliases": ["<PERSON>", "We Are Not The Same"], "size": "5.59 MB"}, {"id": "otis", "name": "<PERSON> [V1]", "artists": [], "producers": [], "notes": "Video of <PERSON><PERSON><PERSON> recording his verse shows a version with mumble lyrics. CDQ mumble lyrics can be heard in the \"L.I.E.\" snippet, which can be found in Fakes.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/5c013662c9e065d481967bcc68b9af40", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5c013662c9e065d481967bcc68b9af40\", \"key\": \"<PERSON>\", \"title\": \"<PERSON> [V1]\", \"description\": \"Video of <PERSON><PERSON><PERSON> recording his verse shows a version with mumble lyrics. CDQ mumble lyrics can be heard in the \\\"L.I.E.\\\" snippet, which can be found in Fakes.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "runnin-through-my-dreams", "name": "Runnin Through My Dreams", "artists": [], "producers": [], "notes": "OG Filename: Runnin Through My Dreams_Kanye Voc Ideas\nSolo Kanye mumble demo. Leaked alongside the instrumental.", "length": "322.03", "fileDate": 16960320, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/b611f709d5434121397e31fa8de153bb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/b611f709d5434121397e31fa8de153bb\", \"key\": \"Runnin Through My Dreams\", \"title\": \"Runnin Through My Dreams\", \"description\": \"OG Filename: Runnin Through My Dreams_Kanye Voc Ideas\\nSolo Kanye mumble demo. Leaked alongside the instrumental.\", \"date\": 16960320, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c87162e5216527656543af429ed1f6ad\", \"url\": \"https://api.pillowcase.su/api/download/c87162e5216527656543af429ed1f6ad\", \"size\": \"10 MB\", \"duration\": 322.03}", "aliases": [], "size": "10 MB"}, {"id": "sky", "name": "Sky [V2]", "artists": ["<PERSON>", "T.I.", "The WRLDFMS <PERSON>"], "producers": [], "notes": "Watch The Throne-era throwaway. <PERSON>ks a Jay<PERSON><PERSON> verse. Samples \"Paperhouse\" by CAN. Unknown whether or not this is TI's song.", "length": "10.51", "fileDate": 17283456, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/6ec68a9feef054ec338f90516770d06c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6ec68a9feef054ec338f90516770d06c\", \"key\": \"Sky\", \"title\": \"Sky [V2]\", \"artists\": \"(feat. <PERSON>, <PERSON><PERSON><PERSON><PERSON> & The WRLDFMS <PERSON>)\", \"description\": \"Watch The Throne-era throwaway. Lacks a Jay-Z verse. Samples \\\"Paperhouse\\\" by CAN. Unknown whether or not this is TI's song.\", \"date\": 17283456, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"ad5245e17d8a8a2d415e244e74762ba5\", \"url\": \"https://api.pillowcase.su/api/download/ad5245e17d8a8a2d415e244e74762ba5\", \"size\": \"5.02 MB\", \"duration\": 10.51}", "aliases": [], "size": "5.02 MB"}, {"id": "why-i-love-you", "name": "Why I Love You [V1]", "artists": ["Mr <PERSON>"], "producers": ["Kanye West", "MIKE DEAN"], "notes": "Original version with an alternate intro and different lines in <PERSON><PERSON><PERSON><PERSON><PERSON>'s verse, \"For the trillionth time / like tourettes on it\".", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/fa6770159a8e308490b58d7e822024a9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fa6770159a8e308490b58d7e822024a9\", \"key\": \"Why I Love You\", \"title\": \"Why I Love You [V1]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON> & MIKE DEAN)\", \"aliases\": [\"Love You So\"], \"description\": \"Original version with an alternate intro and different lines in <PERSON><PERSON><PERSON><PERSON><PERSON>'s verse, \\\"For the trillionth time / like tourettes on it\\\".\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["Love You So"], "size": ""}, {"id": "why-i-love-you-21", "name": "Why I Love You [V2]", "artists": ["Mr <PERSON>"], "producers": ["Kanye West", "MIKE DEAN"], "notes": "OG Filename: WHY I LOVE YOU ZDAR MASTER MIX 01\nVersion with no <PERSON><PERSON><PERSON> vocals. <PERSON><PERSON><PERSON><PERSON><PERSON> does the the full part of what would later be replaced by him and <PERSON><PERSON><PERSON> going back-and-forth. Apart from that, it is very similar to release structure wise.", "length": "301.33", "fileDate": 17223840, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/14a0c149f87004646ae880796ebec4bd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/14a0c149f87004646ae880796ebec4bd\", \"key\": \"Why I Love You\", \"title\": \"Why I Love You [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>)\", \"aliases\": [\"Love You So\"], \"description\": \"OG Filename: WHY I LOVE YOU ZDAR MASTER MIX 01\\nVersion with no <PERSON><PERSON><PERSON> vocals. JA<PERSON>-Z does the the full part of what would later be replaced by him and <PERSON><PERSON><PERSON> going back-and-forth. Apart from that, it is very similar to release structure wise.\", \"date\": 17223840, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"932642992f6f9508530adb677545e317\", \"url\": \"https://api.pillowcase.su/api/download/932642992f6f9508530adb677545e317\", \"size\": \"9.67 MB\", \"duration\": 301.33}", "aliases": ["Love You So"], "size": "9.67 MB"}, {"id": "you-missed-it", "name": "You Missed It [V1]", "artists": [], "producers": ["Hit-Boy"], "notes": "OG Filename: YouMissed It_JL Voc Ideas\nJohn Legend reference track. Was later given to <PERSON>, and also used by <PERSON><PERSON> for \"Black Girl Lost Part 2\". Leaked alongside the session.", "length": "182.97", "fileDate": 16731360, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/6086da13066cd6808b7a016667da066f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6086da13066cd6808b7a016667da066f\", \"key\": \"<PERSON> Missed It\", \"title\": \"<PERSON> Missed It [V1]\", \"artists\": \"(ref. <PERSON>) (prod. Hit-Boy)\", \"aliases\": [\"I Know You Missed It\"], \"description\": \"OG Filename: YouMissed It_JL Voc Ideas\\nJohn Legend reference track. Was later given to <PERSON>, and also used by <PERSON><PERSON> for \\\"Black Girl Lost Part 2\\\". Leaked alongside the session.\", \"date\": 16731360, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"925dd4a1df96825c2a811fba6da501fd\", \"url\": \"https://api.pillowcase.su/api/download/925dd4a1df96825c2a811fba6da501fd\", \"size\": \"7.78 MB\", \"duration\": 182.97}", "aliases": ["I Know You Missed It"], "size": "7.78 MB"}, {"id": "", "name": "??? [V1]", "artists": [], "producers": [], "notes": "\"Can't Look In My Eyes\" is said to originate from the Watch The Throne sessions. Unclear if <PERSON> recorded his vocals during the sessions. Unknown what it was titled at the time. JAY-Z's vocals was reused on his and <PERSON><PERSON><PERSON><PERSON>'s song \"321 Scan\" found in the bleed.", "length": "29.62", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/d15eafa93a0cdce7f541b18af2df81b5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d15eafa93a0cdce7f541b18af2df81b5\", \"key\": \"???\", \"title\": \"??? [V1]\", \"aliases\": [\"Can't Look In My Eyes\", \"Shit On You\", \"Too Bad I Have To Destroy You Now\", \"Fuck\"], \"description\": \"\\\"Can't Look In My Eyes\\\" is said to originate from the Watch The Throne sessions. Unclear if <PERSON> recorded his vocals during the sessions. Unknown what it was titled at the time. <PERSON><PERSON><PERSON><PERSON><PERSON>'s vocals was reused on his and <PERSON><PERSON><PERSON>\\u0301's song \\\"321 Scan\\\" found in the bleed.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"90cb108da2323bfb06b96415aef56bb5\", \"url\": \"https://api.pillowcase.su/api/download/90cb108da2323bfb06b96415aef56bb5\", \"size\": \"5.32 MB\", \"duration\": 29.62}", "aliases": ["Can't Look In My Eyes", "Shit On You", "Too Bad I Have To Destroy You Now", "Fuck"], "size": "5.32 MB"}, {"id": "on-my-own", "name": "Consequence - On My Own", "artists": ["Kanye West", "<PERSON>"], "producers": ["Kanye West"], "notes": "Version with mumble <PERSON><PERSON><PERSON> vocals. Played by <PERSON><PERSON><PERSON> on Instagram live on July 18th, 2024.", "length": "73.56", "fileDate": 17212608, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/694f056e2154ceb877dc660ced4bc31e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/694f056e2154ceb877dc660ced4bc31e\", \"key\": \"On My Own\", \"title\": \"Consequence - On My Own\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"Version with mumble <PERSON><PERSON><PERSON> vocals. Played by Consequence on Instagram live on July 18th, 2024.\", \"date\": 17212608, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"8dcfd73274eefc0d0f23e1b03d0ffb80\", \"url\": \"https://api.pillowcase.su/api/download/8dcfd73274eefc0d0f23e1b03d0ffb80\", \"size\": \"6.03 MB\", \"duration\": 73.56}", "aliases": [], "size": "6.03 MB"}, {"id": "enjoy-the-pain", "name": "<PERSON> - <PERSON><PERSON> [V1]", "artists": [], "producers": [], "notes": "Early version of \"Enjoy The Pain\" by <PERSON>. Snippet leaked October 16th, 2022..", "length": "13.85", "fileDate": 16658784, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/f8af33848726ee42f7bffad68088d2b9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f8af33848726ee42f7bffad68088d2b9\", \"key\": \"Enjoy The Pain\", \"title\": \"<PERSON> - Enjoy The Pain [V1]\", \"description\": \"Early version of \\\"Enjoy The Pain\\\" by <PERSON>. Snippet leaked October 16th, 2022..\", \"date\": 16658784, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"778edfa730aaea9562965fe98d611e0a\", \"url\": \"https://api.pillowcase.su/api/download/778edfa730aaea9562965fe98d611e0a\", \"size\": \"5.07 MB\", \"duration\": 13.85}", "aliases": [], "size": "5.07 MB"}, {"id": "flashlights", "name": "<PERSON> - Flashlights [V1]", "artists": ["The WRLDFMS <PERSON>", "Seal"], "producers": ["Kanye West"], "notes": "OG Filename: JL - flashlights_ja RefMix 1\nA 2011 song produced by <PERSON><PERSON><PERSON>, presumably from Cruel Summer which uses the original \"Fade\" instrumental, featuring <PERSON>, <PERSON>, and <PERSON>. ProTools session also has a <PERSON><PERSON><PERSON> mumble take. Original snippet leaked October 3rd, 2022.", "length": "204.9", "fileDate": 16849728, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/72c9a7e7aafa548ad3b173a3f34f8b0a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/72c9a7e7aafa548ad3b173a3f34f8b0a\", \"key\": \"Flashlights\", \"title\": \"<PERSON> - Flashlights [V1]\", \"artists\": \"(feat. The WRLDFMS <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: JL - flashlights_ja RefMix 1\\nA 2011 song produced by <PERSON><PERSON><PERSON>, presumably from Cruel Summer which uses the original \\\"Fade\\\" instrumental, featuring <PERSON>, <PERSON>, and <PERSON>. ProTools session also has a <PERSON><PERSON><PERSON> mumble take. Original snippet leaked October 3rd, 2022.\", \"date\": 16849728, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e23a8ac58107d85d7ca205f481276416\", \"url\": \"https://api.pillowcase.su/api/download/e23a8ac58107d85d7ca205f481276416\", \"size\": \"8.13 MB\", \"duration\": 204.9}", "aliases": [], "size": "8.13 MB"}, {"id": "flashlights-27", "name": "<PERSON> - Flashlights [V2]", "artists": ["The WRLDFMS <PERSON>", "Seal"], "producers": ["Kanye West"], "notes": "OG Filename: flashlights ye vs idea 1\nVersion with a rough take of <PERSON><PERSON><PERSON> singing as reference for a future verse. Bounced from the session. Original snippet leaked October 16th, 2022.", "length": "200", "fileDate": 16849728, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/9100c2a374149c0dc6a6cd0b318a7529", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9100c2a374149c0dc6a6cd0b318a7529\", \"key\": \"Flashlights\", \"title\": \"<PERSON> - Flashlights [V2]\", \"artists\": \"(with <PERSON><PERSON><PERSON> & <PERSON>) (feat. The WRLDFMS Tony <PERSON> & <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: flashlights ye vs idea 1\\nVersion with a rough take of <PERSON><PERSON><PERSON> singing as reference for a future verse. Bounced from the session. Original snippet leaked October 16th, 2022.\", \"date\": 16849728, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"b2cdd64f8f7521692bd69c15aa07399c\", \"url\": \"https://api.pillowcase.su/api/download/b2cdd64f8f7521692bd69c15aa07399c\", \"size\": \"8.05 MB\", \"duration\": 200}", "aliases": [], "size": "8.05 MB"}, {"id": "flashlights-28", "name": "<PERSON> - Flashlights [V3]", "artists": ["Kanye West", "Seal", "The WRLDFMS <PERSON>"], "producers": ["Kanye West"], "notes": "OG Filename: flashlights ye talk vs\nVersion with a rough take of <PERSON><PERSON><PERSON> talking. Bounced from the session. It's currently unknown what <PERSON><PERSON><PERSON>'s vocals were meant for, whether being a reference for <PERSON> or his own verse. Original snippet leaked October 16th, 2022.", "length": "200", "fileDate": 16848864, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "watch-the-throne", "originalUrl": "https://pillowcase.su/f/ad51a7bb1e4cfb3bffa654d97d3d5ee2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ad51a7bb1e4cfb3bffa654d97d3d5ee2\", \"key\": \"Flashlights\", \"title\": \"<PERSON> - Flashlights [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>, <PERSON> & The WRLDFMS <PERSON>) (prod. <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: flashlights ye talk vs\\nVersion with a rough take of <PERSON><PERSON><PERSON> talking. <PERSON>unced from the session. It's currently unknown what <PERSON><PERSON><PERSON>'s vocals were meant for, whether being a reference for <PERSON> or his own verse. Original snippet leaked October 16th, 2022.\", \"date\": 16848864, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f2b446ddb76402ccdb865ac29044c271\", \"url\": \"https://api.pillowcase.su/api/download/f2b446ddb76402ccdb865ac29044c271\", \"size\": \"8.05 MB\", \"duration\": 200}", "aliases": [], "size": "8.05 MB"}]}