{"id": "good-ass-job", "name": "Good Ass Job (2018)", "description": "Good Ass Job is the name of the <PERSON><PERSON><PERSON> and <PERSON> collab project that people talked about for years before being officially announced in 2018. The project was supposed to be just seven tracks long, similar to all the Wyoming albums. The central theme of this project is a celebration, as many of the tracks we've heard from this project seem to be very joyful and uplifting. <PERSON><PERSON><PERSON> and <PERSON> presumably canceled it sometime in 2019. This project has no covers we know of, so we have used unofficial artwork.", "backgroundColor": "rgb(221, 59, 197)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17E_cWTt_W2um2TgvQ8s8YGZNu1bxXSo2WzK_pjaEX8UlhHgsUF3yyhbV1w-ZvUw2Ea64rzdiPDz0tJLNactDrxrVJIMnT1QeCVmNXcPfOjxXT18CCnriTrAsaj_2KGImpR4FsLwPjhR0uhrlZg?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "all-we-got-is-love", "name": "All We Got Is Love [V1]", "artists": [], "producers": [], "notes": "<PERSON> played during the same Good Ass Job recording session as \"In My Eyes\". Title confirmed by <PERSON> in the session recordings leaked in the Dreezy Chakras zip. Said by him to be an \"old one\", the file played in the session appears to have a finished Chance verse that was recorded prior, with the rest presumably being open. Samples \"Supernatural\" by <PERSON> Are King. File linked is from <PERSON><PERSON>' mic stem from the later session, this entry is for the open verse he is recording over.", "length": "196.53", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/d3b30aeba7ec0ebe6840b3c8c147d3d1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d3b30aeba7ec0ebe6840b3c8c147d3d1\", \"key\": \"All We Got Is Love\", \"title\": \"All We Got Is Love [V1]\", \"aliases\": [\"We Got\", \"We Love\"], \"description\": \"Song played during the same Good Ass Job recording session as \\\"In My Eyes\\\". Title confirmed by <PERSON> in the session recordings leaked in the Dreezy Chakras zip. Said by him to be an \\\"old one\\\", the file played in the session appears to have a finished Chance verse that was recorded prior, with the rest presumably being open. Samples \\\"Supernatural\\\" by <PERSON> Are King. File linked is from <PERSON><PERSON>' mic stem from the later session, this entry is for the open verse he is recording over.\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"0dfddb382cd8b370b27b4d379923d2a0\", \"url\": \"https://api.pillowcase.su/api/download/0dfddb382cd8b370b27b4d379923d2a0\", \"size\": \"2.3 MB\", \"duration\": 196.53}", "aliases": ["We Got", "We Love"], "size": "2.3 MB"}, {"id": "all-we-got-is-love-2", "name": "All We Got Is Love [V2]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "<PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> recording over the open verse version. <PERSON><PERSON>'s mic stem is the only file that leaked so his vocals are much louder than everything else. After finishing recording <PERSON> and <PERSON><PERSON><PERSON> suggested simplifying the title to either \"We Got\" or \"We Love\".", "length": "196.53", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/d3b30aeba7ec0ebe6840b3c8c147d3d1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d3b30aeba7ec0ebe6840b3c8c147d3d1\", \"key\": \"All We Got Is Love\", \"title\": \"All We Got Is Love [V2]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"aliases\": [\"We Got\", \"We Love\"], \"description\": \"<PERSON> <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> recording over the open verse version. <PERSON><PERSON>'s mic stem is the only file that leaked so his vocals are much louder than everything else. After finishing recording <PERSON> and <PERSON><PERSON><PERSON> suggested simplifying the title to either \\\"We Got\\\" or \\\"We Love\\\".\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"0dfddb382cd8b370b27b4d379923d2a0\", \"url\": \"https://api.pillowcase.su/api/download/0dfddb382cd8b370b27b4d379923d2a0\", \"size\": \"2.3 MB\", \"duration\": 196.53}", "aliases": ["We Got", "We Love"], "size": "2.3 MB"}, {"id": "all-we-got-is-love-3", "name": "All We Got Is Love [V2]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "<PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> recording over the open verse version. <PERSON><PERSON>'s mic stem is the only file that leaked so his vocals are much louder than everything else. After finishing recording <PERSON> and <PERSON><PERSON><PERSON> suggested simplifying the title to either \"We Got\" or \"We Love\".", "length": "34.83", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/303a4ac21f0f171973fb097a55350be1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/303a4ac21f0f171973fb097a55350be1\", \"key\": \"All We Got Is Love\", \"title\": \"All We Got Is Love [V2]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"aliases\": [\"We Got\", \"We Love\"], \"description\": \"<PERSON> <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON> recording over the open verse version. <PERSON><PERSON>'s mic stem is the only file that leaked so his vocals are much louder than everything else. After finishing recording <PERSON> and <PERSON><PERSON><PERSON> suggested simplifying the title to either \\\"We Got\\\" or \\\"We Love\\\".\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"0887f2e7a6e4b20d99201ddd3ea75772\", \"url\": \"https://api.pillowcase.su/api/download/0887f2e7a6e4b20d99201ddd3ea75772\", \"size\": \"1 MB\", \"duration\": 34.83}", "aliases": ["We Got", "We Love"], "size": "1 MB"}, {"id": "anxiety", "name": "Anxiety [V1]", "artists": [], "producers": [], "notes": "Solo Chance version. Samples \"Joy\" by <PERSON><PERSON><PERSON>.", "length": "12.04", "fileDate": 16187904, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/7fadfe8bdf913ddfb9a61cf8b44eead5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7fadfe8bdf913ddfb9a61cf8b44eead5\", \"key\": \"Anxiety\", \"title\": \"Anxiety [V1]\", \"description\": \"Solo Chance version. Samples \\\"Joy\\\" by <PERSON><PERSON><PERSON>.\", \"date\": 16187904, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"be1a68e49e8a04653a2f59db95ef7d77\", \"url\": \"https://api.pillowcase.su/api/download/be1a68e49e8a04653a2f59db95ef7d77\", \"size\": \"917 kB\", \"duration\": 12.04}", "aliases": [], "size": "917 kB"}, {"id": "anxiety-5", "name": "Anxiety [V2]", "artists": ["070 Shake"], "producers": [], "notes": "<PERSON> and 070 Shake vocals. Unsure if this comes before or after the Chance version.", "length": "13.06", "fileDate": 16187904, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/3f39d5d49c794b3d95a21004ed20ab55", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3f39d5d49c794b3d95a21004ed20ab55\", \"key\": \"Anxiety\", \"title\": \"Anxiety [V2]\", \"artists\": \"(feat. 070 Shake)\", \"description\": \"<PERSON><PERSON><PERSON> and 070 Shake vocals. Unsure if this comes before or after the Chance version.\", \"date\": 16187904, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b237c8b6fda5c9017dd067a3d03523c9\", \"url\": \"https://api.pillowcase.su/api/download/b237c8b6fda5c9017dd067a3d03523c9\", \"size\": \"934 kB\", \"duration\": 13.06}", "aliases": [], "size": "934 kB"}, {"id": "black-men-don-t-cheat", "name": "Black Men Don't Cheat [V1]", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Version with <PERSON><PERSON> background vocals and <PERSON> the Rapper adlibs. Snippet was posted by <PERSON><PERSON><PERSON> to Instagram. Full recording of the song found in the Dreezy Chakras zip.", "length": "27.9", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/fa0f23b834178383ffe51e3d3fd0fd6c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/fa0f23b834178383ffe51e3d3fd0fd6c\", \"key\": \"Black Men Don't Cheat\", \"title\": \"Black Men Don't Cheat [V1]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"In My Eyes\"], \"description\": \"Version with <PERSON><PERSON> background vocals and <PERSON> the Rapper adlibs. Snippet was posted by <PERSON><PERSON><PERSON> to Instagram. Full recording of the song found in the Dreezy Chakras zip.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"fa0a2fca819ef187366bdcb666f766d2\", \"url\": \"https://api.pillowcase.su/api/download/fa0a2fca819ef187366bdcb666f766d2\", \"size\": \"1.17 MB\", \"duration\": 27.9}", "aliases": ["In My Eyes"], "size": "1.17 MB"}, {"id": "black-men-don-t-cheat-7", "name": "Black Men Don't Cheat [V1]", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Version with <PERSON><PERSON> background vocals and <PERSON> the Rapper adlibs. Snippet was posted by <PERSON><PERSON><PERSON> to Instagram. Full recording of the song found in the Dreezy Chakras zip.", "length": "160.68", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/21e24b8a8e543e438e861c27c0a0ca8b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/21e24b8a8e543e438e861c27c0a0ca8b\", \"key\": \"Black Men Don't Cheat\", \"title\": \"Black Men Don't Cheat [V1]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"In My Eyes\"], \"description\": \"Version with <PERSON><PERSON> background vocals and <PERSON> the Rapper adlibs. Snippet was posted by <PERSON><PERSON><PERSON> to Instagram. Full recording of the song found in the Dreezy Chakras zip.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"e420ab57a9897bec1a3aeb5280fc09ad\", \"url\": \"https://api.pillowcase.su/api/download/e420ab57a9897bec1a3aeb5280fc09ad\", \"size\": \"3.3 MB\", \"duration\": 160.68}", "aliases": ["In My Eyes"], "size": "3.3 MB"}, {"id": "black-men-don-t-cheat-8", "name": "Black Men Don't Cheat [V2]", "artists": [], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "OG Filename: Black Men Dont Cheat Scratch\nSolo Chance reference track. Has a long outro of just talking over no instrumental at the end.", "length": "228.1", "fileDate": 16347744, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/7c3c5bbc1bc0e05991e66102b657a73e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7c3c5bbc1bc0e05991e66102b657a73e\", \"key\": \"Black Men Don't Cheat\", \"title\": \"Black Men Don't Cheat [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON>)\", \"aliases\": [\"In My Eyes\"], \"description\": \"OG Filename: Black Men Dont Cheat Scratch\\nSolo Chance reference track. Has a long outro of just talking over no instrumental at the end.\", \"date\": 16347744, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"4e07a2dab80ce589c4b2bbec313f4f66\", \"url\": \"https://api.pillowcase.su/api/download/4e07a2dab80ce589c4b2bbec313f4f66\", \"size\": \"4.37 MB\", \"duration\": 228.1}", "aliases": ["In My Eyes"], "size": "4.37 MB"}, {"id": "clouted-up", "name": "✨ Clouted Up [V1]", "artists": [], "producers": ["<PERSON> and the Lights"], "notes": "OG Filename: Clouted up_verse\nSupposedly the intro track judging from the fact that the note which had \"Clouted Up\" in it had the title \"JWD intro\". Snippet posted to <PERSON>'s Twitter before being immediately removed.", "length": "217.8", "fileDate": 16352064, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/9530a624894c292abf974bd4f40eefdd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9530a624894c292abf974bd4f40eefdd\", \"key\": \"Clouted Up\", \"title\": \"\\u2728 Clouted Up [V1]\", \"artists\": \"(prod. <PERSON> and the Lights)\", \"description\": \"OG Filename: Clouted up_verse\\nSupposedly the intro track judging from the fact that the note which had \\\"Clouted Up\\\" in it had the title \\\"JWD intro\\\". Snippet posted to Chance's Twitter before being immediately removed.\", \"date\": 16352064, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"1c53a89f8180096d59764662a2bc42f1\", \"url\": \"https://api.pillowcase.su/api/download/1c53a89f8180096d59764662a2bc42f1\", \"size\": \"4.21 MB\", \"duration\": 217.8}", "aliases": [], "size": "4.21 MB"}, {"id": "clouted-up-10", "name": "Clouted Up [V2]", "artists": [], "producers": ["<PERSON> and the Lights"], "notes": "Recorded during the Dr<PERSON>zy Chakras sessions. Contains <PERSON><PERSON> doing what may be a reference for <PERSON>. Other than <PERSON><PERSON>'s vocals it's seemingly the same as the previous version.", "length": "217.1", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/0b6164337f0db4555905a3afc4fdea39", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0b6164337f0db4555905a3afc4fdea39\", \"key\": \"Clouted Up\", \"title\": \"Clouted Up [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (prod. <PERSON> and the Lights)\", \"description\": \"Recorded during the Dreezy Chakras sessions. Contains <PERSON><PERSON> doing what may be a reference for <PERSON>. Other than <PERSON><PERSON>'s vocals it's seemingly the same as the previous version.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f972e1b6ccc70541e19686127f6904f8\", \"url\": \"https://api.pillowcase.su/api/download/f972e1b6ccc70541e19686127f6904f8\", \"size\": \"4.2 MB\", \"duration\": 217.1}", "aliases": [], "size": "4.2 MB"}, {"id": "good-ass-job", "name": "Good Ass Job", "artists": [], "producers": [], "notes": "OG Filename: Good Ass Job [<PERSON><PERSON><PERSON> <PERSON>] (Demo 01)\nGAJ-era reference track with vocals from an unknown artist.", "length": "11.57", "fileDate": 16806528, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/0eca7a82f2ce47f3254ef5f080a72937", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0eca7a82f2ce47f3254ef5f080a72937\", \"key\": \"Good Ass Job\", \"title\": \"Good Ass Job\", \"artists\": \"(ref. ???)\", \"description\": \"OG Filename: Good Ass Job [<PERSON><PERSON><PERSON> x <PERSON>] (Demo 01)\\nGAJ-era reference track with vocals from an unknown artist.\", \"date\": 16806528, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"097dc93cb0c9fee766a38c610531b23d\", \"url\": \"https://api.pillowcase.su/api/download/097dc93cb0c9fee766a38c610531b23d\", \"size\": \"909 kB\", \"duration\": 11.57}", "aliases": [], "size": "909 kB"}, {"id": "gun-or-god", "name": "Gun Or God [V3]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "Played during GAJ 2018 sessions. Contains a different vocal take from <PERSON> and adlibs from <PERSON><PERSON><PERSON> in the Dreezy Chakras zip.", "length": "106.92", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/42ae18a6030085184883bd4fe415110f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/42ae18a6030085184883bd4fe415110f\", \"key\": \"Gun Or God\", \"title\": \"Gun Or God [V3]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"description\": \"Played during GAJ 2018 sessions. Contains a different vocal take from <PERSON> and adlibs from <PERSON><PERSON>. Found in the Dreezy Chakras zip.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"5971a27985fe8d59602e7cd62aeeecac\", \"url\": \"https://api.pillowcase.su/api/download/5971a27985fe8d59602e7cd62aeeecac\", \"size\": \"2.44 MB\", \"duration\": 106.92}", "aliases": [], "size": "2.44 MB"}, {"id": "hold-it-down", "name": "Hold It Down [V2]", "artists": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "producers": [], "notes": "Track originating in sessions for Owbum. Recorded on by <PERSON><PERSON> during sessions for GAJ 2018, however only his adlibs are available.", "length": "158.96", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/ebb082436afa48d1044f473ff8722455", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ebb082436afa48d1044f473ff8722455\", \"key\": \"Hold It Down\", \"title\": \"Hold It Down [V2]\", \"artists\": \"(feat. <PERSON><PERSON> & An<PERSON>)\", \"description\": \"Track originating in sessions for Owbum. Recorded on by <PERSON><PERSON> during sessions for GAJ 2018, however only his adlibs are available.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"ede9a870537b1e4d03413fa8df80a3c8\", \"url\": \"https://api.pillowcase.su/api/download/ede9a870537b1e4d03413fa8df80a3c8\", \"size\": \"3.27 MB\", \"duration\": 158.96}", "aliases": [], "size": "3.27 MB"}, {"id": "how-to-go-crazy", "name": "How To Go Crazy [V1]", "artists": ["<PERSON> and the Lights", "<PERSON><PERSON>"], "producers": ["<PERSON>", "<PERSON> and the Lights"], "notes": "Version of \"How To Go Crazy\" played during Good Ass Job sessions. different beat from release and mumble Ye vocals. Contains <PERSON><PERSON> riffing. Around 5 Minutes in Length. Leaked in the Dreezy Chakras zip. Only <PERSON><PERSON>'s vocal stem has leaked, however a snippet of another recording of this session leaked years before, and was given the fan name \"Australia\" before it was known to be How To Go Crazy.", "length": "345.76", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/811ce8d24cb167c13481f4565ee4030b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/811ce8d24cb167c13481f4565ee4030b\", \"key\": \"How To Go Crazy\", \"title\": \"How To Go Crazy [V1]\", \"artists\": \"(feat. <PERSON> and the Lights & Ant Clemons) (prod. <PERSON> Iver & Francis and the Lights)\", \"aliases\": [\"The Big Day\"], \"description\": \"Version of \\\"How To Go Crazy\\\" played during Good Ass Job sessions. different beat from release and mumble Ye vocals. Contains Ant Clemons riffing. Around 5 Minutes in Length. Leaked in the Dreezy Chakras zip. Only <PERSON><PERSON>'s vocal stem has leaked, however a snippet of another recording of this session leaked years before, and was given the fan name \\\"Australia\\\" before it was known to be How To Go Crazy.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"d1304d520df6ce8c84433e7f6be0601b\", \"url\": \"https://api.pillowcase.su/api/download/d1304d520df6ce8c84433e7f6be0601b\", \"size\": \"6.26 MB\", \"duration\": 345.76}", "aliases": ["The Big Day"], "size": "6.26 MB"}, {"id": "how-to-go-crazy-15", "name": "How To Go Crazy [V2]", "artists": ["<PERSON> and the Lights"], "producers": ["<PERSON>", "<PERSON> and the Lights"], "notes": "Original version of the <PERSON> the Rapper song \"The Big Day\" which ended up on <PERSON>'s album of the same name. <PERSON><PERSON> confirmed that it was recorded during the sessions for Good Ass Job. Has an Ant Clemons reference verse for <PERSON><PERSON><PERSON>, with three minutes of open after. <PERSON> doesn't do the screaming he does in the final song, although otherwise his and <PERSON>'s parts are the same. Instrumental is more laid back than the final.", "length": "11.31", "fileDate": 15552000, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/23009522c5fc5e99112bae995dbc8c13", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/23009522c5fc5e99112bae995dbc8c13\", \"key\": \"How To Go Crazy\", \"title\": \"How To Go Crazy [V2]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON> and the Lights) (prod. <PERSON>ver & Francis and the Lights)\", \"aliases\": [\"The Big Day\"], \"description\": \"Original version of the Chance the Rapper song \\\"The Big Day\\\" which ended up on <PERSON>'s album of the same name. <PERSON><PERSON> confirmed that it was recorded during the sessions for Good Ass Job. Has an <PERSON><PERSON> reference verse for <PERSON><PERSON><PERSON>, with three minutes of open after. <PERSON> doesn't do the screaming he does in the final song, although otherwise his and <PERSON>'s parts are the same. Instrumental is more laid back than the final.\", \"date\": 15552000, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"8ed5ed1e90a42b0537b04af08902538d\", \"url\": \"https://api.pillowcase.su/api/download/8ed5ed1e90a42b0537b04af08902538d\", \"size\": \"906 kB\", \"duration\": 11.31}", "aliases": ["The Big Day"], "size": "906 kB"}, {"id": "j-j-what", "name": "<PERSON><PERSON><PERSON><PERSON> [V2]", "artists": ["Via Rosa", "<PERSON>", "<PERSON><PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Spanish Diego"], "notes": "Track originating from sessions from Chance The Rapper's scrapped album Owbum, apparently being worked on by <PERSON><PERSON> during GAJ 2018 sessions as he recorded adlibs on it. Lacks any Ye vocals. Leaked in the Dreezy Chakras zip.", "length": "168.52", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/319d396b12e798201dec70f0374a892f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/319d396b12e798201dec70f0374a892f\", \"key\": \"<PERSON><PERSON><PERSON>. What\", \"title\": \"<PERSON><PERSON><PERSON><PERSON> What [V2]\", \"artists\": \"(feat. <PERSON>, <PERSON> & <PERSON>) (<PERSON>d<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Spanish <PERSON>)\", \"aliases\": [\"Drowsiness\"], \"description\": \"Track originating from sessions from <PERSON> The Rapper's scrapped album Owbum, apparently being worked on by <PERSON><PERSON> during GAJ 2018 sessions as he recorded adlibs on it. Lacks any Ye vocals. Leaked in the Dreezy Chakras zip.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"9bbced960c1d1416fc97feba16a24fcc\", \"url\": \"https://api.pillowcase.su/api/download/9bbced960c1d1416fc97feba16a24fcc\", \"size\": \"3.42 MB\", \"duration\": 168.52}", "aliases": ["Drowsiness"], "size": "3.42 MB"}, {"id": "like-the-internet", "name": "Like The Internet [V2]", "artists": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "producers": [], "notes": "Track from the Chance and Childish Gambino collab album played during GAJ sessions. <PERSON><PERSON><PERSON> and <PERSON><PERSON> are heard riffing over the open. Found in the Dreezy Chakras zip.", "length": "126.47", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/ffc659c45dcaeffab5c8ba643d7a4c0b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/ffc659c45dcaeffab5c8ba643d7a4c0b\", \"key\": \"Like The Internet\", \"title\": \"Like The Internet [V2]\", \"artists\": \"(feat. <PERSON><PERSON>ambino & <PERSON>)\", \"description\": \"Track from the Chance and Childish Gambino collab album played during GAJ sessions. <PERSON><PERSON><PERSON> and <PERSON><PERSON> are heard riffing over the open. Found in the Dreezy Chakras zip.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f5a6c58206ecf31e2d39f1592020ab73\", \"url\": \"https://api.pillowcase.su/api/download/f5a6c58206ecf31e2d39f1592020ab73\", \"size\": \"2.75 MB\", \"duration\": 126.47}", "aliases": [], "size": "2.75 MB"}, {"id": "", "name": "??? [V1]", "artists": [], "producers": ["Kanye West"], "notes": "Beat that samples \"Never Can Say Goodbye\" by <PERSON>. Likely meant for Good Ass Job made late August 2018, but it's unknown if <PERSON> recorded for it, however sessions of <PERSON> recording for it were found in the Dreezy Chakras zip. Footage of <PERSON><PERSON><PERSON> chopping the sample while in the studio with <PERSON> is linked.", "length": "", "fileDate": 16930944, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/d9e81101afbb8010a4acf9f95c82e4a8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d9e81101afbb8010a4acf9f95c82e4a8\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"MJ\"], \"description\": \"Beat that samples \\\"Never Can Say Goodbye\\\" by <PERSON>. Likely meant for Good Ass Job made late August 2018, but it's unknown if <PERSON> recorded for it, however sessions of <PERSON> recording for it were found in the Dreezy Chakras zip. Footage of <PERSON><PERSON><PERSON> chopping the sample while in the studio with <PERSON> is linked.\", \"date\": 16930944, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["MJ"], "size": ""}, {"id": "-19", "name": "??? [V1]", "artists": [], "producers": ["Kanye West"], "notes": "Beat that samples \"Never Can Say Goodbye\" by <PERSON>. Likely meant for Good Ass Job made late August 2018, but it's unknown if <PERSON> recorded for it, however sessions of <PERSON> recording for it were found in the Dreezy Chakras zip. Footage of <PERSON><PERSON><PERSON> chopping the sample while in the studio with <PERSON> is linked.", "length": "", "fileDate": 16930944, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/7a53f146f8d08076ad6c7e4abd1cd810", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7a53f146f8d08076ad6c7e4abd1cd810\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"MJ\"], \"description\": \"Beat that samples \\\"Never Can Say Goodbye\\\" by <PERSON>. Likely meant for Good Ass Job made late August 2018, but it's unknown if <PERSON> recorded for it, however sessions of <PERSON> recording for it were found in the Dreezy Chakras zip. Footage of <PERSON><PERSON><PERSON> chopping the sample while in the studio with <PERSON> is linked.\", \"date\": 16930944, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": ["MJ"], "size": ""}, {"id": "mj", "name": "✨ MJ [V2]", "artists": [], "producers": ["Kanye West", "BoogzDaBeast"], "notes": "OG Filename: <PERSON><PERSON><PERSON><PERSON> MJ (boogz edit)\nMore finished version with drums. Was played by <PERSON><PERSON><PERSON> on Instagram Live in October 2019.", "length": "103.81", "fileDate": 16972416, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/d276ae43a96e681388b0566b0503ea15", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d276ae43a96e681388b0566b0503ea15\", \"key\": \"MJ\", \"title\": \"\\u2728 MJ [V2]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> West & BoogzDaBeast)\", \"description\": \"OG Filename: <PERSON><PERSON><PERSON><PERSON> (boogz edit)\\nMore finished version with drums. Was played by Consequence on Instagram Live in October 2019.\", \"date\": 16972416, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"862aef2e550d050dd773081c17147718\", \"url\": \"https://api.pillowcase.su/api/download/862aef2e550d050dd773081c17147718\", \"size\": \"2.39 MB\", \"duration\": 103.81}", "aliases": [], "size": "2.39 MB"}, {"id": "mj-21", "name": "MJ [V4]", "artists": [], "producers": ["Kanye West", "BoogzDaBeast"], "notes": "Found in the Dreezy \"I Love It (Remix) session\", which has many files of <PERSON> the Rapper doing his verse for \"MJ\" (this is just one of them). Snippet of the song also leaked the same day the session cracked.", "length": "6.09", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/49f2fb50ddc731a6371bf13b9e34bec8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/49f2fb50ddc731a6371bf13b9e34bec8\", \"key\": \"MJ\", \"title\": \"<PERSON><PERSON> [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & BoogzDaBeast)\", \"description\": \"Found in the Dreezy \\\"I Love It (Remix) session\\\", which has many files of <PERSON> the <PERSON> doing his verse for \\\"MJ\\\" (this is just one of them). Snippet of the song also leaked the same day the session cracked.\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"926b770bc75ccfa6d08c80b7c20f1710\", \"url\": \"https://api.pillowcase.su/api/download/926b770bc75ccfa6d08c80b7c20f1710\", \"size\": \"822 kB\", \"duration\": 6.09}", "aliases": [], "size": "822 kB"}, {"id": "mj-22", "name": "MJ [V4]", "artists": [], "producers": ["Kanye West", "BoogzDaBeast"], "notes": "Found in the Dreezy \"I Love It (Remix) session\", which has many files of <PERSON> the Rapper doing his verse for \"MJ\" (this is just one of them). Snippet of the song also leaked the same day the session cracked.", "length": "290.51", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/c4594dd843c54b7f467c53d7fd412dc5", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c4594dd843c54b7f467c53d7fd412dc5\", \"key\": \"M<PERSON>\", \"title\": \"<PERSON><PERSON> [V4]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & BoogzDaBeast)\", \"description\": \"Found in the Dreezy \\\"I Love It (Remix) session\\\", which has many files of <PERSON> the <PERSON> doing his verse for \\\"MJ\\\" (this is just one of them). Snippet of the song also leaked the same day the session cracked.\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8626bca9c0bef34602ad9bc9c45bcffe\", \"url\": \"https://api.pillowcase.su/api/download/8626bca9c0bef34602ad9bc9c45bcffe\", \"size\": \"3.05 MB\", \"duration\": 290.51}", "aliases": [], "size": "3.05 MB"}, {"id": "pets", "name": "Pet<PERSON> [V4]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "Early version of <PERSON><PERSON> with <PERSON><PERSON> vocals. Has a less developed instrumental than previous versions strangely. Found in the Dreezy Chakras zip.", "length": "1078", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/02459e439aeda6225eb45ba2e95a3896", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/02459e439aeda6225eb45ba2e95a3896\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V4]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"aliases\": [\"The Garden\"], \"description\": \"Early version of <PERSON><PERSON> with <PERSON><PERSON> vocals. Has a less developed instrumental than previous versions strangely. Found in the Dreezy Chakras zip.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"97986100c5caffc065a273a30b0285a1\", \"url\": \"https://api.pillowcase.su/api/download/97986100c5caffc065a273a30b0285a1\", \"size\": \"18 MB\", \"duration\": 1078}", "aliases": ["The Garden"], "size": "18 MB"}, {"id": "pets-24", "name": "Pet<PERSON> [V6]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "17 minute long jam session of <PERSON>, <PERSON>, and <PERSON><PERSON> riffing on what would later become The Garden. Likely still called <PERSON><PERSON> at this time. Features a speech from <PERSON> about happiness. He also takes a phone call 3 minutes into it. Leaked in the Dreezy Chakras zip.", "length": "1023.29", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/c161c9516c6d405e2662fe49056f5853", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c161c9516c6d405e2662fe49056f5853\", \"key\": \"<PERSON><PERSON>\", \"title\": \"<PERSON><PERSON> [V6]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"aliases\": [\"The Garden\"], \"description\": \"17 minute long jam session of <PERSON>, <PERSON>, and <PERSON><PERSON> riffing on what would later become The Garden. Likely still called <PERSON><PERSON> at this time. Features a speech from <PERSON> about happiness. He also takes a phone call 3 minutes into it. Leaked in the Dreezy Chakras zip.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"92509ea7b6129a18ac5398f90bbb90a5\", \"url\": \"https://api.pillowcase.su/api/download/92509ea7b6129a18ac5398f90bbb90a5\", \"size\": \"17.1 MB\", \"duration\": 1023.29}", "aliases": ["The Garden"], "size": "17.1 MB"}, {"id": "slide-around", "name": "Slide Around [V2]", "artists": ["<PERSON><PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "Original version of \"Slide Around\" with <PERSON><PERSON> background vocals. Has very short mumble <PERSON><PERSON><PERSON> vocals are heard in the end of <PERSON>'s verse and he is heard talking at the end of the recording. It is not known if he recorded for this song at all. Has only <PERSON>'s verse and open sections that were later filled in by <PERSON><PERSON> and <PERSON> on the released version. Found in the Dreezy Chakras zip.", "length": "238.55", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/e3dc2f1d3376d081363407638168a557", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e3dc2f1d3376d081363407638168a557\", \"key\": \"Slide Around\", \"title\": \"Slide Around [V2]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"Original version of \\\"Slide Around\\\" with <PERSON><PERSON> background vocals. Has very short mumble Kanye vocals are heard in the end of <PERSON>'s verse and he is heard talking at the end of the recording. It is not known if he recorded for this song at all. Has only <PERSON>'s verse and open sections that were later filled in by <PERSON><PERSON> and <PERSON> on the released version. Found in the Dreezy Chakras zip.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ef2da25932d3d418bb23944e3213847d\", \"url\": \"https://api.pillowcase.su/api/download/ef2da25932d3d418bb23944e3213847d\", \"size\": \"2.63 MB\", \"duration\": 238.55}", "aliases": [], "size": "2.63 MB"}, {"id": "-26", "name": "??? [V1]", "artists": [], "producers": ["Nascent"], "notes": "OG Filename: Chance Liked - 1 - 09.01.18\nFirst version of \"Hurricane\". Has the Ye hook, and vocals that would be excluded from later versions of the song. This version uses the full sample instead of the looped version.", "length": "83.21", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/accc6c390a06a43c59fdae748948eea6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/accc6c390a06a43c59fdae748948eea6\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(prod. Nascent)\", \"aliases\": [\"80 Degrees\", \"Hurricane\", \"Hurricanes\", \"Don't Let Me Down\"], \"description\": \"OG Filename: Chance Liked - 1 - 09.01.18\\nFirst version of \\\"Hurricane\\\". Has the Ye hook, and vocals that would be excluded from later versions of the song. This version uses the full sample instead of the looped version.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b8b8f8573177e972c1f1786b868b4aa7\", \"url\": \"https://api.pillowcase.su/api/download/b8b8f8573177e972c1f1786b868b4aa7\", \"size\": \"2.06 MB\", \"duration\": 83.21}", "aliases": ["80 Degrees", "Hurricane", "Hurricanes", "Don't Let Me Down"], "size": "2.06 MB"}, {"id": "-27", "name": "??? [V1]", "artists": [], "producers": [], "notes": "OG Filename: <PERSON> Liked - 9 - 09.01.18\nKanye freestyle made during a recording session with <PERSON>. Features both voice memo vocals and studio vocals. <PERSON> at one point can be heard talking with <PERSON>. Was later worked on by Consequence.", "length": "126.94", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/9f820b0af9e1595c067698be78f39612", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9f820b0af9e1595c067698be78f39612\", \"key\": \"???\", \"title\": \"??? [V1]\", \"aliases\": [\"Back In This Bitch Like\"], \"description\": \"OG Filename: Chance Liked - 9 - 09.01.18\\nKanye freestyle made during a recording session with <PERSON>. Features both voice memo vocals and studio vocals. Ye at one point can be heard talking with <PERSON>. <PERSON> later worked on by Consequence.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8d6760f558003d383a098e5e2a7be821\", \"url\": \"https://api.pillowcase.su/api/download/8d6760f558003d383a098e5e2a7be821\", \"size\": \"2.76 MB\", \"duration\": 126.94}", "aliases": ["Back In This Bitch Like"], "size": "2.76 MB"}, {"id": "-28", "name": "???", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> Chop 1B 08.28.18 REF - 149.2bpm\nChance the Rapper freestyle made during a recording session with <PERSON><PERSON><PERSON>.", "length": "88.06", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/d3299f101d0f30eb5f04bec0000d403c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d3299f101d0f30eb5f04bec0000d403c\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Chop 1B\"], \"description\": \"OG Filename: KW Chance Chop 1B 08.28.18 REF - 149.2bpm\\nChance the Rapper freestyle made during a recording session with <PERSON><PERSON><PERSON>.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6c31408f6d36673de0e54d47b17100e6\", \"url\": \"https://api.pillowcase.su/api/download/6c31408f6d36673de0e54d47b17100e6\", \"size\": \"2.13 MB\", \"duration\": 88.06}", "aliases": ["Chop 1B"], "size": "2.13 MB"}, {"id": "-29", "name": "???", "artists": [], "producers": [], "notes": "OG Filename: <PERSON><PERSON> 7 08.28.18 REF - 114bpm\nChance the Rapper freestyle made during a recording session with <PERSON><PERSON><PERSON>.", "length": "60.6", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/dd397afa06a2e71dcd8f5e93161bcbae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/dd397afa06a2e71dcd8f5e93161bcbae\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Chop 7\"], \"description\": \"OG Filename: <PERSON><PERSON> Cho<PERSON> 7 08.28.18 REF - 114bpm\\nChance the Rapper freestyle made during a recording session with <PERSON><PERSON><PERSON>.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"58538df24e130d44552ba5f6cf51cb77\", \"url\": \"https://api.pillowcase.su/api/download/58538df24e130d44552ba5f6cf51cb77\", \"size\": \"1.69 MB\", \"duration\": 60.6}", "aliases": ["Chop 7"], "size": "1.69 MB"}, {"id": "-30", "name": "???", "artists": [], "producers": [], "notes": "Kanye freestyle. <PERSON>'s vocals cannot be heard in the snippet. Snippets leaked April 7th, 2024 & July 21st, 2024.", "length": "10.46", "fileDate": 17215200, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/3be5827cfcb8f84bdd13bbe4f21033d1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3be5827cfcb8f84bdd13bbe4f21033d1\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Chop 10\"], \"description\": \"Kanye freestyle. <PERSON>'s vocals cannot be heard in the snippet. Snippets leaked April 7th, 2024 & July 21st, 2024.\", \"date\": 17215200, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"9f1c3577eb951074b37043782da6c7d7\", \"url\": \"https://api.pillowcase.su/api/download/9f1c3577eb951074b37043782da6c7d7\", \"size\": \"891 kB\", \"duration\": 10.46}", "aliases": ["Chop 10"], "size": "891 kB"}, {"id": "-31", "name": "???", "artists": [], "producers": [], "notes": "Kanye freestyle. <PERSON>'s vocals cannot be heard in the snippet. Snippets leaked April 7th, 2024 & July 21st, 2024.", "length": "13.43", "fileDate": 17215200, "leakDate": "", "availableLength": "Snippet", "quality": "CD Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/61f7bbb4ac9ab9025da9278621a4caaa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/61f7bbb4ac9ab9025da9278621a4caaa\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Chop 10\"], \"description\": \"Kany<PERSON> freestyle. <PERSON>'s vocals cannot be heard in the snippet. Snippets leaked April 7th, 2024 & July 21st, 2024.\", \"date\": 17215200, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"7b937c392fc83d2ea080f494aa9fbf12\", \"url\": \"https://api.pillowcase.su/api/download/7b937c392fc83d2ea080f494aa9fbf12\", \"size\": \"939 kB\", \"duration\": 13.43}", "aliases": ["Chop 10"], "size": "939 kB"}, {"id": "-32", "name": "??? [V1]", "artists": [], "producers": ["BONGO ByTheWay"], "notes": "Version of the track that <PERSON><PERSON> recorded his adlibs on. Can be heard heavily in the bleed.", "length": "87.54", "fileDate": 17174592, "leakDate": "", "availableLength": "Snippet", "quality": "Low Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/d96830ded330f2ec2272ce77ab527c1f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d96830ded330f2ec2272ce77ab527c1f\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> ByTheWay)\", \"aliases\": [\"Family\"], \"description\": \"Version of the track that <PERSON><PERSON> recorded his adlibs on. Can be heard heavily in the bleed.\", \"date\": 17174592, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"a45e18872ec63f3b731a2794aa808cbf\", \"url\": \"https://api.pillowcase.su/api/download/a45e18872ec63f3b731a2794aa808cbf\", \"size\": \"2.13 MB\", \"duration\": 87.54}", "aliases": ["Family"], "size": "2.13 MB"}, {"id": "-33", "name": "??? [V2]", "artists": ["<PERSON><PERSON>"], "producers": ["BONGO ByTheWay"], "notes": "Played during the <PERSON><PERSON><PERSON> session, contains <PERSON> and <PERSON> vocals. Has some mumble from <PERSON>. Directly after their portion, <PERSON><PERSON> does some vocals for the track.", "length": "87.54", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/d96830ded330f2ec2272ce77ab527c1f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d96830ded330f2ec2272ce77ab527c1f\", \"key\": \"???\", \"title\": \"??? [V2]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON> ByTheWay)\", \"aliases\": [\"Family\"], \"description\": \"Played during the Dreezy Chakras session, contains <PERSON> and <PERSON> vocals. Has some mumble from <PERSON>. Directly after their portion, <PERSON><PERSON> does some vocals for the track.\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"a45e18872ec63f3b731a2794aa808cbf\", \"url\": \"https://api.pillowcase.su/api/download/a45e18872ec63f3b731a2794aa808cbf\", \"size\": \"2.13 MB\", \"duration\": 87.54}", "aliases": ["Family"], "size": "2.13 MB"}, {"id": "-34", "name": "??? [V2]", "artists": ["<PERSON><PERSON>"], "producers": ["BONGO ByTheWay"], "notes": "Played during the <PERSON><PERSON><PERSON> session, contains <PERSON> and <PERSON> vocals. Has some mumble from <PERSON>. Directly after their portion, <PERSON><PERSON> does some vocals for the track.", "length": "26.15", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/695fb82a499df570e3326684fe506fd4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/695fb82a499df570e3326684fe506fd4\", \"key\": \"???\", \"title\": \"??? [V2]\", \"artists\": \"(feat. <PERSON><PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON> ByTheWay)\", \"aliases\": [\"Family\"], \"description\": \"Played during the Dreezy Chakras session, contains <PERSON> and <PERSON> vocals. Has some mumble from <PERSON>. Directly after their portion, <PERSON><PERSON> does some vocals for the track.\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"e16da900c6e850455ffb31f0de90c69f\", \"url\": \"https://api.pillowcase.su/api/download/e16da900c6e850455ffb31f0de90c69f\", \"size\": \"1.14 MB\", \"duration\": 26.15}", "aliases": ["Family"], "size": "1.14 MB"}, {"id": "-35", "name": "???", "artists": [], "producers": [], "notes": "Recording of <PERSON> and <PERSON><PERSON><PERSON> together, likely recording references for Good Ass Job. Title is unknown but \"Good Job\" is heard numerous times throughout the snippet. Was posted in mid-September 2018.", "length": "19.07", "fileDate": 15369696, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/e38619174eeff20cfd56081ad6c390dc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e38619174eeff20cfd56081ad6c390dc\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Good Job\"], \"description\": \"<PERSON> of <PERSON> and <PERSON><PERSON><PERSON> together, likely recording references for Good Ass Job. Title is unknown but \\\"Good Job\\\" is heard numerous times throughout the snippet. Was posted in mid-September 2018.\", \"date\": 15369696, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"48b89831e693afee77d5189b6a70dc80\", \"url\": \"https://api.pillowcase.su/api/download/48b89831e693afee77d5189b6a70dc80\", \"size\": \"877 kB\", \"duration\": 19.07}", "aliases": ["Good Job"], "size": "877 kB"}, {"id": "-36", "name": "??? [V2]", "artists": ["<PERSON><PERSON>", "<PERSON> and the Lights"], "producers": [], "notes": "Unknown track <PERSON><PERSON> recorded adlibs for. Found in the Dreezy Chakras zip. Contains unfinished Ye and <PERSON> vocals. Only the adlibs stem is avaliable.", "length": "204.64", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/15fcb91e66a01533f631dd2123c47cae", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/15fcb91e66a01533f631dd2123c47cae\", \"key\": \"???\", \"title\": \"??? [V2]\", \"artists\": \"(feat. <PERSON><PERSON>ons & Francis and the Lights)\", \"aliases\": [\"Good Things Don't Last\"], \"description\": \"Unknown track <PERSON><PERSON> recorded adlibs for. Found in the Dreezy Chakras zip. Contains unfinished <PERSON> and <PERSON> vocals. Only the adlibs stem is avaliable.\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"0cfc0c5394be070f2130c2ecd4fe5b9e\", \"url\": \"https://api.pillowcase.su/api/download/0cfc0c5394be070f2130c2ecd4fe5b9e\", \"size\": \"4 MB\", \"duration\": 204.64}", "aliases": ["Good Things Don't Last"], "size": "4 MB"}, {"id": "-37", "name": "???", "artists": [], "producers": ["???"], "notes": "OG Filename: <PERSON> Ben 3\nGAJ 2018 Instrumental. Leaked as a bonus for the blind \"Let It Go\" groupbuy. Originates from Hitler era, as the file is dated 2017.", "length": "99.29", "fileDate": 17394912, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/841f40d559145b80cd85203f1d79e870", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/841f40d559145b80cd85203f1d79e870\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(prod. ???)\", \"aliases\": [\"Head Up\"], \"description\": \"OG Filename: Chance Ben 3\\nGAJ 2018 Instrumental. Leaked as a bonus for the blind \\\"Let It Go\\\" groupbuy. Originates from Hitler era, as the file is dated 2017.\", \"date\": 17394912, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"b0e6b3483fdcfdb425b64e487e7780b2\", \"url\": \"https://api.pillowcase.su/api/download/b0e6b3483fdcfdb425b64e487e7780b2\", \"size\": \"2.31 MB\", \"duration\": 99.29}", "aliases": ["Head Up"], "size": "2.31 MB"}, {"id": "-38", "name": "??? [V2]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "Track that was likely made in the same session as <PERSON><PERSON>, as it contains a simillar vibe and the same drum samples. Contains mumble vocals from <PERSON> and <PERSON>, as well as adlibs from <PERSON><PERSON>. Only the adlibs stem is avaliable.", "length": "219.17", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/7ee8bb419d38f6d82329b57aebf81cbe", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7ee8bb419d38f6d82329b57aebf81cbe\", \"key\": \"???\", \"title\": \"??? [V2]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"aliases\": [\"I Feel Everything\"], \"description\": \"Track that was likely made in the same session as <PERSON><PERSON>, as it contains a simillar vibe and the same drum samples. Contains mumble vocals from <PERSON> and <PERSON>, as well as adlibs from <PERSON><PERSON>. Only the adlibs stem is avaliable.\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"f162257f06b377f6327fd846c12565ce\", \"url\": \"https://api.pillowcase.su/api/download/f162257f06b377f6327fd846c12565ce\", \"size\": \"4.23 MB\", \"duration\": 219.17}", "aliases": ["I Feel Everything"], "size": "4.23 MB"}, {"id": "-39", "name": "???", "artists": [], "producers": [], "notes": "Played during the 2018 GAJ sessions. Lyrics were later reused during a scene in 500 Days In UCLA.", "length": "189.23", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/1cc1e18afbce05aaec4778241763b91a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1cc1e18afbce05aaec4778241763b91a\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"<PERSON> The Grouch\"], \"description\": \"Played during the 2018 GAJ sessions. Lyrics were later reused during a scene in 500 Days In UCLA.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"c5dfe44e010854ce90da0f2e7afdbe90\", \"url\": \"https://api.pillowcase.su/api/download/c5dfe44e010854ce90da0f2e7afdbe90\", \"size\": \"2.24 MB\", \"duration\": 189.23}", "aliases": ["<PERSON>"], "size": "2.24 MB"}, {"id": "-40", "name": "???", "artists": [], "producers": [], "notes": "Track played twice in the Dreezy Chakras zip, randomly interupting <PERSON><PERSON>. Unknown if <PERSON> recorded, or if it was just <PERSON> showing off his track.", "length": "91.32", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/212e88b5fd3760b4e4206d817499983a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/212e88b5fd3760b4e4206d817499983a\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Park\"], \"description\": \"Track played twice in the Dreezy Chakras zip, randomly interupting <PERSON><PERSON>. Unknown if <PERSON> recorded, or if it was just <PERSON> showing off his track.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"66bf2c44e6da659a40e8d1ebd53a78f6\", \"url\": \"https://api.pillowcase.su/api/download/66bf2c44e6da659a40e8d1ebd53a78f6\", \"size\": \"2.19 MB\", \"duration\": 91.32}", "aliases": ["Park"], "size": "2.19 MB"}, {"id": "-41", "name": "???", "artists": [], "producers": [], "notes": "Track played twice in the Dreezy Chakras zip, randomly interupting <PERSON><PERSON>. Unknown if <PERSON> recorded, or if it was just <PERSON> showing off his track.", "length": "44.59", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/d20d3fdc1a68d5671f79058095d80b83", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d20d3fdc1a68d5671f79058095d80b83\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Park\"], \"description\": \"Track played twice in the Dreezy Chakras zip, randomly interupting <PERSON><PERSON>. Unknown if <PERSON> recorded, or if it was just <PERSON> showing off his track.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"2308524482b93033838a4bbace3df3b0\", \"url\": \"https://api.pillowcase.su/api/download/2308524482b93033838a4bbace3df3b0\", \"size\": \"1.44 MB\", \"duration\": 44.59}", "aliases": ["Park"], "size": "1.44 MB"}, {"id": "-42", "name": "???", "artists": [], "producers": [], "notes": "Track played in the Dreezy Chakras zip. Unknown if <PERSON> recorded, or if it was just <PERSON> showing off his track.", "length": "56.27", "fileDate": "", "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/9e5edf6971309b45cf2c6ed6086c88cb", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/9e5edf6971309b45cf2c6ed6086c88cb\", \"key\": \"???\", \"title\": \"???\", \"aliases\": [\"Short Fuse\"], \"description\": \"Track played in the Dreezy Chakras zip. Unknown if <PERSON> recorded, or if it was just <PERSON> showing off his track.\", \"date\": null, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"86798f86b0847f7818d83d1608e9a5d3\", \"url\": \"https://api.pillowcase.su/api/download/86798f86b0847f7818d83d1608e9a5d3\", \"size\": \"1.62 MB\", \"duration\": 56.27}", "aliases": ["Short Fuse"], "size": "1.62 MB"}, {"id": "-43", "name": "??? [V1]", "artists": [], "producers": [], "notes": "Version of the track that <PERSON><PERSON> recorded his adlibs on. Can be heard heavily in the bleed.", "length": "211.43", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/055c9da203febeecd7552cbc03af89cd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/055c9da203febeecd7552cbc03af89cd\", \"key\": \"???\", \"title\": \"??? [V1]\", \"aliases\": [\"Timeout\"], \"description\": \"Version of the track that <PERSON><PERSON> recorded his adlibs on. Can be heard heavily in the bleed.\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"6c352e120b62f8cc240cee18c6293286\", \"url\": \"https://api.pillowcase.su/api/download/6c352e120b62f8cc240cee18c6293286\", \"size\": \"2.42 MB\", \"duration\": 211.43}", "aliases": ["Timeout"], "size": "2.42 MB"}, {"id": "-44", "name": "??? [V1]", "artists": [], "producers": [], "notes": "Version of the track that <PERSON><PERSON> recorded his adlibs on. Can be heard heavily in the bleed.", "length": "211.46", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/585df6d4a34e438adde91c10d9109510", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/585df6d4a34e438adde91c10d9109510\", \"key\": \"???\", \"title\": \"??? [V1]\", \"aliases\": [\"Timeout\"], \"description\": \"Version of the track that <PERSON><PERSON> recorded his adlibs on. Can be heard heavily in the bleed.\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"63f9b65f8ee5e8ee858d11a40ef5ad3f\", \"url\": \"https://api.pillowcase.su/api/download/63f9b65f8ee5e8ee858d11a40ef5ad3f\", \"size\": \"2.42 MB\", \"duration\": 211.46}", "aliases": ["Timeout"], "size": "2.42 MB"}, {"id": "-45", "name": "??? [V1]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "Freestyle recording during the big Good Ass Job recording session with <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and others. Mostly mumble freestyle, only file that leaked is the Ant Clemons mic stem so file where the bleed is made louder is linked alongside the unaltered song.", "length": "211.43", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/055c9da203febeecd7552cbc03af89cd", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/055c9da203febeecd7552cbc03af89cd\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"aliases\": [\"Timeout\"], \"description\": \"Freestyle recording during the big Good Ass Job recording session with <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and others. Mostly mumble freestyle, only file that leaked is the Ant Clemons mic stem so file where the bleed is made louder is linked alongside the unaltered song.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"6c352e120b62f8cc240cee18c6293286\", \"url\": \"https://api.pillowcase.su/api/download/6c352e120b62f8cc240cee18c6293286\", \"size\": \"2.42 MB\", \"duration\": 211.43}", "aliases": ["Timeout"], "size": "2.42 MB"}, {"id": "-46", "name": "??? [V1]", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "Freestyle recording during the big Good Ass Job recording session with <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and others. Mostly mumble freestyle, only file that leaked is the Ant Clemons mic stem so file where the bleed is made louder is linked alongside the unaltered song.", "length": "211.46", "fileDate": 17174592, "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/585df6d4a34e438adde91c10d9109510", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/585df6d4a34e438adde91c10d9109510\", \"key\": \"???\", \"title\": \"??? [V1]\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"aliases\": [\"Timeout\"], \"description\": \"Freestyle recording during the big Good Ass Job recording session with <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and others. Mostly mumble freestyle, only file that leaked is the Ant Clemons mic stem so file where the bleed is made louder is linked alongside the unaltered song.\", \"date\": 17174592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"63f9b65f8ee5e8ee858d11a40ef5ad3f\", \"url\": \"https://api.pillowcase.su/api/download/63f9b65f8ee5e8ee858d11a40ef5ad3f\", \"size\": \"2.42 MB\", \"duration\": 211.46}", "aliases": ["Timeout"], "size": "2.42 MB"}, {"id": "-47", "name": "??? [V2]", "artists": ["???", "<PERSON><PERSON>"], "producers": [], "notes": "Unknown track <PERSON><PERSON> recorded adlibs for. Has a simillar gospel sample to \"No Problem\". Found in the Dreezy Chakras zip. Has an unknown mumble feature and no Ye vocals.", "length": "250.57", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Lossless", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/59b7310b45f4fe85a69684de1f19fbc7", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/59b7310b45f4fe85a69684de1f19fbc7\", \"key\": \"???\", \"title\": \"??? [V2]\", \"artists\": \"(feat. ??? & <PERSON><PERSON>)\", \"aliases\": [\"Rise Up\"], \"description\": \"Unknown track <PERSON><PERSON> recorded adlibs for. Has a simillar gospel sample to \\\"No Problem\\\". Found in the Dreezy Chakras zip. Has an unknown mumble feature and no Ye vocals.\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"a1e7f2cdc23f1c87e963f9cf1b9092c1\", \"url\": \"https://api.pillowcase.su/api/download/a1e7f2cdc23f1c87e963f9cf1b9092c1\", \"size\": \"4.73 MB\", \"duration\": 250.57}", "aliases": ["Rise Up"], "size": "4.73 MB"}, {"id": "-48", "name": "???", "artists": ["<PERSON><PERSON>"], "producers": [], "notes": "Track that was likely made in the same session as <PERSON><PERSON>, as it contains a simillar vibe and the same drum crash sample. Contains <PERSON> and <PERSON><PERSON> freestyle vocals, but not any Ye.", "length": "1086.88", "fileDate": "", "leakDate": "", "availableLength": "Full", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/c32251895863b5033f78b258c4772287", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c32251895863b5033f78b258c4772287\", \"key\": \"???\", \"title\": \"???\", \"artists\": \"(feat. <PERSON><PERSON>)\", \"aliases\": [\"You Were My\"], \"description\": \"Track that was likely made in the same session as <PERSON><PERSON>, as it contains a simillar vibe and the same drum crash sample. Contains <PERSON> and <PERSON><PERSON> freestyle vocals, but not any Ye.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"f3b7ceea5236f356d6df637574065f67\", \"url\": \"https://api.pillowcase.su/api/download/f3b7ceea5236f356d6df637574065f67\", \"size\": \"18.1 MB\", \"duration\": 1086.88}", "aliases": ["You Were My"], "size": "18.1 MB"}, {"id": "-49", "name": "???", "artists": [], "producers": [], "notes": "OG Filename: K<PERSON> x Chance Liked - 2 REF - 09.01.18\nFree<PERSON> made during a recording session with <PERSON> the Rapper. Has no Chance vocals.", "length": "51.72", "fileDate": 16972416, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/6a37fc3bdd0366635997478c439c9ec8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/6a37fc3bdd0366635997478c439c9ec8\", \"key\": \"???\", \"title\": \"???\", \"description\": \"OG Filename: KW x Chance Liked - 2 REF - 09.01.18\\nFreestyle made during a recording session with <PERSON> the Rapper. Has no Chance vocals.\", \"date\": 16972416, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"810efec1bee58b4e866977a052e83cb2\", \"url\": \"https://api.pillowcase.su/api/download/810efec1bee58b4e866977a052e83cb2\", \"size\": \"1.55 MB\", \"duration\": 51.72}", "aliases": [], "size": "1.55 MB"}, {"id": "-50", "name": "???", "artists": [], "producers": [], "notes": "Chance vocals are L<PERSON> but <PERSON><PERSON><PERSON>'s seem to be HQ. Era is unconfirmed but most likely from the Chicago Sessions. Was being sold on TheSource.", "length": "11.7", "fileDate": 16124832, "leakDate": "", "availableLength": "Snippet", "quality": "High Quality", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/56041705c7245f636f74564766bbee7c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/56041705c7245f636f74564766bbee7c\", \"key\": \"???\", \"title\": \"???\", \"description\": \"Chance vocals are LQ but <PERSON><PERSON><PERSON>'s seem to be HQ. Era is unconfirmed but most likely from the Chicago Sessions. Was being sold on TheSource.\", \"date\": 16124832, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"cc310d21c0a7c6db869986054f491f10\", \"url\": \"https://api.pillowcase.su/api/download/cc310d21c0a7c6db869986054f491f10\", \"size\": \"912 kB\", \"duration\": 11.7}", "aliases": [], "size": "912 kB"}, {"id": "oops", "name": "Lil Yachty - OOPS [V2]", "artists": ["Kanye West", "<PERSON> the Rapper"], "producers": ["Polo Boy <PERSON>"], "notes": "Low quality recording of <PERSON><PERSON><PERSON> recording vocals over the open verse for the Lil Yachty song \"<PERSON>OPS\" with the <PERSON> verse. Could be a remix of the track due to the song releasing months before <PERSON><PERSON><PERSON> and <PERSON> started working with each other. Found in the Dreezy \"Chakras\" session.", "length": "185.6", "fileDate": 17174592, "leakDate": "", "availableLength": "Partial", "quality": "Recording", "links": [], "eraId": "good-ass-job", "originalUrl": "https://pillowcase.su/f/be1530b9123aeab558588ecbc79283d6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/be1530b9123aeab558588ecbc79283d6\", \"key\": \"OOPS\", \"title\": \"<PERSON> Yachty - OOPS [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON> the Rapper) (prod. <PERSON>)\", \"description\": \"Low quality recording of <PERSON><PERSON><PERSON> recording vocals over the open verse for the Lil Yachty song \\\"OOPS\\\" with the Chance verse. Could be a remix of the track due to the song releasing months before <PERSON><PERSON><PERSON> and <PERSON> started working with each other. Found in the Dreezy \\\"Chakras\\\" session.\", \"date\": 17174592, \"available\": [\"Partial\", \"rgb(255, 255, 255)\", \"rgb(191, 144, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"id\": \"ea835b8e71367437c880c56127b4f8a9\", \"url\": \"https://api.pillowcase.su/api/download/ea835b8e71367437c880c56127b4f8a9\", \"size\": \"3.69 MB\", \"duration\": 185.6}", "aliases": [], "size": "3.69 MB"}]}