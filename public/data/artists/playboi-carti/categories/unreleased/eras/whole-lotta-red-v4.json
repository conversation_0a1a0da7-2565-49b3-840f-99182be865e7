{"id": "whole-lotta-red-v4", "name": "Whole Lotta Red [V4]", "description": "In late 2020, <PERSON><PERSON> would shift the sound of Whole Lotta Red into being an aggressive ode to punk rock aesthetics, while maintaining the repetitive vocal style that made his previous work so loved. The album was his biggest musical departure by far, forgoing most of the things that brought him success in the first place, such as production from <PERSON><PERSON><PERSON><PERSON>, or features from popular collaborators like <PERSON> or A$AP Rocky. Instead, the album would only see a handful of features, and would see production predominantly from F1LTHY & Art Dealer. Connecting to V3, on this album <PERSON><PERSON> talks about his acts violence, expressing how he felt when <PERSON><PERSON> died (grief to anger). On songs like On That Time, Stop Breathing and No Sl33p, he opens up on how he felt, displaying anger, sometimes sadness and vengence for his friend. <PERSON> would be the executive producer for the album, and it would see a release on Christmas Day, 2020.", "backgroundColor": "rgb(255, 255, 255)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17EppOBRNrO2o2BQD8Boi5YbCgosBE3mwNGQE70NHGh3YeFxzGSCDBqd3OfALwQeSO99SwDWOjGGWuoZd1kTTTvBnQywcaybXpXi8s4fTmmIJY18bSHYO0xcDWhVCpY?key=QrLRwW_6ASWi9mCdhUkgTQ", "tracks": [{"id": "go2damoon", "name": "Go2DaMoon [V4]", "artists": ["Ye"], "producers": ["Wheezy", "Boogz", "FNZ"], "notes": "OG Filename: Go 2 Moon (Boogz x FNZ edit) 147 BPM\nEarlier version of \"Go2DaMoon\". Features alternate production from Boogz & FNZ. Still features <PERSON><PERSON> <PERSON> verse from \"In Abundance\". Leaked on Sep 18, 2023 in the Yeezy Hub. Vocals are AI-isolated", "length": "2:13", "fileDate": 16949952, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://music.froste.lol/song/a783d8a5714e3007de28702c54f704db/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a783d8a5714e3007de28702c54f704db/play\", \"key\": \"Go2DaMoon\", \"title\": \"Go2DaMoon [V4]\", \"artists\": \"(feat. Ye) (prod. <PERSON><PERSON><PERSON>, <PERSON><PERSON>z & FNZ)\", \"aliases\": [\"Bipolar\", \"Go2Moon\", \"Go To The Moon\", \"Room\"], \"description\": \"OG Filename: Go 2 Moon (Boogz x FNZ edit) 147 BPM\\nEarlier version of \\\"Go2DaMoon\\\". Features alternate production from Boogz & FNZ. Still features <PERSON><PERSON> verse from \\\"In Abundance\\\". Leaked on Sep 18, 2023 in the Yeezy Hub. Vocals are AI-isolated\", \"date\": 16949952, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"b12e33d074d1cd0839117ecc3fcb79df\", \"url\": \"https://api.pillowcase.su/api/download/b12e33d074d1cd0839117ecc3fcb79df\", \"size\": \"2.19 MB\", \"duration\": 133.92}", "aliases": ["Bipolar", "Go2Moon", "Go To The Moon", "Room"], "size": "2.19 MB"}, {"id": "go2damoon-2", "name": "Go2DaMoon [V11]", "artists": ["Ye"], "producers": ["Wheezy"], "notes": "Go2DaMoon with different 808's from the WLR premiere.", "length": "", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "http://music.froste.lol/song/355a699e00aba49feb1e4e87a00c8109/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"http://music.froste.lol/song/355a699e00aba49feb1e4e87a00c8109/play\", \"key\": \"Go2DaMoon\", \"title\": \"Go2DaMoon [V11]\", \"artists\": \"(feat. <PERSON>) (prod. Wheezy)\", \"aliases\": [\"Bipolar\", \"Go2Moon\", \"Go To The Moon\", \"Room\"], \"description\": \"Go2DaMoon with different 808's from the WLR premiere.\", \"date\": null, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"]}", "aliases": ["Bipolar", "Go2Moon", "Go To The Moon", "Room"], "size": ""}, {"id": "proud-of-you", "name": "✨ Proud of You [V3]", "artists": ["<PERSON><PERSON> <PERSON>"], "producers": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "notes": "OG Filename: KAP G- CARTI\nContains a Kap G feature. <PERSON><PERSON> <PERSON> is said to have recorded his vocals sometime in December 2020.", "length": "3:36", "fileDate": 16722720, "leakDate": "", "labels": ["CD Quality", "OG File"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://music.froste.lol/song/d6223b5d0779e5dd6f0dd86b1616fb62/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/d6223b5d0779e5dd6f0dd86b1616fb62/play\", \"key\": \"Proud of You\", \"title\": \"\\u2728 Proud of You [V3]\", \"artists\": \"(feat. Kap <PERSON>) (prod. <PERSON><PERSON><PERSON> & <PERSON>) \", \"aliases\": [\"Pop\", \"Pac\"], \"description\": \"OG Filename: KAP G- CARTI\\nContains a Kap G feature. <PERSON><PERSON> <PERSON> is said to have recorded his vocals sometime in December 2020.\", \"date\": 16722720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c9450cc71cf15fd2996e267d5275bb8b\", \"url\": \"https://api.pillowcase.su/api/download/c9450cc71cf15fd2996e267d5275bb8b\", \"size\": \"3.5 MB\", \"duration\": 216.03}", "aliases": ["Pop", "Pac"], "size": "3.5 MB"}, {"id": "everything-is-mine", "name": "🏆 Everything Is Mine*", "artists": [], "producers": ["<PERSON><PERSON><PERSON>"], "notes": "A throwaway from the 'Whole Lotta Red' sessions and a community grail. According to <PERSON>, <PERSON><PERSON> wasn't feeling his verse, so he told him to cut it. Most likely lost.", "length": "0:17", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://music.froste.lol/song/35a4b99c9e5adeac0f0a6743b3fd286a/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/35a4b99c9e5adeac0f0a6743b3fd286a/play\", \"key\": \"Everything Is Mine*\", \"title\": \"\\ud83c\\udfc6 Everything Is Mine*\", \"artists\": \"(prod. <PERSON><PERSON><PERSON>)\", \"description\": \"A throwaway from the 'Whole Lotta Red' sessions and a community grail. According to <PERSON>, <PERSON><PERSON> wasn't feeling his verse, so he told him to cut it. Most likely lost.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"7d700ceb029ee3a52b4e7987ce34bf2a\", \"url\": \"https://api.pillowcase.su/api/download/7d700ceb029ee3a52b4e7987ce34bf2a\", \"size\": \"328 kB\", \"duration\": 17.78}", "aliases": [], "size": "328 kB"}, {"id": "everyday", "name": "🗑️ Everyday", "artists": [], "producers": ["DP Beatz"], "notes": "OG Filename: <PERSON><PERSON> Everyday\nA throwaway from the WLR sessions. Some lyrics have been reused on Go2DaMoon.", "length": "0:12", "fileDate": 16552512, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://music.froste.lol/song/db2f209b4c7c2d297c7e5b159cd9665e/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/db2f209b4c7c2d297c7e5b159cd9665e/play\", \"key\": \"Everyday\", \"title\": \"\\ud83d\\uddd1\\ufe0f Everyday\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: <PERSON><PERSON> Everyday\\nA throwaway from the WLR sessions. Some lyrics have been reused on Go2DaMoon.\", \"date\": 16552512, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"544daee256fe37b8b18446629c92d09a\", \"url\": \"https://api.pillowcase.su/api/download/544daee256fe37b8b18446629c92d09a\", \"size\": \"239 kB\", \"duration\": 12.22}", "aliases": [], "size": "239 kB"}, {"id": "go2damoon-6", "name": "Go2DaMoon [V3]", "artists": ["Ye"], "producers": ["Wheezy"], "notes": "Earlier version of \"Go2DaMoon\" previewed on <PERSON><PERSON><PERSON>'s Instagram. Contains the \"<PERSON>na talk my wife into a threeway\" line from \"In Abundance\", which was removed in the released version.", "length": "0:34", "fileDate": 16949952, "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://music.froste.lol/song/2de751aaf471f56fb816385b2af9f08c/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/2de751aaf471f56fb816385b2af9f08c/play\", \"key\": \"Go2DaMoon\", \"title\": \"Go2DaMoon [V3]\", \"artists\": \"(feat. <PERSON>) (prod. Wheezy)\", \"aliases\": [\"Bipolar\", \"Go2Moon\", \"Go To The Moon\", \"Room\"], \"description\": \"Earlier version of \\\"Go2DaMoon\\\" previewed on <PERSON><PERSON><PERSON>'s Instagram. Contains the \\\"<PERSON><PERSON> talk my wife into a threeway\\\" line from \\\"In Abundance\\\", which was removed in the released version.\", \"date\": 16949952, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"8acb69c22897b90a09c02ccb1ea39035\", \"url\": \"https://api.pillowcase.su/api/download/8acb69c22897b90a09c02ccb1ea39035\", \"size\": \"600 kB\", \"duration\": 34.78}", "aliases": ["Bipolar", "Go2Moon", "Go To The Moon", "Room"], "size": "600 kB"}, {"id": "go2damoon-7", "name": "Go2DaMoon [V7]", "artists": ["Ye"], "producers": ["Wheezy"], "notes": "A snippet of an early version of \"Go2DaMoon\" shared on TheSource in January 2021, still featuring the threeway line but with production closer to the final. Likely fake due to filtered vocals", "length": "0:11", "fileDate": "", "leakDate": "", "labels": ["CD Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://pillowcase.su/f/f49d2ecc2e7a1591e77faf59c496011b", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f49d2ecc2e7a1591e77faf59c496011b\", \"key\": \"Go2DaMoon\", \"title\": \"Go2DaMoon [V7]\", \"artists\": \"(feat. <PERSON>) (prod. Wheezy)\", \"aliases\": [\"Bipolar\", \"Go2Moon\", \"Go To The Moon\", \"Room\"], \"description\": \"A snippet of an early version of \\\"Go2DaMoon\\\" shared on TheSource in January 2021, still featuring the threeway line but with production closer to the final. Likely fake due to filtered vocals\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5a03ecdddb63909158b211a116bfefb0\", \"url\": \"https://api.pillowcase.su/api/download/5a03ecdddb63909158b211a116bfefb0\", \"size\": \"4.13 MB\", \"duration\": 11.96}", "aliases": ["Bipolar", "Go2Moon", "Go To The Moon", "Room"], "size": "4.13 MB"}, {"id": "rockstar-made", "name": "Rockstar Made [V1]", "artists": [], "producers": ["F1LTHY", "<PERSON>"], "notes": "Alternate mix of \"Rockstar Made\" with unused vocal takes & adlibs. Snippets can be briefly heard in a Joy Divizn interview.", "length": "", "fileDate": 16752960, "leakDate": "", "labels": ["Recording", "Snippet"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://www.youtube.com/watch?v=x42Q_CMnM4c&t=1492s", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://www.youtube.com/watch?v=x42Q_CMnM4c&t=1492s\", \"key\": \"Rockstar Made\", \"title\": \"Rockstar Made [V1]\", \"artists\": \"(prod. <PERSON><PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"Alternate mix of \\\"Rockstar Made\\\" with unused vocal takes & adlibs. Snippets can be briefly heard in a Joy Divizn interview.\", \"date\": 16752960, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "yad", "name": "Bermuda Yae - YAD (Remix)", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["DJ <PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "notes": "Remix of the Bermuda Yae track \"YAD\".", "length": "0:11", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://music.froste.lol/song/dac47dd01344652cddc275fe69a1b23a/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/dac47dd01344652cddc275fe69a1b23a/play\", \"key\": \"YAD (Remix)\", \"title\": \"<PERSON> Yae - YAD (Remix)\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. DJ <PERSON> & Le.<PERSON>)\", \"description\": \"Remix of the Bermuda Yae track \\\"YAD\\\".\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"2d7062e0dc6d7bd5a9fe184e947c9c9e\", \"url\": \"https://api.pillowcase.su/api/download/2d7062e0dc6d7bd5a9fe184e947c9c9e\", \"size\": \"231 kB\", \"duration\": 11.73}", "aliases": [], "size": "231 kB"}, {"id": "yad-10", "name": "Bermuda Yae - YAD (Remix)", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["DJ <PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "notes": "Remix of the Bermuda Yae track \"YAD\".", "length": "0:04", "fileDate": "", "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://music.froste.lol/song/a3a30abb890bda1c07eb20499e2d3a2d/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/a3a30abb890bda1c07eb20499e2d3a2d/play\", \"key\": \"YAD (Remix)\", \"title\": \"<PERSON> Yae - YAD (Remix)\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. DJ S<PERSON> & Le.V<PERSON>)\", \"description\": \"Remix of the Bermuda Yae track \\\"YAD\\\".\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"e02a6160e99ac125d29236bb31c6ef47\", \"url\": \"https://api.pillowcase.su/api/download/e02a6160e99ac125d29236bb31c6ef47\", \"size\": \"80.7 kB\", \"duration\": 4.68}", "aliases": [], "size": "80.7 kB"}, {"id": "rockstar-made-11", "name": "🥇 Rockstar Made [V2]", "artists": ["<PERSON>"], "producers": ["F1LTHY", "<PERSON>"], "notes": "A tweet from <PERSON><PERSON> implied that <PERSON> recorded a verse for WLR, but it didn't appear on the tracklist. In 2021, streamer <PERSON><PERSON> stated that <PERSON> originally appeared on \"Rockstar Made\"; he later played a snippet of the song during a livestream in the early hours of April 13, 2023, prompting an angry phonecall from <PERSON>'s manager, <PERSON>.", "length": "", "fileDate": 16813440, "leakDate": "", "labels": ["High Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://imgur.com/a/UQ77vRw", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://imgur.com/a/UQ77vRw\", \"key\": \"Rockstar Made\", \"title\": \"\\ud83e\\udd47 Rockstar Made [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON><PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"A tweet from <PERSON><PERSON> implied that <PERSON> recorded a verse for WLR, but it didn't appear on the tracklist. In 2021, streamer <PERSON><PERSON> stated that <PERSON> originally appeared on \\\"Rockstar Made\\\"; he later played a snippet of the song during a livestream in the early hours of April 13, 2023, prompting an angry phonecall from <PERSON>'s manager, <PERSON>.\", \"date\": 16813440, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"]}", "aliases": [], "size": ""}, {"id": "pissy-pamper", "name": "🥇 Young Nudy & <PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON> [V5]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON><PERSON>erre <PERSON>"], "notes": "<PERSON><PERSON> previewed <PERSON>'s verse on his Twitter and <PERSON><PERSON>. Likely was made for fun after the original was scrapped for sample issues. Potentially lost.", "length": "", "fileDate": 15778368, "leakDate": "", "labels": ["Low Quality", "Snippet"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://music.froste.lol/video/0225c2f5cff5ee43bf249b4544ae61c0/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/video/0225c2f5cff5ee43bf249b4544ae61c0/play\", \"key\": \"Pissy Pamper\", \"title\": \"\\ud83e\\udd47 <PERSON> Nudy & <PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON> [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON><PERSON>er<PERSON>)\", \"aliases\": [\"Kid Cudi\"], \"description\": \"<PERSON><PERSON> previewed <PERSON>'s verse on his Twitter and Tik Tok. Likely was made for fun after the original was scrapped for sample issues. Potentially lost.\", \"date\": 15778368, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"]}", "aliases": ["<PERSON>"], "size": ""}, {"id": "unlock-it", "name": "ABRA - Unlock It [V1]", "artists": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "producers": ["Boyz Noise"], "notes": "Original version of \"Unlock It\" which has a different mix, alternate production and a cut verse from <PERSON><PERSON><PERSON>.", "length": "3:25", "fileDate": 16797024, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://music.froste.lol/song/f8a3a6bde47181c3698365d7c56b4ed4/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/f8a3a6bde47181c3698365d7c56b4ed4/play\", \"key\": \"Unlock It\", \"title\": \"ABRA - Unlock It [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON><PERSON>) (prod. Boyz Noise)\", \"description\": \"Original version of \\\"Unlock It\\\" which has a different mix, alternate production and a cut verse from <PERSON><PERSON><PERSON> J<PERSON>\", \"date\": 16797024, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"3fe611b1240e874ccc7dcdc6abdc128a\", \"url\": \"https://api.pillowcase.su/api/download/3fe611b1240e874ccc7dcdc6abdc128a\", \"size\": \"3.32 MB\", \"duration\": 205.03}", "aliases": [], "size": "3.32 MB"}, {"id": "unlock-it-14", "name": "ABRA - Unlock It [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["Boyz Noise"], "notes": "Original mix of \"Unlock It\" <PERSON><PERSON> didn't clear due to his verse being slightly offbeat.", "length": "2:23", "fileDate": 16276032, "leakDate": "", "labels": ["CD Quality", "Full"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://music.froste.lol/song/80cf5d38e0d4cb9a109e7ce8097fc5c0/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/80cf5d38e0d4cb9a109e7ce8097fc5c0/play\", \"key\": \"Unlock It\", \"title\": \"ABRA - Unlock It [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. Boyz Noise)\", \"description\": \"Original mix of \\\"Unlock It\\\" <PERSON><PERSON> didn't clear due to his verse being slightly offbeat.\", \"date\": 16276032, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"6245df50c95b0510fa3fec64cbfe2740\", \"url\": \"https://api.pillowcase.su/api/download/6245df50c95b0510fa3fec64cbfe2740\", \"size\": \"2.35 MB\", \"duration\": 143.95}", "aliases": [], "size": "2.35 MB"}, {"id": "solo-dolo-pt-iv", "name": "<PERSON> - Solo Dolo Pt. IV [V1]", "artists": [], "producers": ["<PERSON>"], "notes": "OG Filename: 04 Solo Dolo Flip Open Hook and <PERSON><PERSON> Gm 167\nRef. track of <PERSON> (solo) and no <PERSON><PERSON>.", "length": "3:11", "fileDate": 17131392, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://music.froste.lol/song/80e5900038576036d86afd30e657a8d4/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/80e5900038576036d86afd30e657a8d4/play\", \"key\": \"Solo Dolo Pt. IV\", \"title\": \"<PERSON>udi - Solo Dolo Pt. IV [V1]\", \"artists\": \"(prod. <PERSON>)\", \"description\": \"OG Filename: 04 Solo Dolo Flip Open Hook and Verse Gm 167\\nRef. track of <PERSON> (solo) and no <PERSON><PERSON>.\", \"date\": 17131392, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"81cdc7de945a25be82826d287d86f984\", \"url\": \"https://api.pillowcase.su/api/download/81cdc7de945a25be82826d287d86f984\", \"size\": \"3.1 MB\", \"duration\": 191.35}", "aliases": [], "size": "3.1 MB"}, {"id": "solo-dolo-pt-iv-16", "name": "⭐ <PERSON>udi - Solo Dolo Pt. IV [V2]", "artists": ["<PERSON><PERSON><PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: carti x cudi <PERSON> (day1)\nPreviewed by <PERSON> during a show in Paris. Samples \"Solo Dolo\" by <PERSON>. Confirmed to be from Whole Lotta Red era by waterfalls. This song was scrapped from <PERSON><PERSON>'s album INSANO due to <PERSON><PERSON> not clearing the song. Password-protected file leaked on Sep 29, 2023. Seen on <PERSON><PERSON>'s compute and the password leaked after a successful GB revealing that all this time the password was \"BellyFat69!\"", "length": "3:11", "fileDate": 17131392, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://music.froste.lol/song/282f6c668c99db6e585041c0930e37b1/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/282f6c668c99db6e585041c0930e37b1/play\", \"key\": \"Solo Dolo Pt. IV\", \"title\": \"\\u2b50 Kid Cudi - Solo Dolo Pt. IV [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>)\", \"description\": \"OG Filename: carti x cudi <PERSON> (day1)\\nPreviewed by <PERSON> during a show in Paris. Samples \\\"Solo Dolo\\\" by <PERSON>. Confirmed to be from Whole Lotta Red era by waterfalls. This song was scrapped from <PERSON><PERSON>'s album INSANO due to <PERSON><PERSON> not clearing the song. Password-protected file leaked on Sep 29, 2023. Seen on <PERSON><PERSON>'s compute and the password leaked after a successful GB revealing that all this time the password was \\\"BellyFat69!\\\"\", \"date\": 17131392, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"8fed36e0d863d4080d152f9aa6a1f0cd\", \"url\": \"https://api.pillowcase.su/api/download/8fed36e0d863d4080d152f9aa6a1f0cd\", \"size\": \"3.1 MB\", \"duration\": 191.35}", "aliases": [], "size": "3.1 MB"}, {"id": "flex-up", "name": "✨ Lil Yachty - Flex Up [V1]", "artists": ["Future"], "producers": ["Southside", "ATL Jacob", "<PERSON><PERSON><PERSON>"], "notes": "OG Filename: pbc ref\nFirst bounce of flex up without <PERSON><PERSON> on it. Has only <PERSON><PERSON> and <PERSON>. Most likely a refferance for <PERSON> <PERSON><PERSON>, due to his absense on this version.", "length": "3:00", "fileDate": 16964640, "leakDate": "", "labels": ["Lossless", "OG File"], "links": [], "eraId": "whole-lotta-red-v4", "originalUrl": "https://music.froste.lol/song/78b32b708236672572fcfded60d9e48a/play", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://music.froste.lol/song/78b32b708236672572fcfded60d9e48a/play\", \"key\": \"Flex Up\", \"title\": \"\\u2728 <PERSON> Yachty - Flex Up [V1]\", \"artists\": \"(ref. <PERSON><PERSON><PERSON>) (feat. <PERSON>) (prod. <PERSON>, ATL Jacob & Pyrex)\", \"description\": \"OG Filename: pbc ref\\nFirst bounce of flex up without <PERSON><PERSON> on it. Has only <PERSON><PERSON> and <PERSON>. Most likely a refferance for <PERSON>, due to his absense on this version.\", \"date\": 16964640, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(22, 22, 29)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6f23737be602b48e5c0de983aa87dbbb\", \"url\": \"https://api.pillowcase.su/api/download/6f23737be602b48e5c0de983aa87dbbb\", \"size\": \"1.48 MB\", \"duration\": 180.02}", "aliases": [], "size": "1.48 MB"}]}