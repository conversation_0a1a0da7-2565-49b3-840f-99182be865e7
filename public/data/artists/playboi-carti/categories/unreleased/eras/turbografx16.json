{"id": "turbografx16", "name": "TurboGrafx16", "description": "Immediately after <PERSON><PERSON><PERSON> released The Life of Pablo, he announced a whole new album titled TurboGrafx16, intended to be released in the summer of 2016. <PERSON><PERSON><PERSON> intended to pursue a futuristic sound, wanting to incorporate video game samples into the record. However, work on the album was short-lived, as <PERSON><PERSON><PERSON> began touring in August 2016 and scrapped the concept entirely after being diagnosed as bipolar. The cover included for this era is unofficial, despite being popular among <PERSON><PERSON><PERSON> fans.", "backgroundColor": "rgb(48, 43, 74)", "coverImage": "https://lh7-rt.googleusercontent.com/sheetsz/AHOq17GlgTNtn73uZ2CmIFzcPaM2U8lv9YDWTlWlAc2mLRjH74AXGKwgdLShn5n50YHiDekUE7of_hh-VNNZa4Y0-yNEEL_RzTLpr7kQCFiWcGd7C2RHdwUPxO40rrTI9cqgB60y5UtKfB1RVQZZBw?key=vFIW87Iq3I6Uh5WJGSWtug", "tracks": [{"id": "bad-night", "name": "Bad Night [V13]", "artists": ["<PERSON> Thug"], "producers": ["DJDS"], "notes": "OG Filename: Bad Night DJDS Reference\nDJDS version of \"Bad Night\", from March 2016. Has stripped down instrumental.", "length": "226.91", "fileDate": 16646688, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/49d839573ed633160b2d3765fa22dbfc", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/49d839573ed633160b2d3765fa22dbfc\", \"key\": \"Bad Night\", \"title\": \"Bad Night [V13]\", \"artists\": \"(feat. <PERSON>hu<PERSON>) (prod. DJDS)\", \"aliases\": [\"Rap Tarantino\", \"Man Up\", \"Too Reckless\", \"Bad Guy\"], \"description\": \"OG Filename: Bad Night DJDS Reference\\nDJDS version of \\\"Bad Night\\\", from March 2016. Has stripped down instrumental.\", \"date\": 16646688, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"134813102dfeaac05b970578c7297353\", \"url\": \"https://api.pillowcase.su/api/download/134813102dfeaac05b970578c7297353\", \"size\": \"3.84 MB\", \"duration\": 226.91}", "aliases": ["<PERSON>", "Man Up", "Too Re<PERSON>ss", "Bad Guy"], "size": "3.84 MB"}, {"id": "bad-night-2", "name": "Bad Night [V14]", "artists": ["<PERSON> Thug"], "producers": ["Carnage", "Hudson Mohawke"], "notes": "OG Filename: Bad Night (ROUGH)\nTurboGrafx16 version produced by <PERSON> Carnage. This version is allegedly the only one that was intended for TurboGrafx16. The verses are finished and the hook remains the same as other versions. Leaked as part of a groupbuy.", "length": "200.8", "fileDate": 17038944, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/2f67b7daf469879f3fa526da3a23daa1", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2f67b7daf469879f3fa526da3a23daa1\", \"key\": \"Bad Night\", \"title\": \"Bad Night [V14]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON>nage & Hudson Mohawke)\", \"aliases\": [\"Rap Tarantino\", \"Man Up\"], \"description\": \"OG Filename: Bad Night (ROUGH)\\nTurboGrafx16 version produced by <PERSON>. This version is allegedly the only one that was intended for TurboGrafx16. The verses are finished and the hook remains the same as other versions. Leaked as part of a groupbuy.\", \"date\": 17038944, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"440a12ab81a20d23d180af80e3a54aae\", \"url\": \"https://api.pillowcase.su/api/download/440a12ab81a20d23d180af80e3a54aae\", \"size\": \"5.19 MB\", \"duration\": 200.8}", "aliases": ["<PERSON>", "Man Up"], "size": "5.19 MB"}, {"id": "capri-sun", "name": "<PERSON><PERSON> Sun [V15]", "artists": [], "producers": ["Carnage", "Hudson Mohawke"], "notes": "OG Filename: CAPRI SUN ROUGH V1\nLater version of <PERSON>'s version. Has a different drum pattern and sample on the verses alongside no <PERSON> Thug. Leaked as part of a groupbuy.", "length": "142.29", "fileDate": 17038944, "leakDate": "", "availableLength": "OG File", "quality": "High Quality", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/5e52213a4f1869771b5d45b3d4c39572", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5e52213a4f1869771b5d45b3d4c39572\", \"key\": \"Capri Sun\", \"title\": \"Capri Sun [V15]\", \"artists\": \"(prod. <PERSON>nage & Hudson Mohawke)\", \"aliases\": [\"Rap Tarantino\", \"Bad Night\", \"Man Up\"], \"description\": \"OG Filename: CAPRI SUN ROUGH V1\\nLater version of <PERSON>'s version. Has a different drum pattern and sample on the verses alongside no <PERSON> Thug. Leaked as part of a groupbuy.\", \"date\": 17038944, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"afccea7876a82cc397742ae0f6892b0d\", \"url\": \"https://api.pillowcase.su/api/download/afccea7876a82cc397742ae0f6892b0d\", \"size\": \"4.26 MB\", \"duration\": 142.29}", "aliases": ["<PERSON>", "Bad Night", "Man Up"], "size": "4.26 MB"}, {"id": "damn-come-on", "name": "Damn Come On [V1]", "artists": [], "producers": ["Plain Pat", "MIKE DEAN"], "notes": "OG Filename: 2 29 16 1 w reff vox\nKid Cudi mumble reference track. Has a more minimal beat. Originally leaked under the name \"<PERSON>\".", "length": "293.01", "fileDate": 16716672, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/5a9926e896f2a787bf0a19f5f1f9dce6", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/5a9926e896f2a787bf0a19f5f1f9dce6\", \"key\": \"Damn Come On\", \"title\": \"Damn Come On [V1]\", \"artists\": \"(ref. <PERSON>) (prod. <PERSON> Pat & MIKE DEAN)\", \"description\": \"OG Filename: 2 29 16 1 w reff vox\\nKid Cudi mumble reference track. Has a more minimal beat. Originally leaked under the name \\\"Maple\\\".\", \"date\": 16716672, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"eb673381926ef1ce30fd8ffe47721304\", \"url\": \"https://api.pillowcase.su/api/download/eb673381926ef1ce30fd8ffe47721304\", \"size\": \"6.67 MB\", \"duration\": 293.01}", "aliases": [], "size": "6.67 MB"}, {"id": "damn-come-on-5", "name": "✨ Damn Come On [V2]", "artists": ["<PERSON>"], "producers": ["Plain Pat", "MIKE DEAN"], "notes": "OG Filename: 3 1 16 1 Damn Come On\nHas mumble <PERSON><PERSON><PERSON> vocals. Has a somewhat more developed beat with more synths. Sold as \"All Eyes On Ye\", but the name was fake with the file being incorrectly bounced, missing <PERSON> vocals. However, the correct bounce leaked on Jan 1st, 2023.", "length": "295.04", "fileDate": 16725312, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/42bd3416a14fb22118aaa1f92d5b9673", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/42bd3416a14fb22118aaa1f92d5b9673\", \"key\": \"Damn Come On\", \"title\": \"\\u2728 Damn Come On [V2]\", \"artists\": \"(feat. <PERSON>) (prod. <PERSON> Pat & MIKE DEAN)\", \"description\": \"OG Filename: 3 1 16 1 Damn Come On\\nHas mumble Kanye vocals. Has a somewhat more developed beat with more synths. Sold as \\\"All Eyes On Ye\\\", but the name was fake with the file being incorrectly bounced, missing <PERSON> vocals. However, the correct bounce leaked on Jan 1st, 2023.\", \"date\": 16725312, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"4ccea8d1dc16f37f89b9c79863e4de41\", \"url\": \"https://api.pillowcase.su/api/download/4ccea8d1dc16f37f89b9c79863e4de41\", \"size\": \"6.7 MB\", \"duration\": 295.04}", "aliases": [], "size": "6.7 MB"}, {"id": "don-t-act-like-you-care", "name": "⭐ Post Malone - Don't Act Like You Care [V2]", "artists": ["Kanye West", "<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: Don't Act Like You Care (Super Rough 2) (feat - <PERSON><PERSON><PERSON>)\nShares the same features and a similar vibe with \"No Reason\", so it's probably same sessions too. Leaked after a groupbuy.", "length": "262.43", "fileDate": 15616800, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/8382b93f6e75f089e288e6140d68dc5a", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8382b93f6e75f089e288e6140d68dc5a\", \"key\": \"Don't Act Like You Care\", \"title\": \"\\u2b50 Post Malone - Don't Act Like You Care [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"Jealous\"], \"description\": \"OG Filename: Don't Act Like You Care (Super Rough 2) (feat - <PERSON><PERSON><PERSON>)\\nShares the same features and a similar vibe with \\\"No Reason\\\", so it's probably same sessions too. Leaked after a groupbuy.\", \"date\": 15616800, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"598d59d040c6f4d7a4ff0be31f714286\", \"url\": \"https://api.pillowcase.su/api/download/598d59d040c6f4d7a4ff0be31f714286\", \"size\": \"6.18 MB\", \"duration\": 262.43}", "aliases": ["<PERSON><PERSON><PERSON>"], "size": "6.18 MB"}, {"id": "hold-tight", "name": "⭐ Hold Tight [V3]", "artists": ["Migos", "<PERSON> Thug"], "producers": [], "notes": "Snippets were previewed by <PERSON> Thug and <PERSON><PERSON> shortly after the release of The Life Of Pablo, possibly making it one of the first songs recorded for TurboGrafx16. Formerly known as \"Black Tron\".", "length": "276.14", "fileDate": 14967072, "leakDate": "", "availableLength": "Full", "quality": "High Quality", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/af8e75cf2953974c3bae392cf7122af2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/af8e75cf2953974c3bae392cf7122af2\", \"key\": \"Hold Tight\", \"title\": \"\\u2b50 Hold Tight [V3]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON> Thug)\", \"description\": \"Snippets were previewed by <PERSON>hu<PERSON> and <PERSON><PERSON> shortly after the release of The Life Of Pablo, possibly making it one of the first songs recorded for TurboGrafx16. Formerly known as \\\"Black Tron\\\".\", \"date\": 14967072, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"8096437bf96b5f53c1f9f49b1dcf3330\", \"url\": \"https://api.pillowcase.su/api/download/8096437bf96b5f53c1f9f49b1dcf3330\", \"size\": \"6.4 MB\", \"duration\": 276.14}", "aliases": [], "size": "6.4 MB"}, {"id": "hold-tight-8", "name": "Hold Tight [V4]", "artists": ["Migos", "<PERSON> Thug"], "producers": [], "notes": "Snippet surfaced before the leak of V3. Only noticeable difference is that <PERSON><PERSON><PERSON>'s vocals are autotuned and reverbed.", "length": "", "fileDate": 14564448, "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/c492b8099a51ebd7503fe69cea451677", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c492b8099a51ebd7503fe69cea451677\", \"key\": \"Hold Tight\", \"title\": \"Hold Tight [V4]\", \"artists\": \"(feat. <PERSON><PERSON> & <PERSON> Thug)\", \"description\": \"Snippet surfaced before the leak of V3. Only noticeable difference is that <PERSON><PERSON><PERSON>'s vocals are autotuned and reverbed.\", \"date\": 14564448, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "no-man-s-sky", "name": "No Man's Sky", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: No Mans Sky MTS\nIn an interview with <PERSON><PERSON>, it was brought up that <PERSON><PERSON><PERSON> recorded on four songs titled \"ohmygod\", \"Zulu\", \"No Man's Sky\", and \"Forever\" during the TurboGrafx16 sessions. A LQ snippet was played by <PERSON><PERSON> on his Instagram, but it has been lost. A file called \"No Man's Sky (Snippit)\" was seen in the TurboGrafx16-era file list posted by Al<PERSON>. Beat was officially released by <PERSON><PERSON> on December 2nd, 2023 through his \"Spore\" website.", "length": "210.63", "fileDate": 17014752, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/68882a0b3e5ffbe89191d6a5d462606e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/68882a0b3e5ffbe89191d6a5d462606e\", \"key\": \"No Man's Sky\", \"title\": \"No Man's Sky\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: No Mans Sky MTS\\nIn an interview with <PERSON><PERSON>, it was brought up that <PERSON><PERSON><PERSON> recorded on four songs titled \\\"ohmygod\\\", \\\"Zulu\\\", \\\"No Man's Sky\\\", and \\\"Forever\\\" during the TurboGrafx16 sessions. A LQ snippet was played by <PERSON><PERSON> on his Instagram, but it has been lost. A file called \\\"No Man's Sky (Snippit)\\\" was seen in the TurboGrafx16-era file list posted by <PERSON><PERSON>. <PERSON> was officially released by <PERSON><PERSON> on December 2nd, 2023 through his \\\"Spore\\\" website.\", \"date\": 17014752, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"d6da5694edcc246917216a9078bd20be\", \"url\": \"https://api.pillowcase.su/api/download/d6da5694edcc246917216a9078bd20be\", \"size\": \"5.35 MB\", \"duration\": 210.63}", "aliases": [], "size": "5.35 MB"}, {"id": "100-years-now", "name": "100 Years Now [V1]", "artists": ["<PERSON> Malone", "<PERSON>"], "producers": ["<PERSON>"], "notes": "OG Filename: 100 Years Now F1 (Super Rough) (4.20.16)\nVersion is 1:50 longer than the other version that leaked and has a longer, more mumble verse from <PERSON>, along with <PERSON><PERSON><PERSON>'s full mumble verse. Leaked fully in early November 2018 by iTagen.", "length": "286.62", "fileDate": 15414624, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/8bd1a3fc22ca26d25162ca5912ba0a93", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/8bd1a3fc22ca26d25162ca5912ba0a93\", \"key\": \"100 Years Now\", \"title\": \"100 Years Now [V1]\", \"artists\": \"(feat. <PERSON> & <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"No Reason\"], \"description\": \"OG Filename: 100 Years Now F1 (Super Rough) (4.20.16)\\nVersion is 1:50 longer than the other version that leaked and has a longer, more mumble verse from <PERSON>, along with <PERSON><PERSON><PERSON>'s full mumble verse. Leaked fully in early November 2018 by iTagen.\", \"date\": 15414624, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"c2fb8bcaed6a8ee5d46ad0a9758cdc22\", \"url\": \"https://api.pillowcase.su/api/download/c2fb8bcaed6a8ee5d46ad0a9758cdc22\", \"size\": \"6.57 MB\", \"duration\": 286.62}", "aliases": ["No Reason"], "size": "6.57 MB"}, {"id": "no-reason", "name": "⭐ Post Malone - No Reason [V2]", "artists": ["Kanye West", "<PERSON>"], "producers": ["<PERSON>"], "notes": "Later version that doesn't include <PERSON><PERSON><PERSON>'s mumble verse, cut's down <PERSON>'s verse, and features alternate production. Most likely <PERSON>'s song at this point as <PERSON><PERSON><PERSON>'s only vocals that are still on this version are the hook, whereas <PERSON>'s verse was edited to have less mumble.", "length": "183.48", "fileDate": 15356736, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/0f29cefca648497065308fe52c203324", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/0f29cefca648497065308fe52c203324\", \"key\": \"No Reason\", \"title\": \"\\u2b50 Post Malone - No Reason [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON> & <PERSON>) (prod. <PERSON>)\", \"aliases\": [\"100 Years Now\"], \"description\": \"Later version that doesn't include <PERSON><PERSON><PERSON>'s mumble verse, cut's down <PERSON>'s verse, and features alternate production. Most likely <PERSON>'s song at this point as <PERSON><PERSON><PERSON>'s only vocals that are still on this version are the hook, whereas <PERSON>'s verse was edited to have less mumble.\", \"date\": 15356736, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"cfa983f3a4a2584347d4255b5d3483c4\", \"url\": \"https://api.pillowcase.su/api/download/cfa983f3a4a2584347d4255b5d3483c4\", \"size\": \"4.92 MB\", \"duration\": 183.48}", "aliases": ["100 Years Now"], "size": "4.92 MB"}, {"id": "ohmygod", "name": "ohmygod [V1]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "In an interview with <PERSON><PERSON>, it was brought up that <PERSON><PERSON><PERSON> recorded on four songs titled \"ohmygod\", \"Zulu\", \"No Man's Sky\", and \"Forever\" during the TurboGrafx16 sessions. This song was described as having cool glitched out drums, and is <PERSON><PERSON>'s favourite song that he produced. Beat was officially released by <PERSON><PERSON> on November 29th, 2023 through his \"Spore\" website.", "length": "96.26", "fileDate": 17012160, "leakDate": "", "availableLength": "Beat Only", "quality": "High Quality", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/d4f8b0ccc242583208d4fc0d1bf704aa", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/d4f8b0ccc242583208d4fc0d1bf704aa\", \"key\": \"ohmygod\", \"title\": \"ohmygod [V1]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"In an interview with <PERSON><PERSON>, it was brought up that <PERSON><PERSON><PERSON> recorded on four songs titled \\\"ohmygod\\\", \\\"Zulu\\\", \\\"No Man's Sky\\\", and \\\"Forever\\\" during the TurboGrafx16 sessions. This song was described as having cool glitched out drums, and is <PERSON><PERSON>'s favourite song that he produced. Beat was officially released by <PERSON><PERSON> on November 29th, 2023 through his \\\"Spore\\\" website.\", \"date\": 17012160, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"High Quality\", \"rgb(0, 0, 0)\", \"rgb(255, 193, 7)\"], \"id\": \"26b42682f9d4f6fa6547bc95f0a729db\", \"url\": \"https://api.pillowcase.su/api/download/26b42682f9d4f6fa6547bc95f0a729db\", \"size\": \"3.52 MB\", \"duration\": 96.26}", "aliases": [], "size": "3.52 MB"}, {"id": "ohmygod-13", "name": "ohmygod [V2]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "A way shorter version of \"ohmygod\" with different production. <PERSON> was officially released by <PERSON><PERSON> on November 29th, 2023 through his \"Spore\" website.", "length": "41.67", "fileDate": 17012160, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/3ef6a8013cabd7455ca100bf87fbd588", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/3ef6a8013cabd7455ca100bf87fbd588\", \"key\": \"ohmygod\", \"title\": \"ohmygod [V2]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"A way shorter version of \\\"ohmygod\\\" with different production. Beat was officially released by <PERSON><PERSON> on November 29th, 2023 through his \\\"Spore\\\" website.\", \"date\": 17012160, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"2c4ab004516943afeba992c5a4d3d71b\", \"url\": \"https://api.pillowcase.su/api/download/2c4ab004516943afeba992c5a4d3d71b\", \"size\": \"2.65 MB\", \"duration\": 41.67}", "aliases": [], "size": "2.65 MB"}, {"id": "sell-your-soul", "name": "Sell Your Soul [V2]", "artists": ["KIRBY"], "producers": [], "notes": "OG Filename: Sell Your Soul melody ref\nAssumed to be an interlude or an intro for the song \"Souls\". Leaked after a groupbuy.", "length": "62.64", "fileDate": 16506720, "leakDate": "", "availableLength": "OG File", "quality": "Low Quality", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/08e56ede50ac40a28a13156b83085699", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/08e56ede50ac40a28a13156b83085699\", \"key\": \"Sell Your Soul\", \"title\": \"Sell Your Soul [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Sell Your Soul melody ref\\nAssumed to be an interlude or an intro for the song \\\"Souls\\\". Leaked after a groupbuy.\", \"date\": 16506720, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Low Quality\", \"rgb(255, 255, 255)\", \"rgb(231, 0, 0)\"], \"id\": \"197f49cdae444a3a53ac28fe805e1732\", \"url\": \"https://api.pillowcase.su/api/download/197f49cdae444a3a53ac28fe805e1732\", \"size\": \"2.48 MB\", \"duration\": 62.64}", "aliases": [], "size": "2.48 MB"}, {"id": "survive", "name": "Survive", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "Reference track done by TheNightAftr made in the TurboGrafx16 sessions. Original snippet leaked August 11th, 2024. Leaked alongside its acapella & instrumental.", "length": "184.27", "fileDate": 17234208, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/1a8bece23b9d6a9187634ea68c71deb9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/1a8bece23b9d6a9187634ea68c71deb9\", \"key\": \"Survive\", \"title\": \"Survive\", \"artists\": \"(ref. TheNightAftr) (prod. <PERSON><PERSON>)\", \"description\": \"Reference track done by TheNightAftr made in the TurboGrafx16 sessions. Original snippet leaked August 11th, 2024. Leaked alongside its acapella & instrumental.\", \"date\": 17234208, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"254e33f7bd42f0b827a0101f8c0b6b55\", \"url\": \"https://api.pillowcase.su/api/download/254e33f7bd42f0b827a0101f8c0b6b55\", \"size\": \"4.93 MB\", \"duration\": 184.27}", "aliases": [], "size": "4.93 MB"}, {"id": "zulu", "name": "<PERSON><PERSON> [V3]", "artists": [], "producers": ["<PERSON><PERSON>"], "notes": "OG Filename: <PERSON><PERSON> For Kanye West 2\nPosted by <PERSON><PERSON> himself on Twitter. <PERSON> was officially released by <PERSON><PERSON> on December 9th, 2023 through his \"Spore\" website.", "length": "274.34", "fileDate": 17020800, "leakDate": "", "availableLength": "Beat Only", "quality": "CD Quality", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/edc8601f72542fb67330f3bfdab8e2a9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/edc8601f72542fb67330f3bfdab8e2a9\", \"key\": \"<PERSON>ulu\", \"title\": \"<PERSON><PERSON> [V3]\", \"artists\": \"(prod. <PERSON><PERSON>)\", \"description\": \"OG Filename: Zulu For Kanye West 2\\nPosted by <PERSON><PERSON> himself on Twitter. <PERSON> was officially released by <PERSON><PERSON> on December 9th, 2023 through his \\\"Spore\\\" website.\", \"date\": 17020800, \"available\": [\"Beat Only\", \"rgb(255, 255, 255)\", \"rgb(142, 124, 195)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"a0c30e2f68677733468914378fc961d1\", \"url\": \"https://api.pillowcase.su/api/download/a0c30e2f68677733468914378fc961d1\", \"size\": \"6.37 MB\", \"duration\": 274.34}", "aliases": [], "size": "6.37 MB"}, {"id": "pick-up-your-speed", "name": "<PERSON> - Pick Up Your Speed [V12]", "artists": ["Kanye West"], "producers": [], "notes": "OG FIlenames: Copy of Pick Up Your Speed.2 &\nKW - Auxillary Ref (5.19.15) \nUnofficial bounce of a session found in the \"Accelerate\" sessions, bounced by segaretro92. Is a rebounce of the May 19th, 2015 version, shown by how the same filename fills in when bouncing the session.", "length": "108.63", "fileDate": 17038080, "leakDate": "", "availableLength": "Full", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/c7f62065d441b1504a079f0f6b51e7a9", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c7f62065d441b1504a079f0f6b51e7a9\", \"key\": \"Pick Up Your Speed\", \"title\": \"<PERSON> - Pick Up Your Speed [V12]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON><PERSON><PERSON>)\", \"aliases\": [\"Accelerate\", \"Auxillary\"], \"description\": \"OG FIlenames: Copy of Pick Up Your Speed.2 &\\nKW - Auxillary Ref (5.19.15) \\nUnofficial bounce of a session found in the \\\"Accelerate\\\" sessions, bounced by segaretro92. Is a rebounce of the May 19th, 2015 version, shown by how the same filename fills in when bouncing the session.\", \"date\": 17038080, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"c0af65e5938c1a3f602e7b6b4909560f\", \"url\": \"https://api.pillowcase.su/api/download/c0af65e5938c1a3f602e7b6b4909560f\", \"size\": \"3.72 MB\", \"duration\": 108.63}", "aliases": ["Accelerate", "Auxillary"], "size": "3.72 MB"}, {"id": "pick-up-your-speed-18", "name": "<PERSON> - Pick Up Your Speed [V14]", "artists": ["Ty Dolla $ign"], "producers": [], "notes": "OG Filename: Pick Up Your Speed_Ref Vox_01\nOfficial bounce found in the \"Accelerate\" sessions", "length": "194.74", "fileDate": 17038080, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/4f313a919c201e3e28443c6e7460a0d2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/4f313a919c201e3e28443c6e7460a0d2\", \"key\": \"Pick Up Your Speed\", \"title\": \"<PERSON> - Pick Up Your Speed [V14]\", \"artists\": \"(ref. <PERSON><PERSON>) (feat. <PERSON> $ign)\", \"aliases\": [\"Accelerate\", \"Auxillary\"], \"description\": \"OG Filename: Pick Up Your Speed_Ref Vox_01\\nOfficial bounce found in the \\\"Accelerate\\\" sessions\", \"date\": 17038080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"44bd045e5fadc4bdc30e995eda9bba59\", \"url\": \"https://api.pillowcase.su/api/download/44bd045e5fadc4bdc30e995eda9bba59\", \"size\": \"5.1 MB\", \"duration\": 194.74}", "aliases": ["Accelerate", "Auxillary"], "size": "5.1 MB"}, {"id": "pick-up-your-speed-19", "name": "<PERSON> - Pick Up Your Speed [V15]", "artists": ["Ty Dolla $ign"], "producers": [], "notes": "OG Filename: Pick Up Your Speed_X Ref Vox_03\nOfficial bounce found in the \"Accelerate\" sessions.", "length": "130.62", "fileDate": 17038080, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/7a504fda5bf45097d3f89ea96463da6d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7a504fda5bf45097d3f89ea96463da6d\", \"key\": \"Pick Up Your Speed\", \"title\": \"<PERSON> - Pick Up Your Speed [V15]\", \"artists\": \"(feat. <PERSON> $ign)\", \"aliases\": [\"Accelerate\", \"Auxillary\"], \"description\": \"OG Filename: Pick Up Your Speed_X Ref Vox_03\\nOfficial bounce found in the \\\"Accelerate\\\" sessions.\", \"date\": 17038080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"39c920088e32693fee55ad3612cbd29b\", \"url\": \"https://api.pillowcase.su/api/download/39c920088e32693fee55ad3612cbd29b\", \"size\": \"4.07 MB\", \"duration\": 130.62}", "aliases": ["Accelerate", "Auxillary"], "size": "4.07 MB"}, {"id": "accelerate", "name": "<PERSON> - Accelerate [V16]", "artists": ["Ty Dolla $ign"], "producers": [], "notes": "OG Filename: Accelerate_X Ref Vox.01_01\nOfficial bounce found in the \"Accelerate\" sessions.", "length": "200.82", "fileDate": 17038080, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/58a8ac7e7bf07cb4dc9c3f239229a85c", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/58a8ac7e7bf07cb4dc9c3f239229a85c\", \"key\": \"Accelerate\", \"title\": \"<PERSON> - Accelerate [V16]\", \"artists\": \"(feat. <PERSON> $ign)\", \"aliases\": [\"Pick Up Your Speed\", \"Auxillary\"], \"description\": \"OG Filename: Accelerate_X Ref Vox.01_01\\nOfficial bounce found in the \\\"Accelerate\\\" sessions.\", \"date\": 17038080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3b593fef00a86ff3d0caf1500280456a\", \"url\": \"https://api.pillowcase.su/api/download/3b593fef00a86ff3d0caf1500280456a\", \"size\": \"5.19 MB\", \"duration\": 200.82}", "aliases": ["Pick Up Your Speed", "Auxillary"], "size": "5.19 MB"}, {"id": "accelerate-21", "name": "<PERSON> - Accelerate [V17]", "artists": ["Ty Dolla $ign"], "producers": [], "notes": "OG Filename: Accelerate_X Ref Vox.02_06\nOfficial bounce found in the \"Accelerate\" sessions.", "length": "177.2", "fileDate": 17038080, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/41cd559926cebfbdec6c8ab67e991077", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/41cd559926cebfbdec6c8ab67e991077\", \"key\": \"Accelerate\", \"title\": \"<PERSON> - Accelerate [V17]\", \"artists\": \"(feat. <PERSON> $ign)\", \"aliases\": [\"Pick Up Your Speed\", \"Auxillary\"], \"description\": \"OG Filename: Accelerate_X Ref Vox.02_06\\nOfficial bounce found in the \\\"Accelerate\\\" sessions.\", \"date\": 17038080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"5f4bbb8a6c3fe560a6347c3de2981d4d\", \"url\": \"https://api.pillowcase.su/api/download/5f4bbb8a6c3fe560a6347c3de2981d4d\", \"size\": \"4.82 MB\", \"duration\": 177.2}", "aliases": ["Pick Up Your Speed", "Auxillary"], "size": "4.82 MB"}, {"id": "accelerate-22", "name": "<PERSON> - Accelerate [V20]", "artists": ["Ty Dolla $ign"], "producers": [], "notes": "OG Filename: Accelerate_Final Rough_07\nOfficial bounce found in the \"Accelerate\" sessions. Closely matches the previous entry, but there is no fully accurate bounce of that version, so we can't be for sure if it's the same.", "length": "184.86", "fileDate": 17038080, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/cf13fc03565e99a01b1b99fd80d3323d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/cf13fc03565e99a01b1b99fd80d3323d\", \"key\": \"Accelerate\", \"title\": \"<PERSON> - Accelerate [V20]\", \"artists\": \"(feat. <PERSON> $ign)\", \"aliases\": [\"Pick Up Your Speed\", \"Auxillary\"], \"description\": \"OG Filename: Accelerate_Final Rough_07\\nOfficial bounce found in the \\\"Accelerate\\\" sessions. Closely matches the previous entry, but there is no fully accurate bounce of that version, so we can't be for sure if it's the same.\", \"date\": 17038080, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"d43754166055583c8ad41ffc121d0a7a\", \"url\": \"https://api.pillowcase.su/api/download/d43754166055583c8ad41ffc121d0a7a\", \"size\": \"4.94 MB\", \"duration\": 184.86}", "aliases": ["Pick Up Your Speed", "Auxillary"], "size": "4.94 MB"}, {"id": "tiimmy-turner", "name": "Desiigner - <PERSON><PERSON><PERSON> (Remix) [V1]", "artists": ["Kanye West"], "producers": ["<PERSON>", "<PERSON>", "Kanye West", "<PERSON><PERSON><PERSON><PERSON>", "MIKE DEAN"], "notes": "OG Filename: TT_BOAN_new MD parts_2 no strings\nVersion with a slightly different Kany<PERSON> verse.", "length": "144.12", "fileDate": 16034112, "leakDate": "", "availableLength": "OG File", "quality": "CD Quality", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/33112ecada40acc2ba90e89d3590f21f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/33112ecada40acc2ba90e89d3590f21f\", \"key\": \"<PERSON><PERSON><PERSON> (Remix)\", \"title\": \"<PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> (Remix) [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> & <PERSON>)\", \"description\": \"OG Filename: TT_BOAN_new MD parts_2 no strings\\nVersion with a slightly different Kanye verse.\", \"date\": 16034112, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"5330d6081f72768dd208468ab236952f\", \"url\": \"https://api.pillowcase.su/api/download/5330d6081f72768dd208468ab236952f\", \"size\": \"4.29 MB\", \"duration\": 144.12}", "aliases": [], "size": "4.29 MB"}, {"id": "i-hope", "name": "PARTYNEXTDOOR - I Hope", "artists": [], "producers": ["Kanye West", "40"], "notes": "Unreleased PARTYNEXTDOOR song that shares the same beat as \"Glow\". Unrelated to \"Glow\" in any way besides the beat. Snippets were released by PRIVATEFRIEND before the song eventually leaked in full.", "length": "171.48", "fileDate": 15986592, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/286001f0f37e4bd054360a7e4510db04", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/286001f0f37e4bd054360a7e4510db04\", \"key\": \"<PERSON> Hope\", \"title\": \"PARTYNEXTDOOR - <PERSON> Hope\", \"artists\": \"(prod. <PERSON><PERSON><PERSON> & 40)\", \"aliases\": [\"Glow\"], \"description\": \"Unreleased PARTYNEXTDOOR song that shares the same beat as \\\"Glow\\\". Unrelated to \\\"Glow\\\" in any way besides the beat. Snippets were released by PRIVATEFRIEND before the song eventually leaked in full.\", \"date\": 15986592, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"676ac93d0f763c6a1c2947509f57ee21\", \"url\": \"https://api.pillowcase.su/api/download/676ac93d0f763c6a1c2947509f57ee21\", \"size\": \"4.72 MB\", \"duration\": 171.48}", "aliases": ["Glow"], "size": "4.72 MB"}, {"id": "lovers-king", "name": "<PERSON><PERSON><PERSON> - <PERSON> King", "artists": [], "producers": ["<PERSON>", "JB3", "Kanye West"], "notes": "This song is on a tracklist for <PERSON><PERSON><PERSON>' scrapped album that was announced summer 2016. Could have been made in 2015, as <PERSON><PERSON><PERSON> said in 2022 he had \"one song\" produced by <PERSON><PERSON><PERSON> since 2014. Two snippets were posted by <PERSON><PERSON><PERSON>.", "length": "", "fileDate": "", "leakDate": "", "availableLength": "Snippet", "quality": "Recording", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/062972d496d103db78670bcfb343eee8", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/062972d496d103db78670bcfb343eee8\", \"key\": \"Lovers King\", \"title\": \"Theophilus London - Lovers King\", \"artists\": \"(prod. <PERSON>, <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>)\", \"description\": \"This song is on a tracklist for <PERSON><PERSON><PERSON>' scrapped album that was announced summer 2016. Could have been made in 2015, as <PERSON><PERSON><PERSON> said in 2022 he had \\\"one song\\\" produced by <PERSON><PERSON><PERSON> since 2014. Two snippets were posted by Theophilus.\", \"date\": null, \"available\": [\"Snippet\", \"rgb(255, 255, 255)\", \"rgb(153, 0, 0)\"], \"quality\": [\"Recording\", \"rgb(255, 255, 255)\", \"rgb(0, 0, 0)\"]}", "aliases": [], "size": ""}, {"id": "feel-me", "name": "Tyga - Feel Me [V1]", "artists": ["Kanye West"], "producers": ["Crakwav", "Sound M.O.B.", "Kanye West"], "notes": "Has one different line than the released version and slightly different production.", "length": "202.17", "fileDate": 16604352, "leakDate": "", "availableLength": "Full", "quality": "CD Quality", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/c3423c8fa4c0c3c490c4d941aa1ce53f", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/c3423c8fa4c0c3c490c4d941aa1ce53f\", \"key\": \"Feel Me\", \"title\": \"Tyga - Feel Me [V1]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>, Sound M.O.B. & Kany<PERSON> West)\", \"description\": \"Has one different line than the released version and slightly different production.\", \"date\": 16604352, \"available\": [\"Full\", \"rgb(255, 255, 255)\", \"rgb(7, 55, 99)\"], \"quality\": [\"CD Quality\", \"rgb(255, 255, 255)\", \"rgb(76, 175, 80)\"], \"id\": \"47c78f5e6c807137e313ff29e7a8ef35\", \"url\": \"https://api.pillowcase.su/api/download/47c78f5e6c807137e313ff29e7a8ef35\", \"size\": \"5.22 MB\", \"duration\": 202.17}", "aliases": [], "size": "5.22 MB"}, {"id": "feel-me-27", "name": "Tyga - Feel Me [V2]", "artists": ["Kanye West"], "producers": ["Crakwav", "Sound M.O.B.", "Kanye West"], "notes": "OG Filename: Feel Me (M5) - explicit (mastered ts)\n\"Feel Me\" mix 5 explicit.", "length": "199.96", "fileDate": 16604352, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/7273508bd256ccd3e2ab300c823c8c8d", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7273508bd256ccd3e2ab300c823c8c8d\", \"key\": \"Feel Me\", \"title\": \"Tyga - Feel Me [V2]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>, Sound M.O.B. & <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Feel Me (M5) - explicit (mastered ts)\\n\\\"Feel Me\\\" mix 5 explicit.\", \"date\": 16604352, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"429cd69077d9b4dab45aae186d41b8f2\", \"url\": \"https://api.pillowcase.su/api/download/429cd69077d9b4dab45aae186d41b8f2\", \"size\": \"5.18 MB\", \"duration\": 199.96}", "aliases": [], "size": "5.18 MB"}, {"id": "feel-me-28", "name": "Tyga - Feel Me [V3]", "artists": ["Kanye West"], "producers": ["Crakwav", "Sound M.O.B.", "Kanye West"], "notes": "OG Filename: Feel Me (M5) - clean (mastered ts)\n\"Feel Me\" clean master file five.", "length": "199.96", "fileDate": 16604352, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/f729e2d299dff60e1f5da7ccb17c5c71", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f729e2d299dff60e1f5da7ccb17c5c71\", \"key\": \"Feel Me\", \"title\": \"Tyga - Feel Me [V3]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>, Sound M.O.B. & <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Feel Me (M5) - clean (mastered ts)\\n\\\"Feel Me\\\" clean master file five.\", \"date\": 16604352, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"3c7389b9e16b6c85095d951db239b8e8\", \"url\": \"https://api.pillowcase.su/api/download/3c7389b9e16b6c85095d951db239b8e8\", \"size\": \"5.18 MB\", \"duration\": 199.96}", "aliases": [], "size": "5.18 MB"}, {"id": "feel-me-29", "name": "Tyga - Feel Me [V4]", "artists": ["Kanye West"], "producers": ["Crakwav", "Sound M.O.B.", "Kanye West"], "notes": "OG Filename: Feel Me (M5) - super clean (mastered ts)\n\"Feel Me\" super clean master file five.", "length": "199.96", "fileDate": 16604352, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/e797f1aaeca60ecb857b7c68812114a0", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/e797f1aaeca60ecb857b7c68812114a0\", \"key\": \"Feel Me\", \"title\": \"Tyga - Feel Me [V4]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>, Sound M.O.B. & <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Feel Me (M5) - super clean (mastered ts)\\n\\\"Feel Me\\\" super clean master file five.\", \"date\": 16604352, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"9daac8695b3630b79536c1bda68df96c\", \"url\": \"https://api.pillowcase.su/api/download/9daac8695b3630b79536c1bda68df96c\", \"size\": \"5.18 MB\", \"duration\": 199.96}", "aliases": [], "size": "5.18 MB"}, {"id": "feel-me-30", "name": "Tyga - Feel Me [V5]", "artists": ["Kanye West"], "producers": ["Crakwav", "Sound M.O.B.", "Kanye West"], "notes": "OG Filename: Feel Me (M6) - explicit (mastered ts)\n\"Feel Me\" explicit master file six.", "length": "199.96", "fileDate": 16604352, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/f25718dc04f03695b79d1c59665db9a4", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/f25718dc04f03695b79d1c59665db9a4\", \"key\": \"Feel Me\", \"title\": \"Tyga - Feel Me [V5]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>, Sound M.O.B. & <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Feel Me (M6) - explicit (mastered ts)\\n\\\"Feel Me\\\" explicit master file six.\", \"date\": 16604352, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"6b06e885f2fda5e96a99481fa4ded9df\", \"url\": \"https://api.pillowcase.su/api/download/6b06e885f2fda5e96a99481fa4ded9df\", \"size\": \"5.18 MB\", \"duration\": 199.96}", "aliases": [], "size": "5.18 MB"}, {"id": "feel-me-31", "name": "Tyga - Feel Me [V6]", "artists": ["Kanye West"], "producers": ["Crakwav", "Sound M.O.B.", "Kanye West"], "notes": "OG Filename: Feel Me (M6) - clean (mastered ts)\n\"Feel Me\" clean master file six.", "length": "199.96", "fileDate": 16604352, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/2506792b3d0b03e45b392410aa057cdf", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/2506792b3d0b03e45b392410aa057cdf\", \"key\": \"Feel Me\", \"title\": \"Tyga - Feel Me [V6]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>, Sound M.O.B. & <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Feel Me (M6) - clean (mastered ts)\\n\\\"Feel Me\\\" clean master file six.\", \"date\": 16604352, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"586549c34e19e0b352462f7bb9c0525c\", \"url\": \"https://api.pillowcase.su/api/download/586549c34e19e0b352462f7bb9c0525c\", \"size\": \"5.18 MB\", \"duration\": 199.96}", "aliases": [], "size": "5.18 MB"}, {"id": "feel-me-32", "name": "Tyga - Feel Me [V7]", "artists": ["Kanye West"], "producers": ["Crakwav", "Sound M.O.B.", "Kanye West"], "notes": "OG Filename: Feel Me (M6) - super clean (mastered ts)\n\"Feel Me\" super clean master file six.", "length": "199.96", "fileDate": 16604352, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/695452e3cbf6ca0f73f0e03eb0fc50a2", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/695452e3cbf6ca0f73f0e03eb0fc50a2\", \"key\": \"Feel Me\", \"title\": \"Tyga - Feel Me [V7]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod<PERSON> <PERSON>, Sound M.O.B. & <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Feel Me (M6) - super clean (mastered ts)\\n\\\"Feel Me\\\" super clean master file six.\", \"date\": 16604352, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"17e41e279f93888890c77469cd599552\", \"url\": \"https://api.pillowcase.su/api/download/17e41e279f93888890c77469cd599552\", \"size\": \"5.18 MB\", \"duration\": 199.96}", "aliases": [], "size": "5.18 MB"}, {"id": "feel-me-33", "name": "Tyga - Feel Me [V8]", "artists": ["Kanye West"], "producers": ["Crakwav", "Sound M.O.B.", "Kanye West"], "notes": "OG Filename: Feel Me (M7) - super clean (mastered ts)\n\"Feel Me\" super clean master file seven.", "length": "199.96", "fileDate": 16604352, "leakDate": "", "availableLength": "OG File", "quality": "Lossless", "links": [], "eraId": "turbografx16", "originalUrl": "https://pillowcase.su/f/7f39d13a0026454fa3ee497dd7d2ca0e", "originalContent": "{\"type\": \"track\", \"originalUrl\": \"https://pillowcase.su/f/7f39d13a0026454fa3ee497dd7d2ca0e\", \"key\": \"Feel Me\", \"title\": \"Tyga - Feel Me [V8]\", \"artists\": \"(feat. <PERSON><PERSON><PERSON>) (prod. <PERSON>, Sound M.O.B. & <PERSON><PERSON><PERSON>)\", \"description\": \"OG Filename: Feel Me (M7) - super clean (mastered ts)\\n\\\"Feel Me\\\" super clean master file seven.\", \"date\": 16604352, \"available\": [\"OG File\", \"rgb(255, 255, 255)\", \"rgb(42, 159, 102)\"], \"quality\": [\"Lossless\", \"rgb(243, 243, 243)\", \"rgb(69, 188, 255)\"], \"id\": \"7b91d161e0a8d03ee30f9ab2904e2ef1\", \"url\": \"https://api.pillowcase.su/api/download/7b91d161e0a8d03ee30f9ab2904e2ef1\", \"size\": \"5.18 MB\", \"duration\": 199.96}", "aliases": [], "size": "5.18 MB"}]}