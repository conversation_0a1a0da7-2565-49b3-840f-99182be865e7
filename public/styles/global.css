/* 全局样式定义 */

:root {
  --radius: 0.5rem;
  --background: #ffffff;
  --foreground: #252525;
  --card: #ffffff;
  --card-foreground: #252525;
  --popover: #ffffff;
  --popover-foreground: #252525;
  --primary: #1db954; /* Spotify green */
  --primary-foreground: #ffffff;
  --secondary: #f5f5f5;
  --secondary-foreground: #353535;
  --muted: #f5f5f5;
  --muted-foreground: #8e8e8e;
  --accent: #f5f5f5;
  --accent-foreground: #353535;
  --destructive: #e91429;
  --border: #ebebeb;
  --input: #ebebeb;
  --ring: #b5b5b5;
  --chart-1: #a5a5ff;
  --chart-2: #99c7ff;
  --chart-3: #65b2ff;
  --chart-4: #d3a5ff;
  --chart-5: #c4a5ff;
  --sidebar: #fcfcfc;
  --sidebar-foreground: #252525;
  --sidebar-primary: #353535;
  --sidebar-primary-foreground: #fcfcfc;
  --sidebar-accent: #f5f5f5;
  --sidebar-accent-foreground: #353535;
  --sidebar-border: #ebebeb;
  --sidebar-ring: #b5b5b5;
}

.dark {
  --background: #121212; /* Spotify background */
  --foreground: #ffffff;
  --card: #181818; /* Spotify card background */
  --card-foreground: #ffffff;
  --popover: #282828; /* Spotify popover */
  --popover-foreground: #ffffff;
  --primary: #16a34a; /* Softer green for dark theme */
  --primary-foreground: #000000;
  --secondary: #333333; /* Spotify secondary */
  --secondary-foreground: #ffffff;
  --muted: #282828; /* Spotify muted */
  --muted-foreground: #b3b3b3; /* Spotify secondary text */
  --accent: #535353; /* Spotify accent */
  --accent-foreground: #ffffff;
  --destructive: #e91429; /* Spotify red */
  --border: rgba(255, 255, 255, 0.1);
  --input: rgba(255, 255, 255, 0.1);
  --ring: rgba(255, 255, 255, 0.3);
  --chart-1: #16a34a; /* Softer green for dark theme */
  --chart-2: #4687d6; /* Spotify blue */
  --chart-3: #8c1932; /* Spotify red */
  --chart-4: #af2896; /* Spotify purple */
  --chart-5: #eb9b34; /* Spotify orange */
  --sidebar: #000000; /* Spotify sidebar */
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #16a34a; /* Softer green for dark theme */
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #282828; /* Spotify sidebar accent */
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --sidebar-ring: rgba(255, 255, 255, 0.3);
}

/* 基础样式 */
* {
  border-color: var(--border);
  outline-color: var(--ring);
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "ss01", "ss02", "cv01", "cv02";
}

/* Spotify风格滚动条 */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  border: 3px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
  border: 3px solid transparent;
  background-clip: content-box;
}

/* 改进的焦点样式 */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* 平滑过渡 */
a, button {
  transition: all 0.2s ease;
}

/* RTL支持 */
.rtl {
  direction: rtl;
  text-align: right;
}

/* 居中内容设计 */
.container {
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}
