<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download Test</title>
    <style>
        body { background: #000; color: #fff; font-family: Arial, sans-serif; padding: 20px; }
        .test-track { 
            background: #1a1a1a; 
            padding: 20px; 
            margin: 10px 0; 
            border-radius: 8px; 
            border: 1px solid #333;
        }
        .download-btn { 
            background: #22c55e; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 5px;
        }
        .download-btn:hover { background: #16a34a; }
        .info { color: #888; font-size: 12px; margin-top: 10px; }
    </style>
</head>
<body>
    <h1>Download Function Test</h1>
    
    <!-- Test Case 1: Ye Best-of track with pillowcase.su URL -->
    <div class="test-track" 
         data-track-id="ye-home-v2"
         data-track-title="Home [V2]"
         data-track-audio="https://api.pillowcase.su/download/ye/home-v2"
         data-track-original-url="https://pillowcase.su/ye/home-v2.mp3"
         data-track-original-content='{"url":"https://pillowcase.su/ye/home-v2.mp3"}'>
        <h3>Test 1: Ye - Home [V2] (pillowcase.su)</h3>
        <button class="download-btn" onclick="testDownload(this.parentElement)">Download</button>
        <div class="info">
            API URL: https://api.pillowcase.su/download/ye/home-v2<br>
            Original URL: https://pillowcase.su/ye/home-v2.mp3
        </div>
    </div>

    <!-- Test Case 2: Carti track with music.froste.lol URL -->
    <div class="test-track" 
         data-track-id="carti-bianca-v1"
         data-track-title="BIANCA [V1]"
         data-track-original-url="https://music.froste.lol/carti/BIANCA%20%5BV1%5D.mp3"
         data-track-original-content='{"url":"https://music.froste.lol/carti/BIANCA%20%5BV1%5D.mp3"}'>
        <h3>Test 2: Carti - BIANCA [V1] (music.froste.lol)</h3>
        <button class="download-btn" onclick="testDownload(this.parentElement)">Download</button>
        <div class="info">
            Original URL: https://music.froste.lol/carti/BIANCA [V1].mp3
        </div>
    </div>

    <!-- Test Case 3: Multiple URLs -->
    <div class="test-track" 
         data-track-id="test-multi"
         data-track-title="Multi URL Test"
         data-track-audio="https://invalid-url.com/test.mp3"
         data-track-original-url="https://music.froste.lol/carti/BIANCA%20%5BV1%5D.mp3"
         data-track-original-content='{"url":"https://pillowcase.su/ye/home-v2.mp3"}'>
        <h3>Test 3: Multi URL Fallback Test</h3>
        <button class="download-btn" onclick="testDownload(this.parentElement)">Download</button>
        <div class="info">
            Should try invalid URL first, then fallback to working URLs
        </div>
    </div>

    <script src="/utils/newDownloader.js"></script>
    <script>
        // 初始化下载器
        const downloader = new NewDownloader();
        
        function testDownload(container) {
            console.log('Testing download for:', container.dataset.trackTitle);
            console.log('Container data:', container.dataset);
            
            // 模拟点击下载按钮
            const event = new Event('click');
            const downloadBtn = container.querySelector('.download-btn');
            downloader.handleDownload(event, downloadBtn);
        }
        
        // 添加一些调试信息
        console.log('Download test page loaded');
        console.log('NewDownloader available:', typeof NewDownloader !== 'undefined');
    </script>
</body>
</html>
