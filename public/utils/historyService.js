// 播放历史管理服务

// 最大历史记录数量
const MAX_HISTORY_ITEMS = 20;

// 获取播放历史
function getPlayHistory() {
  try {
    const historyJson = localStorage.getItem('playHistory');
    return historyJson ? JSON.parse(historyJson) : [];
  } catch (error) {
    console.error('Error getting play history:', error);
    return [];
  }
}

// 添加曲目到播放历史
function addToPlayHistory(track) {
  try {
    if (!track || !track.name) return;
    
    // 获取当前历史
    let history = getPlayHistory();
    
    // 移除相同曲目（如果存在）
    history = history.filter(item => 
      !(item.name === track.name && 
        JSON.stringify(item.artists) === JSON.stringify(track.artists))
    );
    
    // 添加到历史开头
    history.unshift({
      ...track,
      playedAt: new Date().toISOString()
    });
    
    // 限制历史长度
    if (history.length > MAX_HISTORY_ITEMS) {
      history = history.slice(0, MAX_HISTORY_ITEMS);
    }
    
    // 保存到本地存储
    localStorage.setItem('playHistory', JSON.stringify(history));
    
    // 触发历史更新事件
    const event = new CustomEvent('play-history-updated', {
      detail: { history }
    });
    document.dispatchEvent(event);
    
    return history;
  } catch (error) {
    console.error('Error adding to play history:', error);
    return [];
  }
}

// 清除播放历史
function clearPlayHistory() {
  try {
    localStorage.removeItem('playHistory');
    
    // 触发历史更新事件
    const event = new CustomEvent('play-history-updated', {
      detail: { history: [] }
    });
    document.dispatchEvent(event);
  } catch (error) {
    console.error('Error clearing play history:', error);
  }
}

// 暴露给全局
window.getPlayHistory = getPlayHistory;
window.addToPlayHistory = addToPlayHistory;
window.clearPlayHistory = clearPlayHistory;
