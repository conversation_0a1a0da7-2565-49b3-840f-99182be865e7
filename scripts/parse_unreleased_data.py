#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import json
import os
import re
from pathlib import Path
import html
import unicodedata

# 定义路径
BASE_DIR = Path('/Users/<USER>/overseas_website/TrackerHive')
CSV_FILE = BASE_DIR / 'data' / 'YeTrackerUnreleased.csv'
OUTPUT_DIR = BASE_DIR / 'public' / 'data' / 'artists' / 'ye'
CATEGORY_DIR = OUTPUT_DIR / 'categories' / 'unreleased'
ERAS_DIR = CATEGORY_DIR / 'eras'

# 确保输出目录存在
ERAS_DIR.mkdir(parents=True, exist_ok=True)

# 颜色映射 - 为每个时期分配一个背景色
ERA_COLORS = {
    "Before The College Dropout": "#8B0000",  # 深红色
    "The College Dropout": "#8B4513",  # 棕色
    "Late Registration": "#483D8B",  # 深紫色
    "Graduation": "#006400",  # 深绿色
    "808s & Heartbreak": "#4682B4",  # 钢蓝色
    "My Beautiful Dark Twisted Fantasy": "#800080",  # 紫色
    "Watch The Throne": "#DAA520",  # 金色
    "Cruel Summer": "#FF4500",  # 橙红色
    "Yeezus": "#000000",  # 黑色
    "The Life Of Pablo": "#FF8C00",  # 深橙色
    "ye": "#2F4F4F",  # 深青色
    "Kids See Ghosts": "#8B008B",  # 深洋红色
    "Jesus Is King": "#4169E1",  # 皇家蓝
    "Donda": "#191970",  # 午夜蓝
    "Donda 2": "#8B0000",  # 深红色
    "Vultures": "#2E8B57",  # 海绿色
}

# 默认背景色
DEFAULT_COLOR = "#333333"

def slugify(text):
    """
    将文本转换为URL友好的格式
    """
    # 转换为小写
    text = text.lower()
    # 移除括号内的内容
    text = re.sub(r'\([^)]*\)', '', text)
    # 替换特殊字符为连字符
    text = re.sub(r'[^a-z0-9]+', '-', text)
    # 移除首尾连字符
    text = text.strip('-')
    return text

def clean_text(text):
    """
    清理文本，移除HTML标签和特殊字符
    """
    # 解码HTML实体
    text = html.unescape(text)
    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', text)
    # 规范化Unicode字符
    text = unicodedata.normalize('NFKC', text)
    # 移除引号前的反斜杠
    text = text.replace('\\"', '"')
    return text.strip()

def parse_track_name(name):
    """
    解析曲目名称，提取艺术家、制作人等信息
    """
    name = clean_text(name)
    
    # 提取艺术家
    artists = []
    feat_match = re.search(r'\(feat\.\s+([^)]+)\)', name)
    if feat_match:
        feat_artists = feat_match.group(1)
        # 分割多个艺术家
        artists = [a.strip() for a in re.split(r'[,&]', feat_artists)]
        # 移除"Kanye West"，因为他是主要艺术家
        artists = [a for a in artists if a.lower() != 'kanye west']
    
    # 提取制作人
    producers = []
    prod_match = re.search(r'\(prod\.\s+([^)]+)\)', name)
    if prod_match:
        prod_list = prod_match.group(1)
        # 分割多个制作人
        producers = [p.strip() for p in re.split(r'[,&]', prod_list)]
    
    # 清理名称，移除括号内容
    clean_name = re.sub(r'\s*\([^)]*\)', '', name).strip()
    
    return {
        'name': name,
        'clean_name': clean_name,
        'artists': artists,
        'producers': producers
    }

def parse_links(links_text):
    """
    解析链接文本，提取URL
    """
    if not links_text or links_text.lower() in ['n/a', 'source needed', '']:
        return []
    
    # 分割多个链接
    links = [link.strip() for link in links_text.split('\n')]
    # 过滤掉非URL
    links = [link for link in links if link.startswith('http')]
    return links

def process_csv():
    """
    处理CSV文件并生成JSON数据
    """
    # 预定义的专辑时期列表
    predefined_eras = [
        "Before The College Dropout",
        "The College Dropout", 
        "Late Registration", 
        "Graduation", 
        "808s & Heartbreak", 
        "My Beautiful Dark Twisted Fantasy", 
        "Watch The Throne", 
        "Cruel Summer", 
        "Yeezus", 
        "The Life Of Pablo", 
        "ye", 
        "Kids See Ghosts", 
        "Jesus Is King", 
        "Donda", 
        "Donda 2", 
        "Vultures"
    ]
    
    eras_data = []
    era_details = {}
    
    # 首先创建所有预定义的专辑时期
    for era in predefined_eras:
        era_id = slugify(era)
        eras_data.append({
            "id": era_id,
            "name": era,
            "trackCount": 0,  # 稍后更新
            "backgroundColor": ERA_COLORS.get(era, DEFAULT_COLOR),
            "coverImage": "/images/eras/default.jpg",
            "artist": "Ye",
            "artistId": "ye"
        })
        
        era_details[era_id] = {
            "id": era_id,
            "name": era,
            "description": "",
            "backgroundColor": ERA_COLORS.get(era, DEFAULT_COLOR),
            "coverImage": "/images/eras/default.jpg",
            "tracks": []
        }
    
    with open(CSV_FILE, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        current_era = None
        era_description = ""
        
        for row in reader:
            if not row or len(row) < 9:
                continue
                
            era, name, notes, track_length, file_date, leak_date, available_length, quality, links = row[:9]
            
            # 跳过标题行和空行
            if era == "Era" or not era:
                continue
                
            # 跳过注释行
            if era.startswith("We do not support") or era.startswith("0 OG File") or "OG File" in era:
                continue
                
            # 检查是否是新的时期
            if era and era in predefined_eras and era != current_era:
                current_era = era
                
                # 检查下一行是否包含描述
                if notes and "(" in name and ")" in name:
                    # 更新时期描述
                    era_id = slugify(era)
                    if era_id in era_details:
                        era_details[era_id]["description"] = notes
                    continue
            
            # 处理曲目数据
            if current_era and name:
                # 跳过非曲目数据行
                if name.startswith("Links") or name.startswith("Update Notes") or "Total Links" in name:
                    continue
                    
                track_info = parse_track_name(name)
                track_id = slugify(track_info['clean_name'])
                
                # 确保每个曲目都有一个有效的ID
                if not track_id:
                    track_id = f"track-{len(era_details.get(slugify(current_era), {}).get('tracks', []))+1}"
                
                # 检查是否已经存在相同ID的曲目
                existing_ids = [t['id'] for t in era_details.get(slugify(current_era), {}).get('tracks', [])]
                if track_id in existing_ids:
                    track_id = f"{track_id}-{len(era_details.get(slugify(current_era), {}).get('tracks', []))+1}"
                
                track_data = {
                    "id": track_id,
                    "name": track_info['name'],
                    "artists": track_info['artists'],
                    "producers": track_info['producers'],
                    "notes": notes,
                    "length": track_length,
                    "fileDate": file_date,
                    "leakDate": leak_date,
                    "availableLength": available_length,
                    "quality": quality,
                    "links": parse_links(links),
                    "eraId": slugify(current_era)  # 添加所属时期ID
                }
                
                # 添加到对应时期
                era_id = slugify(current_era)
                if era_id in era_details:
                    era_details[era_id]["tracks"].append(track_data)
    
    # 更新曲目数量
    for era in eras_data:
        era_id = era["id"]
        if era_id in era_details:
            era["trackCount"] = len(era_details[era_id]["tracks"])
    
    # 写入eras.json到分类目录
    with open(CATEGORY_DIR / 'eras.json', 'w', encoding='utf-8') as f:
        json.dump(eras_data, f, ensure_ascii=False, indent=2)
    
    # 写入各时期详情文件
    for era_id, details in era_details.items():
        with open(ERAS_DIR / f'{era_id}.json', 'w', encoding='utf-8') as f:
            json.dump(details, f, ensure_ascii=False, indent=2)
            
    # 创建分类信息文件
    category_info = {
        "id": "unreleased",
        "name": "Unreleased",
        "description": "Leaked and unreleased tracks from various Ye eras.",
        "artistId": "ye",
        "artist": "Ye",
        "eraCount": len(eras_data),
        "trackCount": sum(era["trackCount"] for era in eras_data),
        "backgroundColor": "#333333"
    }
    
    with open(CATEGORY_DIR / 'info.json', 'w', encoding='utf-8') as f:
        json.dump(category_info, f, ensure_ascii=False, indent=2)
    
    print(f"处理完成！共生成 {len(eras_data)} 个时期的数据。")

if __name__ == "__main__":
    process_csv()
