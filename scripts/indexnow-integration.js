#!/usr/bin/env node

/**
 * IndexNow Integration for TrackerHive Build Process
 * Automatically submits new and updated URLs after build
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { submitToIndexNow } from './indexnow-submit.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Extract URLs from built HTML files
 */
function extractUrlsFromBuild() {
  const distPath = path.join(__dirname, '../dist');
  const urls = new Set();
  
  if (!fs.existsSync(distPath)) {
    console.log('⚠️ Dist folder not found, skipping URL extraction');
    return [];
  }
  
  function scanDirectory(dir, baseUrl = 'https://aitrackerhive.com') {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Recursively scan subdirectories
        const relativePath = path.relative(distPath, fullPath);
        if (relativePath && !relativePath.startsWith('.')) {
          urls.add(`${baseUrl}/${relativePath}`);
          scanDirectory(fullPath, baseUrl);
        }
      } else if (item === 'index.html') {
        // Add directory URL for index.html files
        const relativePath = path.relative(distPath, dir);
        const url = relativePath ? `${baseUrl}/${relativePath}` : baseUrl;
        urls.add(url);
      }
    }
  }
  
  try {
    scanDirectory(distPath);
    return Array.from(urls).filter(url => {
      // Filter out unwanted URLs
      return !url.includes('/_astro/') && 
             !url.includes('/images/') && 
             !url.includes('/data/') &&
             !url.endsWith('.xml') &&
             !url.endsWith('.txt');
    });
  } catch (error) {
    console.error('Error scanning build directory:', error);
    return [];
  }
}

/**
 * Get recently modified URLs (for incremental updates)
 */
function getRecentlyModifiedUrls() {
  const cacheFile = path.join(__dirname, '../.indexnow-cache.json');
  const now = Date.now();
  const oneHour = 60 * 60 * 1000;
  
  try {
    if (fs.existsSync(cacheFile)) {
      const cache = JSON.parse(fs.readFileSync(cacheFile, 'utf-8'));
      const lastSubmission = cache.lastSubmission || 0;
      
      // If last submission was less than 1 hour ago, skip
      if (now - lastSubmission < oneHour) {
        console.log('⏰ Recent submission found, skipping IndexNow');
        return [];
      }
    }
    
    // Update cache
    fs.writeFileSync(cacheFile, JSON.stringify({
      lastSubmission: now,
      timestamp: new Date().toISOString()
    }));
    
    return extractUrlsFromBuild();
  } catch (error) {
    console.error('Error handling cache:', error);
    return extractUrlsFromBuild();
  }
}

/**
 * Main integration function
 */
async function runIndexNowIntegration() {
  console.log('🎵 TrackerHive IndexNow Integration');
  console.log('===================================');
  
  // Check if this is a production build
  const isProduction = process.env.NODE_ENV === 'production' || 
                      process.env.VERCEL_ENV === 'production' ||
                      process.argv.includes('--production');
  
  if (!isProduction) {
    console.log('🔧 Development mode detected, skipping IndexNow submission');
    return;
  }
  
  try {
    const urls = getRecentlyModifiedUrls();
    
    if (urls.length === 0) {
      console.log('📭 No URLs to submit');
      return;
    }
    
    console.log(`📋 Found ${urls.length} URLs to submit to IndexNow`);
    
    // Submit in batches of 100 (IndexNow limit)
    const batchSize = 100;
    for (let i = 0; i < urls.length; i += batchSize) {
      const batch = urls.slice(i, i + batchSize);
      console.log(`📤 Submitting batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(urls.length / batchSize)}`);
      await submitToIndexNow(batch);
      
      // Small delay between batches
      if (i + batchSize < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log('✅ IndexNow integration completed successfully');
  } catch (error) {
    console.error('❌ IndexNow integration failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runIndexNowIntegration();
}

export { runIndexNowIntegration, extractUrlsFromBuild };
