#!/usr/bin/env node

/**
 * 将JPG/JPEG图片转换为WebP格式
 * 这个脚本会自动查找public/images目录下的所有JPG/JPEG图片，
 * 并创建相应的WebP版本，以提高网站性能
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 图片目录
const IMAGE_DIR = path.join(process.cwd(), 'public', 'images');

// 检查是否安装了cwebp
try {
  execSync('which cwebp', { stdio: 'ignore' });
} catch (error) {
  console.error('错误: 未找到cwebp工具。请先安装WebP工具：');
  console.error('Mac: brew install webp');
  console.error('Linux: apt-get install webp');
  process.exit(1);
}

// 递归查找所有JPG/JPEG图片
function findJpegImages(dir) {
  let results = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const itemPath = path.join(dir, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      results = results.concat(findJpegImages(itemPath));
    } else if (/\.(jpg|jpeg)$/i.test(item)) {
      results.push(itemPath);
    }
  }
  
  return results;
}

// 转换图片为WebP
function convertToWebP(imagePath) {
  const webpPath = imagePath.replace(/\.(jpg|jpeg)$/i, '.webp');
  
  // 检查WebP版本是否已存在
  if (fs.existsSync(webpPath)) {
    const originalStat = fs.statSync(imagePath);
    const webpStat = fs.statSync(webpPath);
    
    // 如果原图比WebP版本新，则重新转换
    if (originalStat.mtime > webpStat.mtime) {
      console.log(`更新: ${path.relative(process.cwd(), imagePath)} -> ${path.relative(process.cwd(), webpPath)}`);
    } else {
      console.log(`跳过: ${path.relative(process.cwd(), webpPath)} (已存在)`);
      return;
    }
  } else {
    console.log(`转换: ${path.relative(process.cwd(), imagePath)} -> ${path.relative(process.cwd(), webpPath)}`);
  }
  
  try {
    // 使用cwebp转换图片，质量设为80%以平衡大小和质量
    execSync(`cwebp -q 80 "${imagePath}" -o "${webpPath}"`);
    
    // 输出大小比较
    const originalSize = fs.statSync(imagePath).size;
    const webpSize = fs.statSync(webpPath).size;
    const savings = ((1 - webpSize / originalSize) * 100).toFixed(2);
    
    console.log(`  原始大小: ${(originalSize / 1024).toFixed(2)} KiB`);
    console.log(`  WebP大小: ${(webpSize / 1024).toFixed(2)} KiB`);
    console.log(`  节省空间: ${savings}%`);
  } catch (error) {
    console.error(`  错误: 转换失败 ${imagePath}`, error.message);
  }
}

// 主函数
async function main() {
  console.log('开始查找JPG/JPEG图片...');
  const images = findJpegImages(IMAGE_DIR);
  console.log(`找到 ${images.length} 张JPG/JPEG图片`);
  
  if (images.length === 0) {
    console.log('没有找到需要转换的图片');
    return;
  }
  
  console.log('开始转换为WebP格式...');
  for (const image of images) {
    convertToWebP(image);
  }
  
  console.log('转换完成!');
}

// 执行主函数
main().catch(err => {
  console.error('发生错误:', err);
  process.exit(1);
});
