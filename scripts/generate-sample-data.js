// 生成示例数据脚本
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// 确保输出目录存在
const outputDir = path.join(rootDir, 'public', 'data', 'eras');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 辅助函数：将字符串转换为URL友好的slug
function slugify(text) {
  return text
    .toString()
    .toLowerCase()
    .replace(/\s+/g, '-')        // 替换空格为 -
    .replace(/[^\w\-]+/g, '')    // 移除非单词字符
    .replace(/\-\-+/g, '-')      // 替换多个 - 为单个 -
    .replace(/^-+/, '')          // 去除开头的 -
    .replace(/-+$/, '');         // 去除结尾的 -
}

// 创建示例数据
const sampleEras = [
  {
    id: 'before-the-college-dropout',
    name: 'Before The College Dropout',
    description: 'Before Kanye released his first album to critical acclaim in 2004, he pursued many other projects, including a rap trio group named the "Go Getters" and production for other rappers, including but not limited to JAY-Z, Common, Talib Kweli, and Scarface.',
    backgroundColor: '#8B0000',
    coverImage: '/images/eras/default.jpg',
    tracks: [
      {
        id: '10-in-a-benz',
        name: '10 in a Benz (with Go Getters) (feat. Rhymefest & Kanye West)',
        artists: ['Rhymefest', 'Kanye West'],
        producers: ['Kanye West', 'Andy C.'],
        notes: 'Track 10 from Go Getters\'s 1999 compilation tape World Record Holders.',
        length: '4:14',
        leakDate: '',
        availableLength: 'Full',
        quality: 'High Quality',
        links: ['https://example.com/track1']
      },
      {
        id: '187th',
        name: '187th (with The Chicago Outfit)',
        artists: ['The Chicago Outfit'],
        producers: ['Kanye West'],
        notes: 'Song recorded in 1996 for Kanye\'s rap group The Chicago Outfit (later known as Go Getters).',
        length: '2:56',
        leakDate: 'Apr 22, 2009',
        availableLength: 'Full',
        quality: 'Low Quality',
        links: ['https://example.com/track2']
      }
    ]
  },
  {
    id: 'the-college-dropout',
    name: 'The College Dropout',
    description: 'Following his signing to Roc-A-Fella Records, Kanye released his debut studio album, The College Dropout. It features string arrangements, choirs, and his signature soul sampling, frequently branded as "chipmunk soul" for its sped-up and high-pitched nature.',
    backgroundColor: '#8B4513',
    coverImage: '/images/eras/default.jpg',
    tracks: [
      {
        id: 'home',
        name: 'Home (feat. John Legend)',
        artists: ['John Legend'],
        producers: ['Kanye West'],
        notes: 'Original version of "Homecoming" from Graduation.',
        length: '3:23',
        leakDate: 'Feb 10, 2003',
        availableLength: 'Full',
        quality: 'High Quality',
        links: ['https://example.com/track3']
      },
      {
        id: 'never-let-me-down-og',
        name: 'Never Let Me Down OG',
        artists: ['Jay-Z', 'J. Ivy'],
        producers: ['Kanye West'],
        notes: 'Original version without the J. Ivy verse.',
        length: '4:45',
        leakDate: '',
        availableLength: 'Snippet',
        quality: 'Low Quality',
        links: ['https://example.com/track4']
      }
    ]
  },
  {
    id: 'late-registration',
    name: 'Late Registration',
    description: 'Late Registration continues the social themes introduced in The College Dropout, but now with orchestral production influenced by co-producer Jon Brion.',
    backgroundColor: '#483D8B',
    coverImage: '/images/eras/default.jpg',
    tracks: [
      {
        id: 'cant-tell-me-nothing-remix',
        name: 'Can\'t Tell Me Nothing (Remix) (feat. Young Jeezy)',
        artists: ['Young Jeezy'],
        producers: ['Kanye West'],
        notes: 'Official remix featuring Young Jeezy.',
        length: '4:30',
        leakDate: 'Jul 15, 2007',
        availableLength: 'Full',
        quality: 'High Quality',
        links: ['https://example.com/track5']
      }
    ]
  },
  {
    id: '808s-heartbreak',
    name: '808s & Heartbreak',
    description: 'Following the death of his mother due to complications after cosmetic surgery, his relationship with fiancé Alexis Phifer finally ending for good, and a struggle to adapt to his celebrity status, Kanye felt emotionally drained and lost.',
    backgroundColor: '#708090',
    coverImage: '/images/eras/default.jpg',
    tracks: [
      {
        id: 'robocop-og',
        name: 'RoboCop OG',
        artists: [],
        producers: ['Kanye West'],
        notes: 'Original version with different lyrics and production.',
        length: '4:34',
        leakDate: 'Mar 13, 2014',
        availableLength: 'Full',
        quality: 'High Quality',
        links: ['https://example.com/track6']
      },
      {
        id: 'coldest-winter-og',
        name: 'Coldest Winter OG',
        artists: [],
        producers: ['Kanye West'],
        notes: 'Original version with different production.',
        length: '2:45',
        leakDate: '',
        availableLength: 'Not Available',
        quality: 'Unknown',
        links: []
      }
    ]
  }
];

console.log('开始生成示例数据...');

// 为每个Era创建单独的JSON文件
sampleEras.forEach(era => {
  fs.writeFileSync(
    path.join(outputDir, `${era.id}.json`), 
    JSON.stringify(era, null, 2)
  );
  console.log(`已创建 ${era.id}.json`);
});

// 创建索引文件
const erasIndex = sampleEras.map(era => ({
  id: era.id,
  name: era.name,
  trackCount: era.tracks.length,
  backgroundColor: era.backgroundColor,
  coverImage: era.coverImage
}));

fs.writeFileSync(
  path.join(rootDir, 'public', 'data', 'eras-index.json'),
  JSON.stringify(erasIndex, null, 2)
);

console.log('数据生成完成！');
