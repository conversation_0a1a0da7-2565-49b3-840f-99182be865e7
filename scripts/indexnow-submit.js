#!/usr/bin/env node

/**
 * IndexNow Submission Script for TrackerHive
 * Automatically submits URLs to IndexNow API for faster indexing
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// IndexNow configuration
const INDEXNOW_CONFIG = {
  key: 'a1b2c3d4e5f6g7h8i9j0',
  host: 'aitrackerhive.com',
  endpoint: 'https://api.indexnow.org/indexnow'
};

/**
 * Submit URLs to IndexNow API
 * @param {string[]} urls - Array of URLs to submit
 */
async function submitToIndexNow(urls) {
  if (!urls || urls.length === 0) {
    console.log('No URLs to submit to IndexNow');
    return;
  }

  try {
    console.log(`🚀 Submitting ${urls.length} URLs to IndexNow...`);
    
    const payload = {
      host: INDEXNOW_CONFIG.host,
      key: INDEXNOW_CONFIG.key,
      urlList: urls
    };

    const response = await fetch(INDEXNOW_CONFIG.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TrackerHive-IndexNow/1.0'
      },
      body: JSON.stringify(payload)
    });

    if (response.ok) {
      console.log('✅ Successfully submitted URLs to IndexNow');
      console.log(`📊 Status: ${response.status}`);
    } else {
      console.log(`⚠️ IndexNow submission failed: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('❌ Error submitting to IndexNow:', error.message);
  }
}

/**
 * Generate URLs from sitemap
 */
async function getUrlsFromSitemap() {
  const sitemapPath = path.join(__dirname, '../dist/sitemap-index.xml');
  
  if (!fs.existsSync(sitemapPath)) {
    console.log('⚠️ Sitemap not found, generating sample URLs...');
    return generateSampleUrls();
  }

  try {
    const sitemapContent = fs.readFileSync(sitemapPath, 'utf-8');
    const urls = [];
    
    // Extract URLs from sitemap (basic regex parsing)
    const urlMatches = sitemapContent.match(/<loc>(.*?)<\/loc>/g);
    if (urlMatches) {
      urlMatches.forEach(match => {
        const url = match.replace(/<\/?loc>/g, '');
        urls.push(url);
      });
    }

    return urls.slice(0, 100); // Limit to 100 URLs per submission
  } catch (error) {
    console.error('Error reading sitemap:', error.message);
    return generateSampleUrls();
  }
}

/**
 * Generate sample URLs for testing
 */
function generateSampleUrls() {
  const baseUrl = `https://${INDEXNOW_CONFIG.host}`;
  return [
    baseUrl,
    `${baseUrl}/artists/ye`,
    `${baseUrl}/artists/playboi-carti`,
    `${baseUrl}/artists/ye/unreleased`,
    `${baseUrl}/artists/playboi-carti/unreleased`,
    `${baseUrl}/categories/unreleased`
  ];
}

/**
 * Main execution
 */
async function main() {
  console.log('🎵 TrackerHive IndexNow Submission');
  console.log('=====================================');
  
  const urls = await getUrlsFromSitemap();
  
  if (urls.length > 0) {
    console.log(`📋 Found ${urls.length} URLs to submit`);
    await submitToIndexNow(urls);
  } else {
    console.log('❌ No URLs found to submit');
  }
  
  console.log('✨ IndexNow submission completed');
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { submitToIndexNow, getUrlsFromSitemap };
