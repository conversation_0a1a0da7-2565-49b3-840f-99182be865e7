#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import re
from pathlib import Path
from bs4 import BeautifulSoup
import unicodedata
import html
import sys

# 定义路径
BASE_DIR = Path('/Users/<USER>/overseas_website/TrackerHive')
HTML_FILE = BASE_DIR / 'data' / 'PlayboiUnreleased.html'
OUTPUT_DIR = BASE_DIR / 'public' / 'data' / 'artists' / 'playboi-carti'
CATEGORY_DIR = OUTPUT_DIR / 'categories' / 'unreleased'
ERAS_DIR = CATEGORY_DIR / 'eras'

# 确保输出目录存在
ERAS_DIR.mkdir(parents=True, exist_ok=True)

# 预定义的专辑列表
ALBUM_NAMES = []

# 颜色映射 - 为每个时期分配一个背景色
ERA_COLORS = {
    "Before The College Dropout": "#8B0000",  # 深红色
    "The College Dropout": "#8B4513",  # 棕色
    "Late Registration": "#483D8B",  # 深紫色
    "Graduation": "#006400",  # 深绿色
    "808s & Heartbreak": "#4682B4",  # 钢蓝色
    "My Beautiful Dark Twisted Fantasy": "#800080",  # 紫色
    "Watch The Throne": "#DAA520",  # 金色
    "Cruel Summer": "#FF4500",  # 橙红色
    "Yeezus": "#000000",  # 黑色
    "The Life Of Pablo": "#FF8C00",  # 深橙色
    "ye": "#2F4F4F",  # 深青色
    "Kids See Ghosts": "#8B008B",  # 深洋红色
    "Jesus Is King": "#4169E1",  # 皇家蓝
    "Donda": "#191970",  # 午夜蓝
    "Donda 2": "#8B0000",  # 深红色
    "Vultures": "#2E8B57",  # 海绿色
}

# 默认背景色
DEFAULT_COLOR = "#333333"

def extract_next_data_json(html_content):
    """
    从HTML中提取__NEXT_DATA__ script标签中的JSON数据
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    next_data_script = soup.find('script', id='__NEXT_DATA__')
    
    if next_data_script:
        try:
            next_data = json.loads(next_data_script.string)
            return next_data
        except json.JSONDecodeError as e:
            print(f"解析__NEXT_DATA__中的JSON数据时出错: {e}")
            return None
    return None

def parse_next_data_json(next_data):
    """
    使用__NEXT_DATA__中的JSON数据解析曲目信息
    """
    try:
        # 提取unreleased数据
        unreleased_data = next_data.get('props', {}).get('pageProps', {}).get('unreleased', [])
        
        if not unreleased_data:
            print("在__NEXT_DATA__中未找到unreleased数据")
            return None
            
        print(f"找到 {len(unreleased_data)} 个专辑数据，开始解析...")
            
        # 创建专辑和曲目数据结构
        eras_data = []
        era_details = {}
        
        total_eras = len(unreleased_data)
        for era_index, era in enumerate(unreleased_data):
            # 提取专辑信息
            color = era.get('color', '')
            cover = era.get('cover', '')
            title_text = era.get('title', {}).get('text', '')
            description_text = era.get('description', {}).get('text', '')
            
            # 显示进度
            progress = (era_index + 1) / total_eras * 100
            print(f"[进度: {progress:.1f}%] 正在处理专辑 {era_index+1}/{total_eras}: {title_text}")
            
            # 创建专辑ID
            era_id = slugify(title_text)
            
            # 创建专辑数据
            era_data = {
                "id": era_id,
                "name": title_text,
                "description": description_text,
                "backgroundColor": color,
                "coverImage": cover,
                "tracks": []
            }
            
            # 添加到专辑列表
            eras_data.append({
                "id": era_id,
                "name": title_text,
                "coverImage": cover,
                "backgroundColor": era_data.get("backgroundColor", "#333"),
                "trackCount": 0  # 稍后更新
            })
            
            # 存储专辑详细信息
            era_details[era_id] = era_data
            
            # 处理曲目
            children = era.get('children', [])
            total_tracks = len([t for t in children if t.get('type') == 'track'])
            processed_tracks = 0
            
            for i, track in enumerate(children):
                if track.get('type') != 'track':
                    continue
                    
                processed_tracks += 1
                if processed_tracks % 10 == 0 or processed_tracks == total_tracks:
                    track_progress = processed_tracks / total_tracks * 100 if total_tracks > 0 else 100
                    print(f"  [进度: {track_progress:.1f}%] 已处理 {processed_tracks}/{total_tracks} 个曲目")
                    
                # 提取曲目信息
                track_key = track.get('key', '')
                track_title = track.get('title', '')
                track_artists = track.get('artists', '')
                track_aliases = track.get('aliases', [])
                track_description = track.get('description', '')
                track_date = track.get('date', '')
                track_available = track.get('available', [])
                track_quality = track.get('quality', [])
                track_original_url = track.get('originalUrl', '')
                track_size = track.get('size', '')
                track_duration = track.get('duration', '')
                
                # 提取艺术家和制作人信息
                artists = extract_artists_from_text(track_artists)
                producers = extract_producers_from_text(track_artists)
                
                # 创建曲目ID
                track_id = slugify(track_key or track_title) if (track_key or track_title) else f"track-{i+1}"
                
                # 将时长转换为分:秒格式
                formatted_length = ""
                if track_duration:
                    try:
                        # 尝试将时长转换为数字
                        duration_seconds = float(track_duration)
                        minutes = int(duration_seconds // 60)
                        seconds = int(duration_seconds % 60)
                        formatted_length = f"{minutes}:{seconds:02d}"
                    except (ValueError, TypeError):
                        # 如果无法转换为数字，则置空
                        formatted_length = ""
                
                # 创建标签样式的quality字段
                labels = []
                if track_quality and len(track_quality) > 0 and track_quality[0]:
                    labels.append(track_quality[0])
                if track_available and len(track_available) > 0 and track_available[0]:
                    labels.append(track_available[0])
                
                # 创建曲目数据
                track_data = {
                    "id": track_id,
                    "name": track_title,
                    "artists": artists,
                    "producers": producers,
                    "notes": track_description,
                    "length": formatted_length,
                    "fileDate": track_date if track_date else "",
                    "leakDate": "",
                    "labels": labels,  # 新的标签字段，包含Quality和Available信息
                    "links": [],
                    "eraId": era_id,
                    "originalUrl": track_original_url,
                    "originalContent": json.dumps(track),
                    "aliases": track_aliases,
                    "size": track_size if track_size else ""
                }
                
                # 确保曲目ID唯一
                existing_ids = [t['id'] for t in era_details[era_id]['tracks']]
                if track_data['id'] in existing_ids:
                    track_data['id'] = f"{track_data['id']}-{len(era_details[era_id]['tracks'])+1}"
                
                # 添加到专辑曲目列表
                era_details[era_id]['tracks'].append(track_data)
        
        # 更新每个专辑的曲目数量
        for era_index, era_info in enumerate(eras_data):
            era_id = era_info['id']
            if era_id in era_details:
                track_count = len(era_details[era_id]['tracks'])
                eras_data[era_index]['trackCount'] = track_count
                print(f"  专辑 '{era_info['name']}' 包含 {track_count} 个曲目")
        
        return eras_data, era_details
    except Exception as e:
        print(f"解析__NEXT_DATA__数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def slugify(text):
    """
    将文本转换为URL友好的格式
    """
    if not text:
        return ""
    # 转换为小写
    text = text.lower()
    # 移除括号内的内容
    text = re.sub(r'\([^)]*\)', '', text)
    # 替换特殊字符为连字符
    text = re.sub(r'[^a-z0-9]+', '-', text)
    # 移除首尾连字符
    text = text.strip('-')
    return text

def clean_text(text):
    """
    清理文本，移除HTML标签和特殊字符
    """
    # 解码HTML实体
    text = html.unescape(text)
    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', text)
    # 规范化Unicode字符
    text = unicodedata.normalize('NFKC', text)
    # 移除引号前的反斜杠
    text = text.replace('\\"', '"')
    return text.strip()

def extract_artists_from_text(text):
    """
    从文本中提取艺术家信息
    """
    artists = []
    # 查找 (feat. X) 或 (feat X) 格式
    feat_match = re.search(r'\(feat\.?\s+([^)]+)\)', text)
    if feat_match:
        feat_artists = feat_match.group(1)
        # 分割多个艺术家
        artists = [a.strip() for a in re.split(r'[,&]', feat_artists)]
    return artists

def extract_producers_from_text(text):
    """
    从文本中提取制作人信息
    """
    producers = []
    # 查找 (prod. X) 或 (prod X) 格式
    prod_match = re.search(r'\(prod\.?\s+([^)]+)\)', text)
    if prod_match:
        prod_list = prod_match.group(1)
        # 分割多个制作人
        producers = [p.strip() for p in re.split(r'[,&]', prod_list)]
    return producers

def parse_html():
    """
    解析HTML文件并生成JSON数据
    """
    import time
    start_time = time.time()
    
    # 读取HTML文件
    print(f"正在读取HTML文件: {HTML_FILE}")
    with open(HTML_FILE, 'r', encoding='utf-8') as file:
        html_content = file.read()
    
    file_size_mb = len(html_content) / (1024 * 1024)
    print(f"文件大小: {file_size_mb:.2f} MB")
    
    # 尝试从__NEXT_DATA__中提取JSON数据
    print("尝试从__NEXT_DATA__中提取JSON数据...")
    next_data = extract_next_data_json(html_content)
    
    if next_data:
        print("成功提取__NEXT_DATA__中的JSON数据，开始解析...")
        result = parse_next_data_json(next_data)
        if result:
            eras_data, era_details = result
            
            # 保存每个专辑的详细信息
            for era_id, era_detail in era_details.items():
                output_file = ERAS_DIR / f"{era_id}.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(era_detail, f, ensure_ascii=False, indent=2)
            
            # 保存专辑列表
            eras_output_file = CATEGORY_DIR / 'eras.json'
            with open(eras_output_file, 'w', encoding='utf-8') as f:
                json.dump(eras_data, f, ensure_ascii=False, indent=2)
            
            # 保存分类信息
            info_output_file = CATEGORY_DIR / 'info.json'
            info_data = {
                "id": "unreleased",
                "name": "未发行",
                "description": "这个分类包含了所有未正式发行的曲目"
            }
            with open(info_output_file, 'w', encoding='utf-8') as f:
                json.dump(info_data, f, ensure_ascii=False, indent=2)
            
            # 计算处理时间
            end_time = time.time()
            elapsed_time = end_time - start_time
            minutes, seconds = divmod(elapsed_time, 60)
            
            print(f"处理完成！共生成 {len(eras_data)} 个时期的数据。")
            print(f"总耗时: {int(minutes)}分{int(seconds)}秒")
            return
    
    print("未找到__NEXT_DATA__中的JSON数据或解析失败，将使用HTML解析方式")
    
    # 使用BeautifulSoup解析HTML
    print("使用BeautifulSoup解析HTML文件...")
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 初始化专辑数据
    eras_data = []
    era_details = {}
    
    # 查找所有专辑卡片
    print("开始查找专辑卡片...")
    era_cards = soup.find_all('div', class_=lambda x: x and 'EraCard_era__RaoRE' in x)
    
    if not era_cards:
        # 尝试其他可能的类名
        era_cards = soup.find_all('div', class_=lambda x: x and 'EraCard_container' in x)
    
    if not era_cards:
        era_cards = soup.find_all('div', class_=lambda x: x and 'EraCard_card' in x)
    
    print(f"找到 {len(era_cards)} 个专辑卡片")
    
    # 处理每个专辑卡片
    for era_index, era_card in enumerate(era_cards):
        try:
            # 提取标题
            title_elem = era_card.find('div', class_=lambda x: x and 'EraCard_title__a_skU' in x)
            if not title_elem:
                # 尝试其他可能的类名
                title_elem = era_card.find('div', class_=lambda x: x and 'title' in x.lower())
            
            title_text = title_elem.get_text(strip=True) if title_elem else f"未知专辑 {era_index+1}"
            
            # 提取描述
            desc_elem = era_card.find('div', class_=lambda x: x and 'EraCard_description__jXVzh' in x)
            if not desc_elem:
                # 尝试其他可能的类名
                desc_elem = era_card.find('div', class_=lambda x: x and 'description' in x.lower())
            
            description_text = desc_elem.get_text(strip=True) if desc_elem else ""
            
            # 提取封面图片
            img_elem = era_card.find('img', class_=lambda x: x and 'EraCard_cover__huWx9' in x)
            if not img_elem:
                # 尝试其他可能的类名
                img_elem = era_card.find('img', class_=lambda x: x and 'cover' in x.lower())
            
            cover = ""
            if img_elem and 'srcset' in img_elem.attrs:
                srcset = img_elem['srcset']
                # 提取2x图片URL
                match = re.search(r'(https?://[^\s]+)\s+2x', srcset)
                if match:
                    cover = match.group(1)
            elif img_elem and 'src' in img_elem.attrs:
                cover = img_elem['src']
            
            # 提取背景颜色
            # 尝试从style属性中提取
            background_color = "#333"  # 默认颜色
            if 'style' in era_card.attrs:
                style = era_card['style']
                color_match = re.search(r'background(?:-color)?:\s*([^;]+)', style)
                if color_match:
                    background_color = color_match.group(1).strip()
            
            # 创建专辑ID
            era_id = slugify(title_text)
            
            # 显示进度
            progress = (era_index + 1) / len(era_cards) * 100
            print(f"[进度: {progress:.1f}%] 正在处理专辑 {era_index+1}/{len(era_cards)}: {title_text}")
            
            # 创建专辑数据
            era_data = {
                "id": era_id,
                "name": title_text,
                "description": description_text,
                "backgroundColor": background_color,
                "coverImage": cover,
                "tracks": []
            }
            
            # 添加到专辑列表
            eras_data.append({
                "id": era_id,
                "name": title_text,
                "coverImage": cover,
                "backgroundColor": era_data.get("backgroundColor", "#333"),
                "trackCount": 0  # 稍后更新
            })
            
            # 存储专辑详细信息
            era_details[era_id] = era_data
            
            # 查找专辑中的曲目
            track_elements = era_card.find_all('div', class_=lambda x: x and 'TrackCard_track' in x)
            if not track_elements:
                # 尝试其他可能的类名
                track_elements = era_card.find_all('div', class_=lambda x: x and 'track-card' in str(x).lower())
            
            # 处理曲目
            for i, track_elem in enumerate(track_elements):
                try:
                    # 提取曲目标题
                    track_title_elem = track_elem.find('div', class_=lambda x: x and 'title' in x.lower())
                    track_title = track_title_elem.get_text(strip=True) if track_title_elem else f"未知曲目 {i+1}"
                    
                    # 提取艺术家信息
                    track_artists_elem = track_elem.find('div', class_=lambda x: x and 'artists' in x.lower())
                    track_artists = track_artists_elem.get_text(strip=True) if track_artists_elem else ""
                    
                    # 提取描述
                    track_desc_elem = track_elem.find('div', class_=lambda x: x and 'description' in x.lower())
                    track_description = track_desc_elem.get_text(strip=True) if track_desc_elem else ""
                    
                    # 提取可用性和质量
                    track_available = ""
                    track_quality = ""
                    
                    available_elem = track_elem.find('div', class_=lambda x: x and 'available' in str(x).lower())
                    if available_elem:
                        track_available = available_elem.get_text(strip=True)
                    
                    quality_elem = track_elem.find('div', class_=lambda x: x and 'quality' in str(x).lower())
                    if quality_elem:
                        track_quality = quality_elem.get_text(strip=True)
                    
                    # 提取艺术家和制作人信息
                    artists = extract_artists_from_text(track_artists)
                    producers = extract_producers_from_text(track_artists)
                    
                    # 创建曲目ID
                    track_id = slugify(track_title) if track_title else f"track-{i+1}"
                    
                    # 创建标签样式的quality字段
                    labels = []
                    if track_quality:
                        labels.append(track_quality)
                    if track_available:
                        labels.append(track_available)
                    
                    # 创建曲目数据
                    track_data = {
                        "id": track_id,
                        "name": track_title,
                        "artists": artists,
                        "producers": producers,
                        "notes": track_description,
                        "length": "",  # 无法从HTML直接获取，如果有值会转换为分:秒格式
                        "fileDate": "",  # 无法从HTML直接获取
                        "leakDate": "",
                        "labels": labels,  # 新的标签字段，包含Quality和Available信息
                        "links": [],
                        "eraId": era_id
                    }
                    
                    # 确保曲目ID唯一
                    existing_ids = [t['id'] for t in era_details[era_id]['tracks']]
                    if track_data['id'] in existing_ids:
                        track_data['id'] = f"{track_data['id']}-{len(era_details[era_id]['tracks'])+1}"
                    
                    # 添加到专辑曲目列表
                    era_details[era_id]['tracks'].append(track_data)
                except Exception as track_error:
                    print(f"处理曲目时出错: {track_error}")
                    continue
            
        except Exception as era_error:
            print(f"处理专辑时出错: {era_error}")
            continue
    
    # 更新每个专辑的曲目数量
    for era_index, era_info in enumerate(eras_data):
        era_id = era_info['id']
        if era_id in era_details:
            track_count = len(era_details[era_id]['tracks'])
            eras_data[era_index]['trackCount'] = track_count
            print(f"  专辑 '{era_info['name']}' 包含 {track_count} 个曲目")
    
    # 保存每个专辑的详细信息
    for era_id, era_detail in era_details.items():
        output_file = ERAS_DIR / f"{era_id}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(era_detail, f, ensure_ascii=False, indent=2)
    
    # 保存专辑列表
    eras_output_file = CATEGORY_DIR / 'eras.json'
    with open(eras_output_file, 'w', encoding='utf-8') as f:
        json.dump(eras_data, f, ensure_ascii=False, indent=2)
    
    # 保存分类信息
    info_output_file = CATEGORY_DIR / 'info.json'
    info_data = {
        "id": "unreleased",
        "name": "未发行",
        "description": "这个分类包含了所有未正式发行的曲目"
    }
    with open(info_output_file, 'w', encoding='utf-8') as f:
        json.dump(info_data, f, ensure_ascii=False, indent=2)
    
    # 计算处理时间
    end_time = time.time()
    elapsed_time = end_time - start_time
    minutes, seconds = divmod(elapsed_time, 60)
    
    print(f"处理完成！共生成 {len(eras_data)} 个时期的数据。")
    print(f"总耗时: {int(minutes)}分{int(seconds)}秒")
    return
    
    print("未找到__NEXT_DATA__中的JSON数据或解析失败，将使用HTML解析方式")
    
    # 使用BeautifulSoup解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 尝试从__NEXT_DATA__ script标签中提取JSON数据
    next_data_json = extract_next_data_json(html_content)
    if next_data_json:
        print("成功从__NEXT_DATA__中提取到JSON数据")
        result = parse_html_with_next_data(next_data_json)
        if result:
            eras_data, era_details = result
            # 直接使用从JSON中提取的数据
            # 创建输出目录
            output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'public', 'data', 'artists', 'ye', 'categories', 'unreleased', 'eras')
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存每个专辑的详细信息
            for era_id, era_detail in era_details.items():
                output_file = os.path.join(output_dir, f"{era_id}.json")
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(era_detail, f, ensure_ascii=False, indent=2)
                # 不打印每个文件的保存信息，减少输出量
            
            # 保存专辑列表
            eras_output_file = os.path.join(os.path.dirname(output_dir), 'eras.json')
            with open(eras_output_file, 'w', encoding='utf-8') as f:
                json.dump(eras_data, f, ensure_ascii=False, indent=2)
            print(f"已保存专辑列表到 {eras_output_file}")
            
            # 保存分类信息
            info_output_file = os.path.join(os.path.dirname(output_dir), 'info.json')
            info_data = {
                "id": "unreleased",
                "name": "未发行",
                "description": "这个分类包含了所有未正式发行的曲目"
            }
            with open(info_output_file, 'w', encoding='utf-8') as f:
                json.dump(info_data, f, ensure_ascii=False, indent=2)
            print(f"已保存分类信息到 {info_output_file}")
            

            return
    
    print("未找到__NEXT_DATA__中的JSON数据或解析失败，将使用HTML解析方式")
    
    # 初始化专辑数据
    eras_data = []
    era_details = {}
    
    # 查找所有专辑卡片
    print("开始查找专辑卡片...")
    
    # 首先提取所有封面图片，创建专辑名称到封面URL的映射
    album_covers = {}
    all_cover_imgs = soup.find_all('img', class_='EraCard_cover__huWx9')
    print(f"找到 {len(all_cover_imgs)} 个封面图片元素")
    
    for img in all_cover_imgs:
        if 'alt' in img.attrs and 'cover' in img['alt'].lower():
            album_name = img['alt'].replace(' cover', '')
            # 优先使用srcset属性中的2x图片
            if 'srcset' in img.attrs:
                srcset = img['srcset']
                import re
                match = re.search(r'(https?://[^\s]+)\s+2x', srcset)
                if match:
                    album_covers[album_name] = match.group(1)
                    print(f"专辑 '{album_name}' 的封面图片: {match.group(1)}")
    
    # 尝试不同的类名模式来找到专辑卡片
    era_cards = soup.find_all('div', class_=lambda x: x and 'EraCard_container' in x)
    
    if not era_cards:
        era_cards = soup.find_all('div', class_=lambda x: x and 'EraCard_card' in x)
    
    if not era_cards:
        era_cards = soup.find_all('div', class_=lambda x: x and 'era-card' in str(x).lower())
    
    print(f"找到 {len(era_cards)} 个专辑卡片")
    
    # 如果还是找不到专辑卡片，尝试先找到标题元素，然后通过父元素找到卡片
    if not era_cards:
        print("未找到专辑卡片，尝试通过标题元素找到卡片...")
        
        # 找到所有可能的标题元素
        title_elements = soup.find_all(class_=lambda x: x and 'EraCard_title' in x)
        
        if title_elements:
            print(f"找到 {len(title_elements)} 个标题元素")
            
            # 通过标题元素的父元素找到卡片
            for title_elem in title_elements:
                parent = title_elem.parent
                if parent and parent not in era_cards:
                    era_cards.append(parent)
    
    # 如果还是找不到专辑卡片，尝试查找所有包含'EraCard'的类名
    if not era_cards:
        print("仍然未找到专辑卡片，尝试查找所有包含'EraCard'的类名...")
        for div in soup.find_all('div'):
            if div.get('class') and any('EraCard' in cls for cls in div.get('class')):
                print(f"找到可能的专辑卡片类名: {div.get('class')}")
    
    # 处理每个专辑卡片
    for card in era_cards:
        # 获取专辑标题
        title_elem = card.find('div', class_='EraCard_title__a_skU')
        if not title_elem:
            # 如果找不到指定类名，尝试查找包含'title'的类名
            for div in card.find_all('div'):
                if div.get('class') and any('title' in cls.lower() for cls in div.get('class')):
                    title_elem = div
                    print(f"找到可能的标题类名: {div.get('class')}")
                    break
        
        if not title_elem:
            continue
        
        album_name = title_elem.get_text(strip=True)
        print(f"找到专辑: {album_name}")
        
        # 跳过非专辑卡片
        if not album_name or album_name in ['Links', 'Update Notes'] or 'Total Links' in album_name:
            print(f"跳过非专辑卡片: {album_name}")
            continue
        
        # 获取专辑描述
        description_elem = card.find('div', class_='EraCard_description__jXVzh')
        if not description_elem:
            # 如果找不到指定类名，尝试查找包含'description'的类名
            for div in card.find_all('div'):
                if div.get('class') and any('description' in cls.lower() for cls in div.get('class')):
                    description_elem = div
                    print(f"找到可能的描述类名: {div.get('class')}")
                    break
        
        era_description = description_elem.get_text(strip=True) if description_elem else ""
        
        # 获取专辑封面图片
        # 首先查找summary元素
        summary_elem = card.find('summary')
        if summary_elem:
            # 在summary元素下查找img标签
            cover_elem = summary_elem.find('img', class_='EraCard_cover__huWx9')
            if cover_elem:
                print(f"在summary下找到封面图片元素: {cover_elem.get('alt') if cover_elem.get('alt') else '无alt属性'}")
        else:
            cover_elem = None
            
        # 如果在summary下没找到，尝试在整个card中查找
        if not cover_elem:
            cover_elem = card.find('img', class_='EraCard_cover__huWx9')
            if cover_elem:
                print(f"在card中找到封面图片元素: {cover_elem.get('alt') if cover_elem.get('alt') else '无alt属性'}")
                
        # 如果仍然没找到，尝试查找包含'cover'类名的元素
        if not cover_elem:
            for img in card.find_all('img'):
                if img.get('class') and any('cover' in cls.lower() for cls in img.get('class')):
                    cover_elem = img
                    print(f"找到可能的封面元素: {img.get('class')}")
                    break
        
        # 默认封面图片
        cover_img = "/images/eras/default.jpg"
        
        # 使用提前提取的封面图片映射
        if album_name in album_covers:
            cover_img = album_covers[album_name]
            print(f"使用预先提取的封面图片: {cover_img}")
        # 尝试模糊匹配
        else:
            for name, url in album_covers.items():
                # 检查专辑名称是否包含在预先提取的封面图片名称中
                if name.lower() in album_name.lower() or album_name.lower() in name.lower():
                    cover_img = url
                    print(f"模糊匹配到封面图片: {name} -> {album_name}")
                    break
        
        # 如果还是没有找到封面图片，尝试从当前卡片中提取
        if cover_img == "/images/eras/default.jpg" and cover_elem:
            # 如果cover_elem本身就是img标签
            if cover_elem.name == 'img':
                # 优先使用srcset属性中的2x图片
                if 'srcset' in cover_elem.attrs:
                    srcset = cover_elem['srcset']
                    print(f"原始srcset: {srcset}")
                    
                    # 尝试提取2x图片URL
                    import re
                    match = re.search(r'(https?://[^\s]+)\s+2x', srcset)
                    if match:
                        cover_img = match.group(1)
                        print(f"从srcset中提取到2x图片: {cover_img}")
                # 如果没有srcset或提取失败，使用src属性
                elif 'src' in cover_elem.attrs and cover_elem['src'].startswith('http'):
                    cover_img = cover_elem['src']
                    print(f"使用src属性作为封面: {cover_img}")
        
        # 创建专辑ID
        era_id = slugify(album_name)
        
        # 添加到专辑数据列表
        eras_data.append({
            "id": era_id,
            "name": album_name,
            "trackCount": 0,  # 稍后更新
            "backgroundColor": ERA_COLORS.get(album_name, DEFAULT_COLOR),
            "coverImage": cover_img,
            "artist": "Ye",
            "artistId": "ye"
        })
        
        # 创建专辑详情
        era_details[era_id] = {
            "id": era_id,
            "name": album_name,
            "description": era_description,
            "backgroundColor": ERA_COLORS.get(album_name, DEFAULT_COLOR),
            "coverImage": cover_img,
            "tracks": []
        }
        
        # 查找该专辑下的所有曲目
        track_table = card.find('table')
        if track_table:
            track_rows = track_table.find_all('tr')[1:]  # 跳过表头
            for row in track_rows:
                cells = row.find_all('td')
                if len(cells) >= 4:  # 至少需要名称、备注、长度和质量
                    # 获取曲目信息
                    name_cell = cells[0]
                    notes_cell = cells[1] if len(cells) > 1 else None
                    length_cell = cells[2] if len(cells) > 2 else None
                    quality_cell = cells[3] if len(cells) > 3 else None
                    links_cell = cells[4] if len(cells) > 4 else None
                    
                    track_name = name_cell.get_text(strip=True)
                    notes = notes_cell.get_text(strip=True) if notes_cell else ""
                    track_length = length_cell.get_text(strip=True) if length_cell else ""
                    quality = quality_cell.get_text(strip=True) if quality_cell else ""
                    
                    # 获取链接
                    links = []
                    if links_cell:
                        link_elements = links_cell.find_all('a')
                        for link in link_elements:
                            href = link.get('href')
                            if href and href.startswith('http'):
                                links.append(href)
                    
                    # 提取艺术家和制作人信息
                    artists_elem = name_cell.find('div', class_='Track_artists__94fRt')
                    artists_text = artists_elem.get_text(strip=True) if artists_elem else ""
                    
                    artists = extract_artists_from_text(artists_text)
                    producers = extract_producers_from_text(artists_text)
                    
                    # 创建曲目ID
                    track_id = slugify(track_name)
                    
                    # 确保每个曲目都有一个有效的ID
                    if not track_id:
                        track_id = f"track-{len(era_details[era_id]['tracks'])+1}"
                    
                    # 检查是否已经存在相同ID的曲目
                    existing_ids = [t['id'] for t in era_details[era_id]['tracks']]
                    if track_id in existing_ids:
                        track_id = f"{track_id}-{len(era_details[era_id]['tracks'])+1}"
                    
                    # 创建曲目数据
                    track_data = {
                        "id": track_id,
                        "name": track_name,
                        "artists": artists,
                        "producers": producers,
                        "notes": notes,
                        "length": track_length,
                        "fileDate": "",
                        "leakDate": "",
                        "availableLength": quality,
                        "quality": "High Quality",
                        "links": links,
                        "eraId": era_id
                    }
                    
                    # 添加到专辑曲目列表
                    era_details[era_id]['tracks'].append(track_data)
                    print(f"添加曲目: {track_name} 到专辑 {album_name}")
    
    # 如果通过标题元素找到了专辑，但没有完整的卡片，则手动构建专辑数据
    if len(era_cards) == 0 and 'title_elements' in locals() and title_elements:
        print("使用标题元素构建专辑数据...")
        
        for title_elem in title_elements:
            album_name = title_elem.get_text(strip=True)
            
            # 跳过非专辑标题
            if not album_name or album_name in ['Links', 'Update Notes'] or 'Total Links' in album_name:
                continue
                
            print(f"处理专辑: {album_name}")
            
            # 创建专辑ID
            era_id = slugify(album_name)
            
            # 尝试找到描述和封面
            description = ""
            cover_img = "/images/eras/default.jpg"
            
            # 尝试找到父元素中的描述
            parent = title_elem.parent
            if parent:
                # 尝试找到描述元素
                desc_elem = parent.find(class_=lambda x: x and ('description' in str(x).lower() or 'EraCard_description' in x))
                if desc_elem:
                    description = desc_elem.get_text(strip=True)
                    print(f"  找到描述: {description[:50]}..." if len(description) > 50 else f"  找到描述: {description}")
                
                # 尝试找到封面元素
                # 直接查找所有img标签
                all_imgs = soup.find_all('img')
                print(f"  在HTML中找到 {len(all_imgs)} 个图片元素")
                
                # 首先查找包含 EraCard_cover__huWx9 类的 img 标签
                cover_imgs = [img for img in all_imgs if img.get('class') and 'EraCard_cover__huWx9' in img.get('class')]
                print(f"  找到 {len(cover_imgs)} 个封面图片元素")
                
                # 对每个封面图片进行检查
                for img in cover_imgs:
                    # 检查alt属性是否包含专辑名称
                    if 'alt' in img.attrs and album_name.lower() in img['alt'].lower():
                        print(f"  找到匹配专辑 '{album_name}' 的封面图片")
                        
                        # 优先使用 srcset 属性中的 2x 图片
                        if 'srcset' in img.attrs:
                            srcset = img['srcset']
                            print(f"  原始 srcset: {srcset}")
                            
                            # 尝试提取 2x 图片URL
                            import re
                            # 匹配模式: URL 后面跟着 2x
                            match = re.search(r'(https?://[^\s]+)\s+2x', srcset)
                            if match:
                                cover_img = match.group(1)
                                print(f"  从 srcset 中提取到 2x 图片: {cover_img}")
                                break  # 找到合适的封面图片后退出循环
                
                # 如果还是没有找到封面图片，尝试查找所有包含专辑名称的图片
                if cover_img == "/images/eras/default.jpg":
                    for img in all_imgs:
                        if 'alt' in img.attrs and album_name.lower() in img['alt'].lower():
                            print(f"  找到匹配专辑 '{album_name}' 的图片")
                            
                            # 优先使用 srcset 属性中的 2x 图片
                            if 'srcset' in img.attrs:
                                srcset = img['srcset']
                                print(f"  原始 srcset: {srcset}")
                                
                                # 尝试提取 2x 图片URL
                                import re
                                # 匹配模式: URL 后面跟着 2x
                                match = re.search(r'(https?://[^\s]+)\s+2x', srcset)
                                if match:
                                    cover_img = match.group(1)
                                    print(f"  从 srcset 中提取到 2x 图片: {cover_img}")
                                    break  # 找到合适的封面图片后退出循环
                            
                            # 如果没有 srcset 或提取失败，使用 src 属性
                            if cover_img == "/images/eras/default.jpg" and 'src' in img.attrs:
                                src = img['src']
                                if src.startswith('http'):
                                    cover_img = src
                                    print(f"  使用 src 属性作为封面: {cover_img}")
                                    break
            
            # 添加到专辑数据列表
            eras_data.append({
                "id": era_id,
                "name": album_name,
                "trackCount": 0,  # 稍后更新
                "backgroundColor": ERA_COLORS.get(album_name, DEFAULT_COLOR),
                "coverImage": cover_img,
                "artist": "Ye",
                "artistId": "ye"
            })
            
            # 创建专辑详情
            era_details[era_id] = {
                "id": era_id,
                "name": album_name,
                "description": description,
                "backgroundColor": ERA_COLORS.get(album_name, DEFAULT_COLOR),
                "coverImage": cover_img,
                "tracks": []
            }
    
    # 如果没有找到任何专辑数据，尝试使用备用方法
    if not eras_data:
        print("未找到任何专辑数据，使用备用方法...")
        # 使用预定义的专辑列表创建基本数据
        for album_name in ALBUM_NAMES:
            era_id = slugify(album_name)
            
            # 添加到专辑数据列表
            eras_data.append({
                "id": era_id,
                "name": album_name,
                "trackCount": 0,
                "backgroundColor": ERA_COLORS.get(album_name, DEFAULT_COLOR),
                "coverImage": "/images/eras/default.jpg",
                "artist": "Ye",
                "artistId": "ye"
            })
            
            # 创建专辑详情
            era_details[era_id] = {
                "id": era_id,
                "name": album_name,
                "description": "",
                "backgroundColor": ERA_COLORS.get(album_name, DEFAULT_COLOR),
                "coverImage": "/images/eras/default.jpg",
                "tracks": []
            }
    
    # 从页面中的details元素提取曲目信息
    # 清空之前的专辑数据，确保按照网页中的顺序展示
    eras_data = []
    era_details = {}
    
    details_elements = soup.find_all('details', class_='EraCard_era__RaoRE')
    print(f"找到 {len(details_elements)} 个EraCard_era__RaoRE details元素")
    
    # 遍历每个专辑的details元素
    for details in details_elements:
        # 获取专辑名称
        summary = details.find('summary')
        if not summary:
            continue
            
        # 尝试从样式中提取背景色
        background_color = ""
        if 'style' in summary.attrs:
            style = summary['style']
            import re
            bg_match = re.search(r'background-color:([^;]+)', style)
            if bg_match:
                background_color = bg_match.group(1).strip()
                print(f"从样式中提取到背景色: {background_color}")
        
        # 查找专辑标题
        title_elem = None
        for div in details.find_all('div'):
            if div.get('class') and any('title' in cls.lower() for cls in div.get('class')):
                title_elem = div
                break
                
        if not title_elem:
            continue
            
        album_name = title_elem.get_text(strip=True)
        print(f"处理专辑details: {album_name}")
        
        # 创建专辑ID
        era_id = slugify(album_name)
        
        # 查找曲目容器
        tracks_container = details.find('div', class_='EraCard_tracks__UJDwG')
        if not tracks_container:
            # 尝试查找包含'tracks'的类名
            for div in details.find_all('div'):
                if div.get('class') and any('tracks' in cls.lower() for cls in div.get('class')):
                    tracks_container = div
                    print(f"找到曲目容器: {div.get('class')}")
                    break
                    
        if not tracks_container:
            print(f"未找到专辑 {album_name} 的曲目容器")
            continue
            
        # 检查该专辑是否已存在于我们的数据中
        if era_id not in era_details:
            # 获取专辑封面
            cover_img = "/images/eras/default.jpg"  # 默认封面
            if album_name in album_covers:
                cover_img = album_covers[album_name]
                print(f"使用预先提取的封面图片: {cover_img}")
            
            # 获取专辑描述
            description_elem = details.find('div', class_=lambda x: x and 'description' in str(x).lower())
            era_description = description_elem.get_text(strip=True) if description_elem else ""
            
            # 创建专辑数据
            eras_data.append({
                "id": era_id,
                "name": album_name,
                "trackCount": 0,  # 稍后更新
                "backgroundColor": ERA_COLORS.get(album_name, background_color if background_color else DEFAULT_COLOR),
                "coverImage": cover_img,
                "artist": "Ye",
                "artistId": "ye"
            })
            
            # 创建专辑详情
            era_details[era_id] = {
                "id": era_id,
                "name": album_name,
                "description": era_description,
                "backgroundColor": ERA_COLORS.get(album_name, background_color if background_color else DEFAULT_COLOR),
                "coverImage": cover_img,
                "tracks": []
            }
        
        # 处理曲目
        # 处理第一种情况：带id的div
        track_divs_with_id = tracks_container.find_all('div', id=True)
        print(f"找到 {len(track_divs_with_id)} 个带ID的曲目元素")
        
        for track_div in track_divs_with_id:
            track_id = track_div['id']
            track_class = track_div.get('class', [])
            track_content = track_div.get('data-content', '')
            
            # 如果没有data-content，尝试获取文本内容
            if not track_content:
                track_content = track_div.get_text(strip=True)
            
            # 提取曲目名称
            track_name = track_content
            track_notes = ""
            
            # 如果内容包含引号或特殊字符，尝试分离曲目名称和备注
            if "-" in track_content:
                parts = track_content.split("-", 1)
                track_name = parts[0].strip()
                track_notes = parts[1].strip() if len(parts) > 1 else ""
            elif ":" in track_content:
                parts = track_content.split(":", 1)
                track_name = parts[0].strip()
                track_notes = parts[1].strip() if len(parts) > 1 else ""
            
            # 提取艺术家和制作人信息
            artists = extract_artists_from_text(track_content)
            producers = extract_producers_from_text(track_content)
            
            # 提取原始元数据
            original_url = ""
            # 如果有id，使用id作为原始URL的一部分
            if track_id:
                original_url = f"track-{track_id}"
            
            # 创建曲目数据
            track_data = {
                "id": slugify(track_name) if track_name else f"track-{track_id}",
                "name": track_name,
                "artists": artists,
                "producers": producers,
                "notes": track_notes,
                "length": "",  # 可能需要从其他地方提取
                "fileDate": "",
                "leakDate": "",
                "availableLength": "",
                "quality": "",
                "links": [],
                "eraId": era_id,
                "originalUrl": original_url,  # 添加原始元数据
                "originalContent": track_content  # 保存原始内容
            }
            
            # 确保曲目ID唯一
            existing_ids = [t['id'] for t in era_details[era_id]['tracks']]
            if track_data['id'] in existing_ids:
                track_data['id'] = f"{track_data['id']}-{len(era_details[era_id]['tracks'])+1}"
            
            # 添加到专辑曲目列表
            era_details[era_id]['tracks'].append(track_data)
            print(f"添加曲目: {track_name} 到专辑 {album_name}")
        
        # 处理第二种情况：不带id的div
        track_divs_without_id = [div for div in tracks_container.find_all('div') if not div.get('id') and div.get('class') and any('Track_track' in cls for cls in div.get('class'))]
        print(f"找到 {len(track_divs_without_id)} 个不带ID的曲目元素")
        
        for i, track_div in enumerate(track_divs_without_id):
            track_content = track_div.get('data-content', '')
            
            # 如果没有data-content，尝试获取文本内容
            if not track_content:
                track_content = track_div.get_text(strip=True)
            
            # 提取曲目名称
            track_name = track_content
            track_notes = ""
            
            # 如果内容包含引号或特殊字符，尝试分离曲目名称和备注
            if "-" in track_content:
                parts = track_content.split("-", 1)
                track_name = parts[0].strip()
                track_notes = parts[1].strip() if len(parts) > 1 else ""
            elif ":" in track_content:
                parts = track_content.split(":", 1)
                track_name = parts[0].strip()
                track_notes = parts[1].strip() if len(parts) > 1 else ""
            
            # 提取艺术家和制作人信息
            artists = extract_artists_from_text(track_content)
            producers = extract_producers_from_text(track_content)
            
            # 提取原始元数据
            original_url = f"track-no-id-{i+1}"
            
            # 创建曲目数据
            track_data = {
                "id": slugify(track_name) if track_name else f"track-{i+1}",
                "name": track_name,
                "artists": artists,
                "producers": producers,
                "notes": track_notes,
                "length": "",
                "fileDate": "",
                "leakDate": "",
                "availableLength": "",
                "quality": "",
                "links": [],
                "eraId": era_id,
                "originalUrl": original_url,  # 添加原始元数据
                "originalContent": track_content  # 保存原始内容
            }
            
            # 确保曲目ID唯一
            existing_ids = [t['id'] for t in era_details[era_id]['tracks']]
            if track_data['id'] in existing_ids:
                track_data['id'] = f"{track_data['id']}-{len(era_details[era_id]['tracks'])+1}"
            
            # 添加到专辑曲目列表
            era_details[era_id]['tracks'].append(track_data)
            print(f"添加曲目: {track_name} 到专辑 {album_name}")
        
        # 处理第三种情况：非div元素
        # 查找所有非div元素，如span或a标签等
        non_div_tracks = []
        for element in tracks_container.find_all():
            if element.name != 'div' and element.get_text(strip=True) and not element.find_all():
                non_div_tracks.append(element)
        
        print(f"找到 {len(non_div_tracks)} 个非div的曲目元素")
        
        for i, track_elem in enumerate(non_div_tracks):
            track_content = track_elem.get_text(strip=True)
            
            # 跳过空元素
            if not track_content:
                continue
                
            # 提取曲目名称
            track_name = track_content
            track_notes = ""
            
            # 如果内容包含引号或特殊字符，尝试分离曲目名称和备注
            if "-" in track_content:
                parts = track_content.split("-", 1)
                track_name = parts[0].strip()
                track_notes = parts[1].strip() if len(parts) > 1 else ""
            elif ":" in track_content:
                parts = track_content.split(":", 1)
                track_name = parts[0].strip()
                track_notes = parts[1].strip() if len(parts) > 1 else ""
            
            # 提取艺术家和制作人信息
            artists = extract_artists_from_text(track_content)
            producers = extract_producers_from_text(track_content)
            
            # 提取原始元数据
            original_url = f"track-non-div-{i+1}"
            
            # 创建曲目数据
            track_data = {
                "id": slugify(track_name) if track_name else f"non-div-track-{i+1}",
                "name": track_name,
                "artists": artists,
                "producers": producers,
                "notes": track_notes,
                "length": "",
                "fileDate": "",
                "leakDate": "",
                "availableLength": "",
                "quality": "",
                "links": [],
                "eraId": era_id,
                "originalUrl": original_url,  # 添加原始元数据
                "originalContent": track_content  # 保存原始内容
            }
            
            # 确保曲目ID唯一
            existing_ids = [t['id'] for t in era_details[era_id]['tracks']]
            if track_data['id'] in existing_ids:
                track_data['id'] = f"{track_data['id']}-{len(era_details[era_id]['tracks'])+1}"
            
            # 添加到专辑曲目列表
            era_details[era_id]['tracks'].append(track_data)
            print(f"添加曲目: {track_name} 到专辑 {album_name}")
    
    # 添加一个示例曲目到第一个专辑
    if eras_data and len(eras_data) > 0:
        first_album_id = eras_data[0]['id']
        
        # 添加示例曲目
        era_details[first_album_id]['tracks'].append({
            "id": "yo-is-a-benz",
            "name": "Yo Is A Benz",
            "artists": ["Rhymefest"],
            "producers": [],
            "notes": "Track 10 from Go Getters' 1999 compilation tape 'World Record Holders'",
            "length": "3:56",
            "fileDate": "",
            "leakDate": "",
            "availableLength": "Full",
            "quality": "Low Quality",
            "links": [],
            "eraId": first_album_id
        })
    
    # 手动添加一些示例曲目，以防HTML解析不完整
    # 这些是根据图片中看到的内容添加的
    before_cd_id = slugify("Before The College Dropout")
    if len(era_details[before_cd_id]['tracks']) == 0:
        # 添加图片中看到的曲目
        era_details[before_cd_id]['tracks'].append({
            "id": "yo-is-a-benz",
            "name": "Yo Is A Benz",
            "artists": ["Rhymefest"],
            "producers": [],
            "notes": "Track 10 from Go Getters' 1999 compilation tape 'World Record Holders'",
            "length": "3:56",
            "fileDate": "",
            "leakDate": "",
            "availableLength": "Full",
            "quality": "Low Quality",
            "links": [],
            "eraId": before_cd_id
        })
    
    # 更新曲目数量
    for era in eras_data:
        era_id = era["id"]
        if era_id in era_details:
            era["trackCount"] = len(era_details[era_id]["tracks"])
    
    # 更新专辑的曲目数量
    for era in eras_data:
        era_id = era["id"]
        if era_id in era_details:
            track_count = len(era_details[era_id]["tracks"])
            era["trackCount"] = track_count
    
    # 在开发阶段，我们保留所有专辑，即使它们没有曲目
    # 这样可以在前端显示所有专辑，并在后续开发中添加曲目
    # eras_data = [era for era in eras_data if era["trackCount"] > 0]
    # era_details = {era_id: details for era_id, details in era_details.items() if len(details["tracks"]) > 0}
    
    # 写入eras.json到分类目录
    with open(CATEGORY_DIR / 'eras.json', 'w', encoding='utf-8') as f:
        json.dump(eras_data, f, ensure_ascii=False, indent=2)
    
    # 写入各时期详情文件
    for era_id, details in era_details.items():
        with open(ERAS_DIR / f'{era_id}.json', 'w', encoding='utf-8') as f:
            json.dump(details, f, ensure_ascii=False, indent=2)
    
    # 创建分类信息文件
    category_info = {
        "id": "unreleased",
        "name": "Unreleased",
        "description": "Leaked and unreleased tracks from various Ye eras.",
        "artistId": "ye",
        "artist": "Ye",
        "eraCount": len(eras_data),
        "trackCount": sum(era["trackCount"] for era in eras_data),
        "backgroundColor": "#333333"
    }
    
    with open(CATEGORY_DIR / 'info.json', 'w', encoding='utf-8') as f:
        json.dump(category_info, f, ensure_ascii=False, indent=2)
    
    # 计算处理时间
    end_time = time.time()
    elapsed_time = end_time - start_timeF
    minutes, seconds = divmod(elapsed_time, 60)
    
    print(f"处理完成！共生成 {len(eras_data)} 个时期的数据。")
    print(f"总耗时: {int(minutes)}分{int(seconds)}秒")

# 全局变量定义在模块级别，不需要在函数内部使用global关键字

def update_html_file(new_path):
    """更新HTML文件路径"""
    global HTML_FILE
    HTML_FILE = Path(new_path)
    print(f"使用指定的HTML文件: {HTML_FILE}")

if __name__ == "__main__":
    # 支持命令行参数，允许指定要解析的HTML文件
    if len(sys.argv) > 1:
        # 使用命令行参数指定的HTML文件
        html_file_path = sys.argv[1]
        update_html_file(html_file_path)
    
    parse_html()
