#!/usr/bin/env node

/**
 * 图片下载脚本 - 在构建前运行，将所有外部图片下载到本地
 * 
 * 使用方法: node scripts/download-images.js
 */

import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';
import { fileURLToPath } from 'url';
import { createRequire } from 'module';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// 图片下载配置
const config = {
  // 图片保存基础路径
  imagesBasePath: path.join(rootDir, 'public', 'images'),
  // 数据文件路径
  dataPath: path.join(rootDir, 'public', 'data'),
  // 已下载图片的记录文件
  downloadedImagesLog: path.join(rootDir, 'scripts', 'downloaded-images.json'),
  // 并发下载数量限制 - 降低并发数以避免被限制
  concurrencyLimit: 3,
  // 批次之间的间隔时间（毫秒）
  batchDelay: 2000
};

// 已下载图片的映射表 (原URL -> 本地路径)
let downloadedImages = {};

// 初始化函数
async function init() {
  try {
    // 尝试加载已下载图片的记录
    const logExists = await fileExists(config.downloadedImagesLog);
    if (logExists) {
      const logData = await fs.readFile(config.downloadedImagesLog, 'utf-8');
      downloadedImages = JSON.parse(logData);
      console.log(`已加载${Object.keys(downloadedImages).length}条已下载图片记录`);
    }
  } catch (error) {
    console.error('初始化失败:', error);
    // 继续执行，使用空的下载记录
    downloadedImages = {};
  }
}

// 主函数
async function main() {
  try {
    await init();
    
    // 1. 扫描所有JSON数据文件
    console.log('正在扫描数据文件...');
    const jsonFiles = await findAllJsonFiles(config.dataPath);
    console.log(`找到${jsonFiles.length}个JSON文件`);
    
    // 2. 从JSON文件中提取所有图片URL
    console.log('正在提取图片URL...');
    const imageUrls = await extractImageUrls(jsonFiles);
    console.log(`找到${Object.keys(imageUrls).length}个图片URL`);
    
    // 3. 下载图片
    console.log('开始下载图片...');
    await downloadImages(imageUrls);
    
    // 4. 更新JSON数据
    console.log('正在更新JSON数据...');
    await updateJsonFiles(jsonFiles, downloadedImages);
    
    // 5. 保存下载记录
    await fs.writeFile(
      config.downloadedImagesLog, 
      JSON.stringify(downloadedImages, null, 2), 
      'utf-8'
    );
    
    console.log('所有操作完成!');
  } catch (error) {
    console.error('执行过程中发生错误:', error);
    process.exit(1);
  }
}

// 查找所有JSON文件
async function findAllJsonFiles(dir) {
  const files = [];
  
  async function scanDir(currentDir) {
    const entries = await fs.readdir(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry.name);
      
      if (entry.isDirectory()) {
        await scanDir(fullPath);
      } else if (entry.isFile() && entry.name.endsWith('.json')) {
        files.push(fullPath);
      }
    }
  }
  
  await scanDir(dir);
  return files;
}

// 从JSON文件中提取图片URL
async function extractImageUrls(jsonFiles) {
  const imageUrls = {};
  
  for (const file of jsonFiles) {
    try {
      const data = await fs.readFile(file, 'utf-8');
      const json = JSON.parse(data);
      
      // 递归查找所有图片URL
      findImagesInObject(json, file, imageUrls);
    } catch (error) {
      console.error(`处理文件${file}时出错:`, error);
    }
  }
  
  return imageUrls;
}

// 递归查找对象中的图片URL
function findImagesInObject(obj, filePath, imageUrls) {
  if (!obj) return;
  
  if (typeof obj === 'object' && !Array.isArray(obj)) {
    // 检查常见的图片字段
    const imageFields = [
      'coverImage', 'image', 'thumbnail', 'background', 'avatar', 'logo', 'icon',
      'cover', 'photo', 'picture', 'img', 'banner', 'artwork', 'albumArt', 'albumCover'
    ];
    
    for (const [key, value] of Object.entries(obj)) {
      // 字符串处理 - 检查是否是图片URL
      if (typeof value === 'string') {
        // 检查是否是图片相关字段或URL看起来像图片
        if (isExternalImageUrl(value) && (
            imageFields.includes(key) || 
            key.toLowerCase().includes('image') || 
            key.toLowerCase().includes('img') || 
            key.toLowerCase().includes('cover') || 
            key.toLowerCase().includes('photo') || 
            key.toLowerCase().includes('icon') || 
            key.toLowerCase().includes('avatar')
          )) {
          // 这看起来是一个图片URL
          if (!imageUrls[value]) {
            imageUrls[value] = {
              files: [filePath],
              keys: [`${key} in ${path.basename(filePath)}`]
            };
            console.log(`发现图片URL: ${value} (字段: ${key})`);
          } else {
            if (!imageUrls[value].files.includes(filePath)) {
              imageUrls[value].files.push(filePath);
            }
            imageUrls[value].keys.push(`${key} in ${path.basename(filePath)}`);
          }
        }
      } else if (typeof value === 'object') {
        // 递归处理嵌套对象和数组
        findImagesInObject(value, filePath, imageUrls);
      }
    }
  } else if (Array.isArray(obj)) {
    // 递归处理数组中的每个项
    for (const item of obj) {
      findImagesInObject(item, filePath, imageUrls);
    }
  }
}

// 下载图片
async function downloadImages(imageUrls) {
  const urls = Object.keys(imageUrls).filter(url => !downloadedImages[url]);
  
  if (urls.length === 0) {
    console.log('没有新图片需要下载');
    return;
  }
  
  console.log(`需要下载${urls.length}张图片`);
  
  // 使用并发限制进行下载
  const chunks = chunkArray(urls, config.concurrencyLimit);
  let downloadedCount = 0;
  let successCount = 0;
  let failedCount = 0;
  
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    console.log(`开始下载批次 ${i+1}/${chunks.length} (并发数: ${chunk.length})`);
    
    const promises = chunk.map(url => downloadImage(url, imageUrls[url]));
    
    const results = await Promise.allSettled(promises);
    
    // 处理结果
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        downloadedCount++;
        const url = chunk[index];
        downloadedImages[url] = result.value;
        
        if (result.value !== '/images/placeholder.svg') {
          successCount++;
        } else {
          failedCount++;
        }
      } else {
        failedCount++;
      }
    });
    
    console.log(`批次 ${i+1} 完成: 成功 ${successCount}, 失败 ${failedCount}, 总进度 ${downloadedCount}/${urls.length}`);
    
    // 如果不是最后一个批次，等待一段时间再开始下一批
    if (i < chunks.length - 1) {
      console.log(`等待 ${config.batchDelay/1000} 秒后开始下一批...`);
      await new Promise(resolve => setTimeout(resolve, config.batchDelay));
    }
  }
  
  console.log(`下载完成: 成功 ${successCount}, 失败 ${failedCount}, 总计 ${urls.length}`);
}

// 下载单个图片
async function downloadImage(url, metadata) {
  try {
    // 生成文件名和保存路径
    const urlHash = crypto.createHash('md5').update(url).digest('hex');
    const fileExtension = getFileExtension(url);
    const fileName = `${urlHash}${fileExtension}`;
    
    // 确定保存目录
    // 根据文件路径推断艺术家/分类/专辑结构
    let savePath = path.join(config.imagesBasePath, 'downloaded');
    
    // 尝试从文件路径中提取结构化信息
    for (const filePath of metadata.files) {
      if (filePath.includes('/artists/')) {
        const relativePath = path.relative(config.dataPath, filePath);
        const parts = relativePath.split(path.sep);
        
        if (parts.length >= 3 && parts[0] === 'artists') {
          const artistId = parts[1];
          
          if (parts.length >= 5 && parts[2] === 'categories') {
            const categoryId = parts[3];
            savePath = path.join(config.imagesBasePath, 'artists', artistId, 'categories', categoryId);
            
            if (parts.length >= 7 && parts[4] === 'eras') {
              const eraId = parts[5];
              savePath = path.join(savePath, 'eras');
            }
            
            break;
          } else {
            savePath = path.join(config.imagesBasePath, 'artists', artistId);
            break;
          }
        }
      }
    }
    
    // 确保目录存在
    await fs.mkdir(savePath, { recursive: true });
    
    const filePath = path.join(savePath, fileName);
    const relativePath = '/' + path.relative(path.join(rootDir, 'public'), filePath).replace(/\\/g, '/');
    
    // 检查文件是否已经存在
    if (await fileExists(filePath)) {
      console.log(`图片已存在: ${relativePath}`);
      return relativePath;
    }
    
    // 下载图片
    console.log(`下载图片: ${url} -> ${relativePath}`);
    
    // 添加重试机制
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 2000; // 2秒
    const TIMEOUT = 30000; // 30秒
    
    let success = false;
    let lastError;
    
    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), TIMEOUT);
        
        const response = await fetch(url, {
          signal: controller.signal,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.google.com/'
          }
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
          throw new Error(`下载失败: ${response.status} ${response.statusText}`);
        }
        
        const arrayBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        await fs.writeFile(filePath, buffer);
        
        success = true;
        break; // 下载成功，跳出重试循环
      } catch (error) {
        lastError = error;
        if (attempt < MAX_RETRIES) {
          console.log(`下载图片失败，正在重试 (${attempt}/${MAX_RETRIES}): ${url}`);
          // 等待一段时间再重试
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        }
      }
    }
    
    if (!success) {
      throw lastError || new Error('下载失败，达到最大重试次数');
    }
    
    return relativePath;
  } catch (error) {
    console.error(`下载图片失败 ${url}:`, error);
    
    // 返回占位符图片路径
    return '/images/placeholder.svg';
  }
}

// 更新JSON文件中的图片URL
async function updateJsonFiles(jsonFiles, urlMap) {
  for (const file of jsonFiles) {
    try {
      const data = await fs.readFile(file, 'utf-8');
      let json = JSON.parse(data);
      
      // 递归更新所有图片URL
      const updated = { value: false };
      const updatedJson = updateImagesInObject(json, urlMap, updated);
      
      if (updated.value) {
        await fs.writeFile(file, JSON.stringify(updatedJson, null, 2), 'utf-8');
        console.log(`已更新文件: ${file}`);
      }
    } catch (error) {
      console.error(`更新文件${file}时出错:`, error);
    }
  }
}

// 递归更新对象中的图片URL
function updateImagesInObject(obj, urlMap, updated) {
  if (!obj) return obj;
  
  // 使用引用来跟踪更新状态
  let wasUpdated = false;
  
  if (typeof obj === 'object' && !Array.isArray(obj)) {
    const newObj = {};
    
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'string' && isExternalImageUrl(value) && urlMap[value]) {
        newObj[key] = urlMap[value];
        wasUpdated = true;
      } else if (typeof value === 'object') {
        const result = updateImagesInObject(value, urlMap, updated);
        newObj[key] = result;
        // 检查子对象是否有更新
        if (JSON.stringify(result) !== JSON.stringify(value)) {
          wasUpdated = true;
        }
      } else {
        newObj[key] = value;
      }
    }
    
    if (wasUpdated) {
      updated.value = true;
    }
    
    return newObj;
  } else if (Array.isArray(obj)) {
    const newArray = [];
    let arrayUpdated = false;
    
    for (let i = 0; i < obj.length; i++) {
      const result = updateImagesInObject(obj[i], urlMap, updated);
      newArray.push(result);
      // 检查数组项是否有更新
      if (JSON.stringify(result) !== JSON.stringify(obj[i])) {
        arrayUpdated = true;
      }
    }
    
    if (arrayUpdated) {
      updated.value = true;
    }
    
    return newArray;
  }
  
  return obj;
}

// 辅助函数

// 检查文件是否存在
async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

// 从URL中获取文件扩展名
function getFileExtension(url) {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const lastDotIndex = pathname.lastIndexOf('.');
    
    if (lastDotIndex !== -1 && lastDotIndex < pathname.length - 1) {
      return pathname.substring(lastDotIndex);
    }
  } catch (error) {
    console.error('解析URL失败:', error);
  }
  
  return '.jpg';
}

// 检查URL是否是外部图片URL
function isExternalImageUrl(url) {
  if (typeof url !== 'string') return false;
  
  // 检查是否是URL
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return false;
  }
  
  // 特别检查Google Drive链接
  if (url.includes('googleusercontent.com') || 
      url.includes('drive.google.com') || 
      url.includes('lh3.googleusercontent.com') || 
      url.includes('lh7-rt.googleusercontent.com')) {
    return true;
  }
  
  // 检查是否是图片URL
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp', '.bmp', '.ico'];
  const lowercaseUrl = url.toLowerCase();
  
  return imageExtensions.some(ext => lowercaseUrl.endsWith(ext)) || 
         lowercaseUrl.includes('images') || 
         lowercaseUrl.includes('img') ||
         lowercaseUrl.includes('photos') ||
         lowercaseUrl.includes('cover') ||
         lowercaseUrl.includes('avatar') ||
         lowercaseUrl.includes('thumbnail');
}

// 将数组分成多个小块
function chunkArray(array, chunkSize) {
  const chunks = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

// 执行主函数
main();
