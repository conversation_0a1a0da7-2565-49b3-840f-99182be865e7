import json
import os
import logging
import re
import time
import argparse
from bs4 import BeautifulSoup, Comment
from datetime import datetime
# python3 scripts/parse_html_to_json.py --input data/PlayboiBest.html --output-dir public/data/artists/playboi-carti/categories/best-of --artist playboi-carti --category best-of
# python3 scripts/parse_html_to_json.py --input data/PlayboiRecent.html --output-dir public/data/artists/playboi-carti/categories/recent --artist playboi-carti --category recent
# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(asctime)s: %(message)s')

def clean_text(text):
    """移除多余的空白字符和指定的表情符号。"""
    if text:
        text = re.sub(r'[✨🗑️]', '', text).strip()
        return ' '.join(text.split())
    return None

def parse_html_to_sequential_tracks(html_content):
    print("  开始使用 BeautifulSoup 解析 HTML...")
    start_parse_time = time.time()
    soup = BeautifulSoup(html_content, 'html.parser')
    parse_duration = time.time() - start_parse_time
    print(f"  HTML 解析完成，耗时: {parse_duration:.2f} 秒")

    # --- 解析 __NEXT_DATA__ ---
    next_data_tracks = {}
    next_data_script = soup.find('script', id='__NEXT_DATA__')
    next_data = {}
    if next_data_script:
        logging.info("  找到 __NEXT_DATA__ script 标签，开始解析...")
        script_content = next_data_script.string # Get raw content
        
        try:
            next_data = json.loads(script_content) # Try parsing raw content
            logging.info(f"  __NEXT_DATA__ JSON 成功解析。顶层键: {list(next_data.keys())}") # Print top-level keys

            # 尝试从 props.pageProps.best 获取数据（适用于 YeBest.html）
            best_items = next_data.get('props', {}).get('pageProps', {}).get('best', [])
            
            # 如果 best_items 不存在，则尝试从 props.pageProps.recent 获取数据（适用于 YeRecent.html）
            if not best_items or not isinstance(best_items, list):
                best_items = next_data.get('props', {}).get('pageProps', {}).get('recent', [])
            
            if best_items and isinstance(best_items, list):
                logging.info(f"  找到音轨列表，包含 {len(best_items)} 个项目。开始提取音轨...")
                # Create a lookup dictionary using track 'id' or 'originalUrl' as the key
                for item in best_items:
                    if isinstance(item, dict) and 'children' in item and isinstance(item['children'], list):
                        tracks_list = item['children']
                        for track in tracks_list:
                            if isinstance(track, dict) and track.get('type') == 'track': # Ensure it's a track object
                                track_id = track.get('id')
                                original_url = track.get('originalUrl')
                                
                                if track_id:
                                    next_data_tracks[track_id] = track
                                elif original_url: 
                                    # Fallback to originalUrl if id is missing
                                    logging.warning(f"  __NEXT_DATA__ Track missing 'id', using 'originalUrl' as key: {track.get('title', 'N/A')}")
                                    next_data_tracks[original_url] = track
                                else:
                                    logging.warning(f"  __NEXT_DATA__ Track missing both 'id' and 'originalUrl', cannot index: {track.get('title', 'N/A')}")
                
                if next_data_tracks:
                    logging.info(f"  成功从 __NEXT_DATA__ 解析并索引了 {len(next_data_tracks)} 个音轨。")
                else:
                    logging.warning("  在音轨列表的 'children' 中未找到有效的音轨数据。")
            else:
                 logging.warning("  在 __NEXT_DATA__ 中未找到有效的音轨列表。")

        except json.JSONDecodeError as e:
            logging.error(f"  错误：解析 __NEXT_DATA__ JSON 时失败: {e}")
            # Print context around the error (using the raw script_content)
            context_start = max(0, e.pos - 50)
            context_end = min(len(script_content), e.pos + 50)
            error_context = script_content[context_start:context_end]
            pointer = " " * (e.pos - context_start) + "^"
            logging.error(f"  错误位置附近内容 (pos {e.pos}):\n  Context: {error_context}\n  Pointer: {pointer}")
        except Exception as e:
            logging.error(f"  解析 __NEXT_DATA__ 时发生未知错误: {e}")
    else:
        logging.warning("  未找到 __NEXT_DATA__ script 标签。")

    # --- 结束解析 __NEXT_DATA__ ---

    # 使用列表来按顺序存储每个 Era Group 的数据
    all_eras_data = []
    total_processed_tracks = 0
    missing_in_next_data_count = 0 # 计数器

    era_groups = soup.find_all('details', class_='EraGroup_era__PTfzH')
    total_eras_found = len(era_groups)
    print(f"  共找到 {total_eras_found} 个 <details> Era group(s)。")

    if total_eras_found == 0:
        print("警告：未找到任何 class='EraGroup_era__PTfzH' 的 <details> 标签。请检查 HTML 文件和 class 名称。")
        return []

    for index, era_group in enumerate(era_groups):
        start_era_time = time.time()
        era_name = 'Unknown Era'
        current_era_tracks = [] # List to hold tracks for this specific era group instance
        processed_tracks_in_this_instance = 0

        try:
            # 提取 Era 名称
            era_name_tag = era_group.find('summary', class_='arrow')
            if era_name_tag and era_name_tag.find('span'):
                era_name = clean_text(era_name_tag.find('span').get_text())
            else:
                summary_text = clean_text(era_group.find('summary').get_text() if era_group.find('summary') else None)
                if summary_text:
                    era_name = summary_text
                else:
                    print(f"警告：Era Group {index + 1}/{total_eras_found} 未能提取到名称。使用 'Unknown Era'。")

            print(f"--- 开始处理 Era Group {index + 1}/{total_eras_found} (名称: '{era_name}') ---")

            tracks_container = era_group.find('div', class_='EraGroup_tracks__h3q9r')
            if not tracks_container:
                print(f"  警告: 在 Era Group '{era_name}' (索引 {index+1}) 中未找到 'EraGroup_tracks__h3q9r' 容器。此 Era Group 将没有音轨。")
            else:
                track_divs = tracks_container.find_all('div', class_='Track_track__b2YEg', recursive=False)
                total_tracks_in_this_instance = len(track_divs)
                print(f"  在 Era Group '{era_name}' (索引 {index+1}) 中找到 {total_tracks_in_this_instance} 个 track div(s)。")

                for track_index, track_div in enumerate(track_divs):
                    try:
                        track_data = {}
                        # --- 提取基础数据 (HTML 可见部分) ---
                        html_track_id = track_div.get('id') # ID from the div tag
                        if not html_track_id:
                             # 如果没有id，先尝试从链接找？或者生成一个？这里需要一个可靠的ID
                             # 尝试从 `data-content` 解析 `originalUrl` 或类似标识符可能更鲁棒
                             # 暂时先生成一个临时ID
                            html_track_id = f"generated_id_{era_name.replace(' ', '_')}_{index}_{track_index}"
                            print(f"  警告: Era Group '{era_name}' (索引 {index+1}), Track Div {track_index + 1} 缺少 'id' 属性，使用生成的 ID: {html_track_id}")

                        track_data['id'] = html_track_id # Use the div's ID initially

                        # 标题 (HTML)
                        title_container = track_div.find('div', recursive=False)
                        html_title = None
                        if title_container:
                            title_inner_div = title_container.find('div', recursive=False)
                            if title_inner_div:
                                title_span = title_inner_div.find('span', recursive=False)
                                if title_span:
                                    html_title = clean_text(title_span.get_text())
                                else:
                                    text_nodes = []
                                    for elem in title_inner_div.contents:
                                        if isinstance(elem, str):
                                            text_nodes.append(elem)
                                        elif elem.name == 'span' and 'Track_tag' not in elem.get('class', []):
                                            text_nodes.append(elem.get_text())
                                    html_title = clean_text("".join(text_nodes))
                        track_data['title'] = html_title

                        # 艺术家 (HTML)
                        artists_div = track_div.find('div', class_='Track_artists__94fRt')
                        track_data['artists'] = clean_text(artists_div.get_text() if artists_div else None)

                        # 时长 (HTML)
                        length_div = track_div.find('div', class_='Track_length__KUiUO')
                        track_data['length'] = clean_text(length_div.get_text() if length_div else None)

                        # 备注 和 OG Filename (HTML data-content)
                        notes_raw = track_div.get('data-content')
                        og_filename = None
                        notes_clean = None
                        if notes_raw:
                            notes_raw = notes_raw.strip()
                            og_match = re.match(r"OG Filename(?:s)?:(.*?)(?:\n(.*)|$)", notes_raw, re.DOTALL | re.IGNORECASE)
                            if og_match:
                                og_filename = clean_text(og_match.group(1))
                                notes_clean = clean_text(og_match.group(2))
                            else:
                                notes_clean = clean_text(notes_raw)
                        track_data['ogFilename'] = og_filename
                        track_data['notes'] = notes_clean

                        # 标签 (HTML)
                        tags = [clean_text(tag.get_text()) for tag in track_div.find_all('span', class_='Track_tag__bK7_v')]
                        track_data['tags'] = tags if tags else []

                        # 别名 (HTML)
                        aliases_div = track_div.find('div', class_='Track_aliases__yo_m5')
                        aliases = []
                        if aliases_div:
                            comment_text = ''.join(aliases_div.find_all(string=lambda text: isinstance(text, Comment)))
                            cleaned_comment = clean_text(comment_text)
                            if cleaned_comment:
                                aliases = [a.strip() for a in cleaned_comment.split(',') if a.strip()]
                            else:
                                raw_aliases_text = clean_text(aliases_div.get_text(strip=True))
                                if raw_aliases_text:
                                    aliases = [a.strip() for a in raw_aliases_text.split(',') if a.strip()]
                        track_data['aliases'] = aliases

                        # --- 尝试从 __NEXT_DATA__ 合并/补充信息 ---
                        # Use html_track_id (which is the div's ID) to lookup in next_data
                        if html_track_id in next_data_tracks:
                            next_track = next_data_tracks[html_track_id]
                            logging.info(f"    找到匹配的 __NEXT_DATA__ 音轨 (ID: {html_track_id})，正在合并...")
                            for key, value in next_track.items():
                                if key not in track_data:
                                    track_data[key] = value
                            logging.info(f"    合并完成。为音轨 '{track_data.get('title', 'N/A')}' 添加了来自 __NEXT_DATA__ 的额外字段。")
                            
                            # 添加 originalContent 字段，确保包含 artists 和 url 信息
                            if 'originalUrl' in next_track and 'originalContent' not in track_data:
                                track_data['originalContent'] = {
                                    'url': next_track.get('originalUrl'),
                                    'artists': next_track.get('artists')
                                }
                        else:
                            # 尝试根据标题和艺术家匹配
                            matched = False
                            for track_id, next_track in next_data_tracks.items():
                                if (track_data.get('title') and next_track.get('title') and 
                                    clean_text(track_data['title']) == clean_text(next_track['title']) and
                                    track_data.get('artists') and next_track.get('artists') and
                                    clean_text(track_data['artists']) == clean_text(next_track['artists'])):
                                    logging.info(f"    通过标题和艺术家匹配到 __NEXT_DATA__ 音轨，正在合并...")
                                    for key, value in next_track.items():
                                        if key not in track_data:
                                            track_data[key] = value
                                    
                                    # 添加 originalContent 字段
                                    if 'originalUrl' in next_track and 'originalContent' not in track_data:
                                        track_data['originalContent'] = {
                                            'url': next_track.get('originalUrl'),
                                            'artists': next_track.get('artists')
                                        }
                                    matched = True
                                    break
                            
                            if not matched:
                                logging.warning(f"    在 __NEXT_DATA__ 中未找到匹配的音轨: '{track_data.get('title', 'N/A')}' by {track_data.get('artists', 'N/A')}")
                                missing_in_next_data_count += 1
                                
                                # 即使没有匹配，也尝试添加 originalContent 字段
                                if track_data.get('artists') and not track_data.get('originalContent'):
                                    track_data['originalContent'] = {
                                        'artists': track_data.get('artists')
                                    }

                        # --- 如果最终标题存在，则添加到当前 Era Group 的列表 ---
                        if track_data.get('title'):
                            current_era_tracks.append(track_data)
                            total_processed_tracks += 1
                            processed_tracks_in_this_instance += 1
                        else:
                            print(f"  警告: 在 Era Group '{era_name}' (索引 {index+1}), Track Div {track_index + 1} 中跳过缺少最终标题的音轨。ID: {html_track_id}")

                    except Exception as e:
                        track_id_for_error = track_div.get('id', f'index_{track_index}')
                        print(f"错误：处理 Era Group '{era_name}' (索引 {index+1}), Track Div {track_index + 1} (ID/Index: {track_id_for_error}) 时发生错误: {e}")
                        import traceback
                        traceback.print_exc()
                        continue

            # Append the era group data (even if tracks are empty)
            all_eras_data.append({"era": era_name, "tracks": current_era_tracks})

            era_duration = time.time() - start_era_time
            print(f"--- 完成处理 Era Group {index + 1}/{total_eras_found} ('{era_name}', {processed_tracks_in_this_instance} 个有效音轨)，耗时: {era_duration:.2f} 秒 ---")

        except Exception as e:
             print(f"严重错误：处理 Era Group {index + 1} 时发生意外错误: {e}")
             import traceback
             traceback.print_exc()
             continue # 继续处理下一个 Era Group

    # Final summary log
    final_era_groups_count = len(all_eras_data)
    print(f"\n解析完成。共处理了 {final_era_groups_count} 个 Era Group 实例。")
    print(f"总共成功解析并添加了 {total_processed_tracks} 个音轨到相应的 Era Group 实例中。")
    if missing_in_next_data_count > 0:
        print(f"提示：有 {missing_in_next_data_count} 个音轨在 HTML 中找到，但在 __NEXT_DATA__ 中未找到匹配的 ID。")

    return all_eras_data

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='解析 HTML 文件并提取音轨数据到 JSON 文件')
    parser.add_argument('--input', '-i', required=True, help='输入 HTML 文件路径')
    parser.add_argument('--output-dir', '-o', required=True, help='输出目录路径')
    parser.add_argument('--artist', '-a', default='ye', help='艺术家 ID (默认: ye)')
    parser.add_argument('--category', '-c', required=True, help='分类 ID')
    args = parser.parse_args()

    # 构建输出文件路径
    output_dir = os.path.join('public', 'data', 'artists', args.artist, 'categories', args.category)
    output_file_path = os.path.join(output_dir, 'tracks.json')

    print(f"开始运行脚本...")
    script_start_time = time.time()
    print(f"读取 HTML 文件: {args.input}")
    if not os.path.exists(args.input):
        print(f"错误：输入 HTML 文件未找到于 {args.input}")
        exit(1)

    try:
        with open(args.input, 'r', encoding='utf-8') as f:
            print("  正在读取文件内容...")
            html_content = f.read()
            print("  文件内容读取完毕。")

        sequential_data = parse_html_to_sequential_tracks(html_content)

        if not sequential_data:
             print("错误：未能解析出任何数据，输出文件将不会被创建或更新。")
             exit(1)

        # 确保输出目录存在
        print(f"检查并确保输出目录存在: {output_dir}")
        os.makedirs(output_dir, exist_ok=True)

        # 写入 JSON 文件
        final_era_group_count = len(sequential_data)
        final_track_count = sum(len(era['tracks']) for era in sequential_data)
        print(f"开始将 {final_era_group_count} 个 Era Group 实例的 {final_track_count} 个音轨写入 JSON 文件: {output_file_path}")
        write_start_time = time.time()
        with open(output_file_path, 'w', encoding='utf-8') as f:
            json.dump(sequential_data, f, ensure_ascii=False, indent=2)
        write_duration = time.time() - write_start_time
        print(f"JSON 文件写入完成，耗时: {write_duration:.2f} 秒。")

        script_duration = time.time() - script_start_time
        print(f"\n脚本执行成功！总耗时: {script_duration:.2f} 秒。")
        print(f"结果已保存到: {output_file_path}")

    except Exception as e:
        print(f"\n脚本执行过程中发生严重错误: {e}")
        import traceback
        traceback.print_exc()
        script_duration = time.time() - script_start_time
        print(f"脚本因错误中止。总耗时: {script_duration:.2f} 秒。")
        exit(1)

if __name__ == "__main__":
    main()
