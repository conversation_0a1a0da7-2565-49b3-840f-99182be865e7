#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图片下载脚本 - 在构建前运行，将所有外部图片下载到本地
使用方法: python scripts/download_images.py
"""

import os
import json
import hashlib
import asyncio
import aiohttp
import re
import time
from pathlib import Path
from urllib.parse import urlparse
from typing import Dict, List, Set, Tuple, Any, Optional

# 配置
class Config:
    # 项目根目录
    ROOT_DIR = Path(__file__).parent.parent
    # 图片保存基础路径
    IMAGES_BASE_PATH = ROOT_DIR / "public" / "images"
    # 数据文件路径
    DATA_PATH = ROOT_DIR / "public" / "data"
    # 已下载图片的记录文件
    DOWNLOADED_IMAGES_LOG = ROOT_DIR / "scripts" / "downloaded-images.json"
    # 并发下载数量限制
    CONCURRENCY_LIMIT = 3
    # 批次之间的间隔时间（秒）
    BATCH_DELAY = 2
    # 下载超时时间（秒）
    DOWNLOAD_TIMEOUT = 30
    # 重试次数
    MAX_RETRIES = 3
    # 重试间隔（秒）
    RETRY_DELAY = 2
    # 占位符图片路径
    PLACEHOLDER_IMAGE = "/images/placeholder.svg"

# 已下载图片的映射表 (原URL -> 本地路径)
downloaded_images = {}

# 初始化函数
async def init():
    """初始化下载记录"""
    try:
        # 尝试加载已下载图片的记录
        if os.path.exists(Config.DOWNLOADED_IMAGES_LOG):
            with open(Config.DOWNLOADED_IMAGES_LOG, 'r', encoding='utf-8') as f:
                global downloaded_images
                downloaded_images = json.load(f)
            print(f"已加载{len(downloaded_images)}条已下载图片记录")
    except Exception as e:
        print(f"初始化失败: {e}")
        # 继续执行，使用空的下载记录
        downloaded_images = {}

# 检查URL是否是外部图片URL
def is_external_image_url(url: str) -> bool:
    """检查URL是否是外部图片URL"""
    if not isinstance(url, str):
        return False
    
    # 检查是否是URL
    if not url.startswith('http://') and not url.startswith('https://'):
        return False
    
    # 特别检查Google Drive链接
    if any(domain in url for domain in [
        'googleusercontent.com',
        'drive.google.com',
        'lh3.googleusercontent.com',
        'lh7-rt.googleusercontent.com'
    ]):
        return True
    
    # 检查是否是图片URL
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp', '.bmp', '.ico']
    url_lower = url.lower()
    
    return any(url_lower.endswith(ext) for ext in image_extensions) or \
           any(keyword in url_lower for keyword in [
               'images', 'img', 'photos', 'cover', 'avatar', 'thumbnail'
           ])

# 从URL中获取文件扩展名
def get_file_extension(url: str) -> str:
    """从URL中获取文件扩展名"""
    try:
        parsed_url = urlparse(url)
        path = parsed_url.path
        last_dot_index = path.rfind('.')
        
        if last_dot_index != -1 and last_dot_index < len(path) - 1:
            return path[last_dot_index:]
    except Exception as e:
        print(f"解析URL出错: {e}")
    
    # 如果URL中没有扩展名或解析出错，默认使用.jpg
    return '.jpg'

# 查找所有JSON文件
async def find_all_json_files(dir_path: Path) -> List[Path]:
    """查找所有JSON文件"""
    json_files = []
    
    for root, _, files in os.walk(dir_path):
        for file in files:
            if file.endswith('.json'):
                json_files.append(Path(root) / file)
    
    return json_files

# 递归查找对象中的图片URL
def find_images_in_object(obj: Any, file_path: Path, image_urls: Dict[str, Dict]) -> None:
    """递归查找对象中的图片URL"""
    if obj is None:
        return
    
    if isinstance(obj, dict):
        # 检查常见的图片字段
        image_fields = [
            'coverImage', 'image', 'thumbnail', 'background', 'avatar', 'logo', 'icon',
            'cover', 'photo', 'picture', 'img', 'banner', 'artwork', 'albumArt', 'albumCover'
        ]
        
        for key, value in obj.items():
            # 字符串处理 - 检查是否是图片URL
            if isinstance(value, str):
                # 检查是否是图片相关字段或URL看起来像图片
                if is_external_image_url(value) and (
                    key in image_fields or
                    any(keyword in key.lower() for keyword in [
                        'image', 'img', 'cover', 'photo', 'icon', 'avatar'
                    ])
                ):
                    # 这看起来是一个图片URL
                    if value not in image_urls:
                        image_urls[value] = {
                            'files': [str(file_path)],
                            'keys': [f"{key} in {file_path.name}"]
                        }
                        print(f"发现图片URL: {value} (字段: {key})")
                    else:
                        if str(file_path) not in image_urls[value]['files']:
                            image_urls[value]['files'].append(str(file_path))
                        image_urls[value]['keys'].append(f"{key} in {file_path.name}")
            elif isinstance(value, (dict, list)):
                # 递归处理嵌套对象和数组
                find_images_in_object(value, file_path, image_urls)
    
    elif isinstance(obj, list):
        # 递归处理数组中的每个项
        for item in obj:
            find_images_in_object(item, file_path, image_urls)

# 从JSON文件中提取图片URL
async def extract_image_urls(json_files: List[Path]) -> Dict[str, Dict]:
    """从JSON文件中提取图片URL"""
    image_urls = {}
    
    for file in json_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # 递归查找所有图片URL
            find_images_in_object(json_data, file, image_urls)
        except Exception as e:
            print(f"处理文件{file}时出错: {e}")
    
    return image_urls

# 下载单个图片
async def download_image(session: aiohttp.ClientSession, url: str, metadata: Dict) -> str:
    """下载单个图片"""
    try:
        # 生成文件名和保存路径
        url_hash = hashlib.md5(url.encode()).hexdigest()
        file_extension = get_file_extension(url)
        file_name = f"{url_hash}{file_extension}"
        
        # 确定保存目录
        # 根据文件路径推断艺术家/分类/专辑结构
        save_path = Config.IMAGES_BASE_PATH / "downloaded"
        
        # 尝试从文件路径中提取结构化信息
        for file_path in metadata['files']:
            if '/artists/' in file_path:
                relative_path = os.path.relpath(file_path, Config.DATA_PATH)
                parts = relative_path.split(os.sep)
                
                if len(parts) >= 3 and parts[0] == 'artists':
                    artist_id = parts[1]
                    
                    if len(parts) >= 5 and parts[2] == 'categories':
                        category_id = parts[3]
                        save_path = Config.IMAGES_BASE_PATH / "artists" / artist_id / "categories" / category_id
                        
                        if len(parts) >= 7 and parts[4] == 'eras':
                            save_path = save_path / "eras"
                        
                        break
                    else:
                        save_path = Config.IMAGES_BASE_PATH / "artists" / artist_id
                        break
        
        # 确保目录存在
        os.makedirs(save_path, exist_ok=True)
        
        file_path = save_path / file_name
        relative_path = '/' + os.path.relpath(file_path, Config.ROOT_DIR / "public").replace('\\', '/')
        
        # 检查文件是否已经存在
        if os.path.exists(file_path):
            print(f"图片已存在: {relative_path}")
            return relative_path
        
        # 下载图片
        print(f"下载图片: {url} -> {relative_path}")
        
        # 添加重试机制
        for attempt in range(1, Config.MAX_RETRIES + 1):
            try:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
                    'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Referer': 'https://www.google.com/'
                }
                
                async with session.get(url, headers=headers, timeout=Config.DOWNLOAD_TIMEOUT) as response:
                    if response.status != 200:
                        raise Exception(f"下载失败: {response.status} {response.reason}")
                    
                    content = await response.read()
                    
                    with open(file_path, 'wb') as f:
                        f.write(content)
                    
                    return relative_path
            except Exception as e:
                if attempt < Config.MAX_RETRIES:
                    print(f"下载图片失败，正在重试 ({attempt}/{Config.MAX_RETRIES}): {url}")
                    # 等待一段时间再重试
                    await asyncio.sleep(Config.RETRY_DELAY)
                else:
                    print(f"下载图片失败 {url}: {e}")
                    raise
        
        return relative_path
    except Exception as e:
        print(f"下载图片失败 {url}: {e}")
        
        # 返回占位符图片路径
        return Config.PLACEHOLDER_IMAGE

# 下载图片
async def download_images(image_urls: Dict[str, Dict]) -> None:
    """下载图片"""
    urls = [url for url in image_urls.keys() if url not in downloaded_images]
    
    if not urls:
        print("没有新图片需要下载")
        return
    
    print(f"需要下载{len(urls)}张图片")
    
    # 使用并发限制进行下载
    chunks = [urls[i:i + Config.CONCURRENCY_LIMIT] for i in range(0, len(urls), Config.CONCURRENCY_LIMIT)]
    downloaded_count = 0
    success_count = 0
    failed_count = 0
    
    # 创建一个共享的会话
    async with aiohttp.ClientSession() as session:
        for i, chunk in enumerate(chunks):
            print(f"开始下载批次 {i+1}/{len(chunks)} (并发数: {len(chunk)})")
            
            tasks = [download_image(session, url, image_urls[url]) for url in chunk]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for j, result in enumerate(results):
                downloaded_count += 1
                url = chunk[j]
                
                if isinstance(result, Exception):
                    failed_count += 1
                    downloaded_images[url] = Config.PLACEHOLDER_IMAGE
                else:
                    downloaded_images[url] = result
                    if result != Config.PLACEHOLDER_IMAGE:
                        success_count += 1
                    else:
                        failed_count += 1
            
            print(f"批次 {i+1} 完成: 成功 {success_count}, 失败 {failed_count}, 总进度 {downloaded_count}/{len(urls)}")
            
            # 如果不是最后一个批次，等待一段时间再开始下一批
            if i < len(chunks) - 1:
                print(f"等待 {Config.BATCH_DELAY} 秒后开始下一批...")
                await asyncio.sleep(Config.BATCH_DELAY)
    
    print(f"下载完成: 成功 {success_count}, 失败 {failed_count}, 总计 {len(urls)}")

# 递归更新对象中的图片URL
def update_images_in_object(obj: Any, url_map: Dict[str, str], updated: Dict[str, bool]) -> Any:
    """递归更新对象中的图片URL"""
    if obj is None:
        return obj
    
    # 使用引用来跟踪更新状态
    was_updated = False
    
    if isinstance(obj, dict):
        new_obj = {}
        
        for key, value in obj.items():
            if isinstance(value, str) and is_external_image_url(value) and value in url_map:
                new_obj[key] = url_map[value]
                was_updated = True
            elif isinstance(value, (dict, list)):
                result = update_images_in_object(value, url_map, updated)
                new_obj[key] = result
                # 检查子对象是否有更新
                if json.dumps(result) != json.dumps(value):
                    was_updated = True
            else:
                new_obj[key] = value
        
        if was_updated:
            updated['value'] = True
        
        return new_obj
    elif isinstance(obj, list):
        new_array = []
        array_updated = False
        
        for item in obj:
            result = update_images_in_object(item, url_map, updated)
            new_array.append(result)
            # 检查数组项是否有更新
            if json.dumps(result) != json.dumps(item):
                array_updated = True
        
        if array_updated:
            updated['value'] = True
        
        return new_array
    
    return obj

# 更新JSON文件中的图片URL
async def update_json_files(json_files: List[Path], url_map: Dict[str, str]) -> None:
    """更新JSON文件中的图片URL"""
    for file in json_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # 递归更新所有图片URL
            updated = {'value': False}
            updated_json = update_images_in_object(json_data, url_map, updated)
            
            if updated['value']:
                with open(file, 'w', encoding='utf-8') as f:
                    json.dump(updated_json, f, ensure_ascii=False, indent=2)
                print(f"已更新文件: {file}")
        except Exception as e:
            print(f"更新文件{file}时出错: {e}")

# 主函数
async def main():
    """主函数"""
    try:
        await init()
        
        # 1. 扫描所有JSON数据文件
        print("正在扫描数据文件...")
        json_files = await find_all_json_files(Config.DATA_PATH)
        print(f"找到{len(json_files)}个JSON文件")
        
        # 2. 从JSON文件中提取所有图片URL
        print("正在提取图片URL...")
        image_urls = await extract_image_urls(json_files)
        print(f"找到{len(image_urls)}个图片URL")
        
        # 3. 下载图片
        print("开始下载图片...")
        await download_images(image_urls)
        
        # 4. 更新JSON数据
        print("正在更新JSON数据...")
        await update_json_files(json_files, downloaded_images)
        
        # 5. 保存下载记录
        with open(Config.DOWNLOADED_IMAGES_LOG, 'w', encoding='utf-8') as f:
            json.dump(downloaded_images, f, ensure_ascii=False, indent=2)
        
        print("所有操作完成!")
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        exit(1)

# 执行主函数
if __name__ == "__main__":
    asyncio.run(main())
