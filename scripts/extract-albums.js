#!/usr/bin/env node

/**
 * 专辑信息提取脚本
 * 从tracks.json和eras/*.json文件中提取专辑/时期信息
 * 生成albums.json文件供表单使用
 *
 * 支持三种分类处理策略：
 * 1. ERAS_BASED: 使用 eras.json 文件 (如: unreleased)
 * 2. NAME_BASED: 使用 tracks.json 中的 name 字段 (如: art)
 * 3. ERA_BASED: 使用 tracks.json 中的 era 字段 (如: recent, best-of)
 *
 * 添加新分类：
 * 只需在 CATEGORY_STRATEGIES 配置中将新分类名添加到对应的数组即可
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

// 配置
const ARTISTS = ['ye', 'playboi-carti'];
const DATA_DIR = path.join(projectRoot, 'public', 'data', 'artists');

// 分类处理策略配置
const CATEGORY_STRATEGIES = {
  // 使用 eras.json 文件的分类
  ERAS_BASED: ['unreleased'],

  // 使用 tracks.json 中 name 字段的分类
  NAME_BASED: ['art'],

  // 使用 tracks.json 中 era 字段的分类
  ERA_BASED: ['recent', 'best-of']
};

/**
 * 获取分类的处理策略
 */
function getCategoryStrategy(categoryName) {
  if (CATEGORY_STRATEGIES.ERAS_BASED.includes(categoryName)) {
    return 'ERAS_BASED';
  }
  if (CATEGORY_STRATEGIES.NAME_BASED.includes(categoryName)) {
    return 'NAME_BASED';
  }
  if (CATEGORY_STRATEGIES.ERA_BASED.includes(categoryName)) {
    return 'ERA_BASED';
  }
  return 'UNKNOWN';
}

/**
 * 从tracks.json提取专辑信息 - ERA_BASED策略
 */
function extractAlbumsFromEraField(tracksData) {
  const albums = new Set();

  if (Array.isArray(tracksData)) {
    tracksData.forEach(collection => {
      if (collection.era) {
        albums.add(collection.era);
      }
    });
  }

  return Array.from(albums);
}

/**
 * 从tracks.json提取专辑信息 - NAME_BASED策略
 */
function extractAlbumsFromNameField(tracksData) {
  const albums = new Set();

  if (Array.isArray(tracksData)) {
    tracksData.forEach(collection => {
      // 添加collection的name字段
      if (collection.name) {
        albums.add(collection.name);
      }

      // 处理artworks中的tags
      if (collection.artworks) {
        collection.artworks.forEach(artwork => {
          if (artwork.tags) {
            artwork.tags.forEach(tag => albums.add(tag));
          }
        });
      }
    });
  }

  return Array.from(albums);
}

/**
 * 从tracks.json提取专辑信息 - 主函数
 */
async function extractAlbumsFromTracks(tracksPath, categoryName) {
  try {
    const tracksData = JSON.parse(await fs.readFile(tracksPath, 'utf-8'));
    const strategy = getCategoryStrategy(categoryName);

    let albums = [];

    switch (strategy) {
      case 'ERA_BASED':
        albums = extractAlbumsFromEraField(tracksData);
        break;

      case 'NAME_BASED':
        albums = extractAlbumsFromNameField(tracksData);
        break;

      case 'UNKNOWN':
        console.warn(`Unknown category strategy for: ${categoryName}`);
        return [];

      default:
        console.warn(`Unhandled strategy: ${strategy} for category: ${categoryName}`);
        return [];
    }

    return albums.filter(album => album && album.trim()).sort();
  } catch (error) {
    console.warn(`Warning: Could not extract albums from ${tracksPath}:`, error.message);
    return [];
  }
}

/**
 * 从eras.json提取时期信息
 */
async function extractAlbumsFromEras(erasPath) {
  try {
    const erasData = JSON.parse(await fs.readFile(erasPath, 'utf-8'));
    return erasData.map(era => ({
      id: era.id,
      name: era.name,
      coverImage: era.coverImage,
      backgroundColor: era.backgroundColor,
      trackCount: era.trackCount || 0
    })).sort((a, b) => a.name.localeCompare(b.name));
  } catch (error) {
    console.warn(`Warning: Could not extract eras from ${erasPath}:`, error.message);
    return [];
  }
}

/**
 * 处理单个分类
 */
async function processCategoryAlbums(artistPath, categoryName) {
  const categoryPath = path.join(artistPath, 'categories', categoryName);
  const albumsPath = path.join(categoryPath, 'albums.json');

  try {
    await fs.access(categoryPath);
  } catch {
    console.log(`Category ${categoryName} does not exist, skipping...`);
    return;
  }

  const strategy = getCategoryStrategy(categoryName);
  let albums = [];

  switch (strategy) {
    case 'ERAS_BASED':
      // 使用 eras.json 文件
      const erasPath = path.join(categoryPath, 'eras.json');
      try {
        await fs.access(erasPath);
        const erasAlbums = await extractAlbumsFromEras(erasPath);
        albums = erasAlbums;
      } catch {
        console.warn(`eras.json not found for ${categoryName} category`);
      }
      break;

    case 'ERA_BASED':
    case 'NAME_BASED':
      // 使用 tracks.json 文件
      const tracksPath = path.join(categoryPath, 'tracks.json');
      try {
        await fs.access(tracksPath);
        const tracksAlbums = await extractAlbumsFromTracks(tracksPath, categoryName);
        albums = tracksAlbums.map(name => ({
          id: name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
          name
        }));
      } catch {
        console.warn(`tracks.json not found for ${categoryName} category`);
      }
      break;

    case 'UNKNOWN':
      console.warn(`Unknown strategy for category: ${categoryName}`);
      break;
  }

  // 去重并排序
  const uniqueAlbums = albums.filter((album, index, self) =>
    index === self.findIndex(a => a.id === album.id)
  ).sort((a, b) => a.name.localeCompare(b.name));

  // 保存albums.json
  await fs.writeFile(albumsPath, JSON.stringify(uniqueAlbums, null, 2));
  console.log(`✓ Generated ${albumsPath} with ${uniqueAlbums.length} albums (strategy: ${strategy})`);

  return uniqueAlbums;
}

/**
 * 处理单个艺术家
 */
async function processArtistAlbums(artistName) {
  const artistPath = path.join(DATA_DIR, artistName);
  
  try {
    await fs.access(artistPath);
  } catch {
    console.log(`Artist ${artistName} does not exist, skipping...`);
    return;
  }
  
  console.log(`\nProcessing artist: ${artistName}`);
  
  // 获取所有分类
  const categoriesPath = path.join(artistPath, 'categories');
  let categories = [];
  
  try {
    categories = await fs.readdir(categoriesPath);
  } catch {
    console.log(`No categories found for ${artistName}`);
    return;
  }
  
  // 处理每个分类
  for (const category of categories) {
    const categoryPath = path.join(categoriesPath, category);
    const stat = await fs.stat(categoryPath);
    
    if (stat.isDirectory()) {
      await processCategoryAlbums(artistPath, category);
    }
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🎵 Starting album extraction...\n');
  
  try {
    // 确保数据目录存在
    await fs.access(DATA_DIR);
    
    // 处理每个艺术家
    for (const artist of ARTISTS) {
      await processArtistAlbums(artist);
    }
    
    console.log('\n✅ Album extraction completed successfully!');
    console.log('\n📁 Generated files:');
    console.log('   - public/data/artists/*/categories/*/albums.json');
    
  } catch (error) {
    console.error('❌ Error during album extraction:', error);
    process.exit(1);
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { extractAlbumsFromTracks, extractAlbumsFromEras, processCategoryAlbums };
