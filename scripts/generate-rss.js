import { Feed } from 'feed';
import fs from 'fs/promises';
import path from 'path';

const SITE_URL = 'https://aitrackerhive.com';
const LANGUAGES = ['en', 'ar', 'pt'];
const ARTISTS = ['ye', 'playboi-carti'];

async function generateArtistFeed(artist, lang) {
  const feed = new Feed({
    title: `TrackerHive - ${artist} Updates`,
    description: `Latest updates and content for ${artist} on TrackerHive`,
    id: `${SITE_URL}/${lang}/artists/${artist}`,
    link: `${SITE_URL}/${lang}/artists/${artist}`,
    language: lang,
    image: `${SITE_URL}/images/artists/${artist}/profile.jpg`,
    favicon: `${SITE_URL}/favicon.ico`,
    copyright: `All rights reserved ${new Date().getFullYear()}, TrackerHive`,
    generator: 'TrackerHive RSS Generator',
    feedLinks: {
      rss2: `${SITE_URL}/${lang}/artists/${artist}/feed.xml`,
      json: `${SITE_URL}/${lang}/artists/${artist}/feed.json`,
      atom: `${SITE_URL}/${lang}/artists/${artist}/feed.atom`
    }
  });

  // 这里应该从数据库或API获取最新内容
  // 目前使用示例数据
  const recentItems = [
    {
      title: `New ${artist} Content`,
      description: `Check out the latest content from ${artist}`,
      link: `${SITE_URL}/${lang}/artists/${artist}/recent`,
      date: new Date(),
      image: `${SITE_URL}/images/artists/${artist}/recent-og.jpg`
    }
  ];

  recentItems.forEach(item => {
    feed.addItem({
      title: item.title,
      id: item.link,
      link: item.link,
      description: item.description,
      date: item.date,
      image: item.image
    });
  });

  // 确保目录存在
  const outputDir = path.join(process.cwd(), 'public', lang, 'artists', artist);
  await fs.mkdir(outputDir, { recursive: true });

  // 生成不同格式的feed
  await Promise.all([
    fs.writeFile(path.join(outputDir, 'feed.xml'), feed.rss2()),
    fs.writeFile(path.join(outputDir, 'feed.atom'), feed.atom1()),
    fs.writeFile(path.join(outputDir, 'feed.json'), feed.json1())
  ]);
}

async function main() {
  try {
    // 为每个艺术家的每种语言生成feed
    for (const lang of LANGUAGES) {
      for (const artist of ARTISTS) {
        console.log(`Generating RSS feed for ${artist} in ${lang}...`);
        await generateArtistFeed(artist, lang);
      }
    }
    console.log('✅ Generated all RSS feeds');
  } catch (error) {
    console.error('Error generating RSS feeds:', error);
    process.exit(1);
  }
}

main();
