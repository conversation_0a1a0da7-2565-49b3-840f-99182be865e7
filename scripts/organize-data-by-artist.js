import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function organizeDataByArtist() {
  try {
    // 获取项目根目录
    const projectRoot = path.resolve(__dirname, '..');
    const dataDir = path.join(projectRoot, 'public', 'data');
    
    // 确保艺术家目录存在
    const artistsDir = path.join(dataDir, 'artists');
    try {
      await fs.mkdir(artistsDir, { recursive: true });
    } catch (err) {
      if (err.code !== 'EEXIST') throw err;
    }
    
    // 读取现有的 eras-index.json
    const erasIndexPath = path.join(dataDir, 'eras-index.json');
    const erasData = JSON.parse(await fs.readFile(erasIndexPath, 'utf-8'));
    
    // 读取 eras 目录中的所有 JSON 文件
    const erasDir = path.join(dataDir, 'eras');
    const eraFiles = await fs.readdir(erasDir);
    
    // 按艺术家组织数据
    const artistsMap = {};
    
    // 首先处理 eras-index.json 中的数据
    // 这里我们假设所有专辑都属于 Ye
    const yeEras = [];
    
    for (const era of erasData) {
      // 添加艺术家信息
      const eraWithArtist = {
        ...era,
        artist: "Ye",
        artistId: "ye"
      };
      
      yeEras.push(eraWithArtist);
      
      // 查找并处理对应的详细数据文件
      const eraFileName = `${era.id}.json`;
      if (eraFiles.includes(eraFileName)) {
        const eraDetailPath = path.join(erasDir, eraFileName);
        const eraDetailData = JSON.parse(await fs.readFile(eraDetailPath, 'utf-8'));
        
        // 将详细数据写入艺术家目录
        const artistEraDir = path.join(artistsDir, 'ye', 'eras');
        try {
          await fs.mkdir(artistEraDir, { recursive: true });
        } catch (err) {
          if (err.code !== 'EEXIST') throw err;
        }
        
        await fs.writeFile(
          path.join(artistEraDir, eraFileName),
          JSON.stringify(eraDetailData, null, 2)
        );
      }
    }
    
    // 保存 Ye 的专辑索引
    await fs.writeFile(
      path.join(artistsDir, 'ye', 'eras.json'),
      JSON.stringify(yeEras, null, 2)
    );
    
    // 创建艺术家索引
    const artistsIndex = [
      {
        id: "ye",
        name: "Ye",
        fullName: "Kanye West",
        description: "American rapper, record producer, and fashion designer",
        imageUrl: "/images/artists/ye.jpg",
        backgroundColor: "#5D4037",
        eraCount: yeEras.length,
        trackCount: yeEras.reduce((sum, era) => sum + era.trackCount, 0),
        featured: true
      }
    ];
    
    await fs.writeFile(
      path.join(dataDir, 'artists-index.json'),
      JSON.stringify(artistsIndex, null, 2)
    );
    
    console.log('数据已按艺术家成功组织！');
  } catch (error) {
    console.error('组织数据时出错:', error);
  }
}

organizeDataByArtist();
