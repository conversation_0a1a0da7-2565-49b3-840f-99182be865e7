#!/usr/bin/env node

/**
 * Redirect Verification Script for TrackerHive
 * Tests if the Vercel redirects are working correctly
 */

import https from 'https';
import { URL } from 'url';

const DOMAIN = 'aitrackerhive.com';

/**
 * Test URLs and their expected redirects
 */
const TEST_CASES = [
  {
    source: '/ar/ar/artists/ye/unreleased/',
    expected: '/ar/artists/ye/unreleased/',
    description: 'Double language prefix (ar/ar)'
  },
  {
    source: '/pt/pt/artists/playboi-carti/',
    expected: '/pt/artists/playboi-carti/',
    description: 'Double language prefix (pt/pt)'
  },
  {
    source: '/ar/pt/artists/ye/',
    expected: '/ar/artists/ye/',
    description: 'Mixed language prefix (ar/pt)'
  },
  {
    source: '/pt/ar/artists/playboi-carti/',
    expected: '/pt/artists/playboi-carti/',
    description: 'Mixed language prefix (pt/ar)'
  },
  {
    source: '/artists/undefined/unreleased/',
    expected: '/artists/ye/unreleased/',
    description: 'Undefined artist ID'
  },
  {
    source: '/artists/playboi-carti/fakes/',
    expected: '/artists/playboi-carti/unreleased/',
    description: 'Invalid category (fakes)'
  },
  {
    source: '/ar/artists/ye/stems/',
    expected: '/ar/artists/ye/unreleased/',
    description: 'Invalid category (stems) with language'
  },
  {
    source: '/cdn-cgi/l/email-protection',
    expected: '/',
    description: 'Cloudflare email protection'
  },
  {
    source: '/en/artists/ye/',
    expected: '/artists/ye/',
    description: 'Old /en/ path'
  }
];

/**
 * Check if a URL redirects correctly
 */
function checkRedirect(testCase) {
  return new Promise((resolve) => {
    const url = `https://${DOMAIN}${testCase.source}`;
    
    const options = {
      method: 'HEAD',
      timeout: 5000,
      headers: {
        'User-Agent': 'TrackerHive-Redirect-Checker/1.0'
      }
    };

    const req = https.request(url, options, (res) => {
      const result = {
        ...testCase,
        status: res.statusCode,
        location: res.headers.location || null,
        success: false,
        message: ''
      };

      if (res.statusCode === 301 || res.statusCode === 302) {
        const redirectUrl = res.headers.location;
        if (redirectUrl) {
          // Handle relative and absolute URLs
          const expectedPath = testCase.expected;
          const actualPath = redirectUrl.startsWith('http') 
            ? new URL(redirectUrl).pathname 
            : redirectUrl;

          if (actualPath === expectedPath) {
            result.success = true;
            result.message = `✅ Redirects correctly to ${actualPath}`;
          } else {
            result.message = `❌ Expected ${expectedPath}, got ${actualPath}`;
          }
        } else {
          result.message = `❌ Redirect status but no location header`;
        }
      } else if (res.statusCode === 200) {
        result.message = `⚠️ Returns 200 (no redirect) - may need time to propagate`;
      } else if (res.statusCode === 404) {
        result.message = `❌ Still returns 404 - redirect not working`;
      } else {
        result.message = `❓ Unexpected status: ${res.statusCode}`;
      }

      resolve(result);
    });

    req.on('error', (error) => {
      resolve({
        ...testCase,
        status: 'ERROR',
        location: null,
        success: false,
        message: `❌ Request failed: ${error.message}`
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        ...testCase,
        status: 'TIMEOUT',
        location: null,
        success: false,
        message: `❌ Request timeout`
      });
    });

    req.end();
  });
}

/**
 * Run all redirect tests
 */
async function runTests() {
  console.log('🔍 TrackerHive Redirect Verification');
  console.log('====================================');
  console.log(`Testing redirects on: https://${DOMAIN}`);
  console.log('');

  const results = [];
  
  for (const testCase of TEST_CASES) {
    console.log(`Testing: ${testCase.description}`);
    console.log(`  ${testCase.source} → ${testCase.expected}`);
    
    const result = await checkRedirect(testCase);
    results.push(result);
    
    console.log(`  ${result.message}`);
    console.log('');
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // Summary
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log('📊 Summary');
  console.log('==========');
  console.log(`✅ Successful redirects: ${successful}/${total}`);
  console.log(`❌ Failed redirects: ${total - successful}/${total}`);
  
  if (successful === total) {
    console.log('🎉 All redirects are working correctly!');
  } else {
    console.log('⚠️ Some redirects need attention. This may be due to:');
    console.log('   - Deployment still propagating (wait 5-10 minutes)');
    console.log('   - Vercel configuration not applied yet');
    console.log('   - DNS/CDN caching issues');
  }

  console.log('\n🚀 Next steps:');
  console.log('1. If redirects are not working, wait 5-10 minutes and test again');
  console.log('2. Check Vercel deployment logs for any errors');
  console.log('3. Monitor GSC for 404 error reduction over 24-48 hours');
  
  return results;
}

/**
 * Quick test for a single URL
 */
async function testSingleUrl(url) {
  const testCase = {
    source: url,
    expected: 'unknown',
    description: 'Manual test'
  };
  
  const result = await checkRedirect(testCase);
  console.log(`${url}: ${result.message}`);
  return result;
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const singleUrl = process.argv[2];
  
  if (singleUrl) {
    console.log(`Testing single URL: ${singleUrl}`);
    testSingleUrl(singleUrl);
  } else {
    runTests();
  }
}

export { runTests, testSingleUrl, checkRedirect };
