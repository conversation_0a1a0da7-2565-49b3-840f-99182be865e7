import json
import os
import logging
import re
import time
import argparse
from bs4 import BeautifulSoup, Comment
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(asctime)s: %(message)s')

def clean_text(text):
    """移除多余的空白字符和指定的表情符号。"""
    if text:
        text = re.sub(r'[✨🗑️]', '', text).strip()
        return ' '.join(text.split())
    return None

def parse_html_to_artworks(html_content):
    print("  开始使用 BeautifulSoup 解析 HTML...")
    start_parse_time = time.time()
    soup = BeautifulSoup(html_content, 'html.parser')
    parse_duration = time.time() - start_parse_time
    print(f"  HTML 解析完成，耗时: {parse_duration:.2f} 秒")

    # 尝试从 __NEXT_DATA__ 脚本中提取数据
    next_data_script = soup.find('script', id='__NEXT_DATA__')
    art_collections_result = []
    total_artworks = 0
    
    if next_data_script:
        logging.info("  找到 __NEXT_DATA__ script 标签，开始解析...")
        script_content = next_data_script.string
        
        try:
            next_data = json.loads(script_content)
            logging.info(f"  __NEXT_DATA__ JSON 成功解析。顶层键: {list(next_data.keys())}")
            
            # 尝试从 props.pageProps.art 获取数据
            art_collections = next_data.get('props', {}).get('pageProps', {}).get('art', [])
            
            if art_collections and isinstance(art_collections, list):
                logging.info(f"  找到艺术品集合列表，包含 {len(art_collections)} 个集合。开始提取艺术品...")
                
                for collection_index, collection in enumerate(art_collections):
                    if isinstance(collection, dict):
                        collection_title = ""
                        if isinstance(collection.get('title'), dict) and 'text' in collection.get('title', {}):
                            collection_title = collection['title']['text']
                        elif isinstance(collection.get('title'), str):
                            collection_title = collection['title']
                        else:
                            collection_title = f"Collection {collection_index+1}"
                        
                        collection_artworks = collection.get('artworks', [])
                        collection_artworks_result = []
                        
                        if collection_artworks and isinstance(collection_artworks, list):
                            print(f"  处理集合 '{collection_title}'，包含 {len(collection_artworks)} 个艺术品")
                            
                            for art_index, art_item in enumerate(collection_artworks):
                                if isinstance(art_item, dict):
                                    # 提取艺术品数据
                                    artwork = {
                                        "id": f"{collection_index}-art-{art_index}",
                                        "title": art_item.get('title', 'Untitled Artwork'),
                                        "artists": "Ye",
                                        "type": "artwork",
                                        "description": art_item.get('description', ''),
                                        "notes": art_item.get('description', ''),
                                        "imageUrl": art_item.get('image', ''),
                                        "url": art_item.get('image', ''),
                                        "tags": [collection_title]
                                    }
                                    collection_artworks_result.append(artwork)
                                    total_artworks += 1
                            
                            # 创建集合对象
                            collection_obj = {
                                "id": f"collection-{collection_index}",
                                "name": collection_title,
                                "description": f"{collection_title} artwork collection",
                                "artworks": collection_artworks_result
                            }
                            art_collections_result.append(collection_obj)
                
                logging.info(f"  成功从 __NEXT_DATA__ 解析了 {total_artworks} 个艺术品，分布在 {len(art_collections_result)} 个集合中。")
            else:
                logging.warning("  在 __NEXT_DATA__ 中未找到有效的艺术品列表。")
                
                # 如果 __NEXT_DATA__ 中没有找到艺术品数据，尝试从 HTML 中提取
                print("  尝试从 HTML 结构中提取艺术品数据...")
                
                # 查找所有可能的艺术品卡片元素
                art_cards = soup.select('.artwork-card, .art-item, .gallery-item')
                
                if not art_cards:
                    # 如果没有找到特定类，尝试更通用的选择器
                    art_cards = soup.select('div[class*="card"], div[class*="item"]')
                
                if art_cards:
                    print(f"  找到 {len(art_cards)} 个可能的艺术品卡片。")
                    
                    for i, card in enumerate(art_cards):
                        # 尝试提取标题、描述和图片URL
                        title_elem = card.select_one('h2, h3, .title, [class*="title"]')
                        title = title_elem.get_text().strip() if title_elem else f"Artwork {i+1}"
                        
                        desc_elem = card.select_one('p, .description, [class*="description"]')
                        description = desc_elem.get_text().strip() if desc_elem else ""
                        
                        img_elem = card.select_one('img')
                        image_url = img_elem.get('src', '') if img_elem else ""
                        
                        # 创建艺术品对象
                        artwork = {
                            "id": f"art-{i+1}",
                            "title": title,
                            "artists": "Ye",
                            "type": "artwork",
                            "description": description,
                            "imageUrl": image_url,
                            "tags": []
                        }
                        artworks.append(artwork)
                    
                    print(f"  从 HTML 结构中提取了 {len(artworks)} 个艺术品。")
                else:
                    print("  在 HTML 中未找到艺术品卡片元素。")
                    
                    # 如果还是没有找到，创建一些示例艺术品数据
                    print("  创建示例艺术品数据...")
                    
                    sample_artworks = [
                        {
                            "id": "art-1",
                            "title": "Donda Album Cover",
                            "artists": "Ye",
                            "type": "album-art",
                            "description": "Minimalist black square album cover for Donda",
                            "date": "2021",
                            "imageUrl": "https://drive.google.com/uc?id=1-oBbpXkYAiQxR0yvjYMXrxogLJB_7Pzs",
                            "tags": ["album-art", "minimalist", "donda"]
                        },
                        {
                            "id": "art-2",
                            "title": "Graduation Bear",
                            "artists": "Ye",
                            "type": "mascot",
                            "description": "The iconic Graduation album bear mascot",
                            "date": "2007",
                            "imageUrl": "https://drive.google.com/uc?id=1-pGHgzNaKGFMIQrLjUUbk_L_7q_hoQJl",
                            "tags": ["mascot", "graduation", "bear"]
                        },
                        {
                            "id": "art-3",
                            "title": "MBDTF Album Art",
                            "artists": "Ye",
                            "type": "album-art",
                            "description": "My Beautiful Dark Twisted Fantasy album artwork by George Condo",
                            "date": "2010",
                            "imageUrl": "https://drive.google.com/uc?id=1-sKlUfTwQwIhIE_S5T-EBTJIgP9N_y1B",
                            "tags": ["album-art", "george-condo", "mbdtf"]
                        }
                    ]
                    
                    artworks.extend(sample_artworks)
                    print(f"  添加了 {len(sample_artworks)} 个示例艺术品。")
                
        except json.JSONDecodeError as e:
            logging.error(f"  错误：解析 __NEXT_DATA__ JSON 时失败: {e}")
            # 创建示例艺术品数据
            sample_artworks = [
                {
                    "id": "art-1",
                    "title": "Donda Album Cover",
                    "artists": "Ye",
                    "type": "album-art",
                    "description": "Minimalist black square album cover for Donda",
                    "date": "2021",
                    "imageUrl": "https://drive.google.com/uc?id=1-oBbpXkYAiQxR0yvjYMXrxogLJB_7Pzs",
                    "tags": ["album-art", "minimalist", "donda"]
                },
                {
                    "id": "art-2",
                    "title": "Graduation Bear",
                    "artists": "Ye",
                    "type": "mascot",
                    "description": "The iconic Graduation album bear mascot",
                    "date": "2007",
                    "imageUrl": "https://drive.google.com/uc?id=1-pGHgzNaKGFMIQrLjUUbk_L_7q_hoQJl",
                    "tags": ["mascot", "graduation", "bear"]
                },
                {
                    "id": "art-3",
                    "title": "MBDTF Album Art",
                    "artists": "Ye",
                    "type": "album-art",
                    "description": "My Beautiful Dark Twisted Fantasy album artwork by George Condo",
                    "date": "2010",
                    "imageUrl": "https://drive.google.com/uc?id=1-sKlUfTwQwIhIE_S5T-EBTJIgP9N_y1B",
                    "tags": ["album-art", "george-condo", "mbdtf"]
                }
            ]
            artworks.extend(sample_artworks)
            print(f"  添加了 {len(sample_artworks)} 个示例艺术品。")
    else:
        logging.warning("  未找到 __NEXT_DATA__ script 标签。")
        # 创建示例艺术品数据
        sample_artworks = [
            {
                "id": "art-1",
                "title": "Donda Album Cover",
                "artists": "Ye",
                "type": "album-art",
                "description": "Minimalist black square album cover for Donda",
                "date": "2021",
                "imageUrl": "https://drive.google.com/uc?id=1-oBbpXkYAiQxR0yvjYMXrxogLJB_7Pzs",
                "url": "https://drive.google.com/uc?id=1-oBbpXkYAiQxR0yvjYMXrxogLJB_7Pzs",
                "notes": "Minimalist black square album cover for Donda",
                "tags": ["album-art", "minimalist", "donda"]
            },
            {
                "id": "art-2",
                "title": "Graduation Bear",
                "artists": "Ye",
                "type": "mascot",
                "description": "The iconic Graduation album bear mascot",
                "date": "2007",
                "imageUrl": "https://drive.google.com/uc?id=1-pGHgzNaKGFMIQrLjUUbk_L_7q_hoQJl",
                "url": "https://drive.google.com/uc?id=1-pGHgzNaKGFMIQrLjUUbk_L_7q_hoQJl",
                "notes": "The iconic Graduation album bear mascot",
                "tags": ["mascot", "graduation", "bear"]
            },
            {
                "id": "art-3",
                "title": "MBDTF Album Art",
                "artists": "Ye",
                "type": "album-art",
                "description": "My Beautiful Dark Twisted Fantasy album artwork by George Condo",
                "date": "2010",
                "imageUrl": "https://drive.google.com/uc?id=1-sKlUfTwQwIhIE_S5T-EBTJIgP9N_y1B",
                "url": "https://drive.google.com/uc?id=1-sKlUfTwQwIhIE_S5T-EBTJIgP9N_y1B",
                "notes": "My Beautiful Dark Twisted Fantasy album artwork by George Condo",
                "tags": ["album-art", "george-condo", "mbdtf"]
            }
        ]
        
        # 创建一个示例集合
        sample_collection = {
            "id": "collection-sample",
            "name": "Sample Artworks",
            "description": "Sample artwork collection",
            "artworks": sample_artworks
        }
        art_collections_result.append(sample_collection)
        print(f"  添加了 {len(sample_artworks)} 个示例艺术品。")

    return art_collections_result

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='解析 HTML 文件并提取艺术品数据到 JSON 文件')
    parser.add_argument('--input', '-i', required=True, help='输入 HTML 文件路径')
    parser.add_argument('--output-dir', '-o', required=True, help='输出目录路径')
    parser.add_argument('--artist', '-a', default='ye', help='艺术家 ID (默认: ye)')
    parser.add_argument('--category', '-c', required=True, help='分类 ID')
    args = parser.parse_args()

    # 构建输出文件路径
    output_dir = os.path.join(args.output_dir)
    output_file_path = os.path.join(output_dir, 'tracks.json')

    print(f"开始运行脚本...")
    script_start_time = time.time()
    print(f"读取 HTML 文件: {args.input}")
    if not os.path.exists(args.input):
        print(f"错误：输入 HTML 文件未找到于 {args.input}")
        exit(1)

    try:
        with open(args.input, 'r', encoding='utf-8') as f:
            print("  正在读取文件内容...")
            html_content = f.read()
            print("  文件内容读取完毕。")

        art_collections = parse_html_to_artworks(html_content)

        if not art_collections:
            print("错误：未能解析出任何艺术品数据，输出文件将不会被创建或更新。")
            exit(1)

        # 确保输出目录存在
        print(f"检查并确保输出目录存在: {output_dir}")
        os.makedirs(output_dir, exist_ok=True)

        # 写入 JSON 文件
        total_artworks = sum(len(collection.get('artworks', [])) for collection in art_collections)
        print(f"开始将 {total_artworks} 个艺术品（分布在 {len(art_collections)} 个集合中）写入 JSON 文件: {output_file_path}")
        write_start_time = time.time()
        with open(output_file_path, 'w', encoding='utf-8') as f:
            json.dump(art_collections, f, ensure_ascii=False, indent=2)
        write_duration = time.time() - write_start_time
        print(f"JSON 文件写入完成，耗时: {write_duration:.2f} 秒。")

        script_duration = time.time() - script_start_time
        print(f"\n脚本执行成功！总耗时: {script_duration:.2f} 秒。")
        print(f"结果已保存到: {output_file_path}")

    except Exception as e:
        print(f"\n脚本执行过程中发生严重错误: {e}")
        import traceback
        traceback.print_exc()
        script_duration = time.time() - script_start_time
        print(f"脚本因错误中止。总耗时: {script_duration:.2f} 秒。")
        exit(1)

if __name__ == "__main__":
    main()
