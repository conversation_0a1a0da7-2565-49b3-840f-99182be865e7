#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re
from bs4 import BeautifulSoup

def extract_next_data_json(html_content):
    """
    从HTML中提取__NEXT_DATA__ script标签中的JSON数据
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    next_data_script = soup.find('script', id='__NEXT_DATA__')
    
    if next_data_script:
        try:
            next_data = json.loads(next_data_script.string)
            return next_data
        except json.JSONDecodeError as e:
            print(f"解析__NEXT_DATA__中的JSON数据时出错: {e}")
            return None
    return None

def main():
    # 读取测试HTML文件
    with open('data/test_next_data.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 提取JSON数据
    next_data = extract_next_data_json(html_content)
    
    if next_data:
        print("成功提取JSON数据")
        # 获取unreleased数据
        unreleased = next_data.get('props', {}).get('pageProps', {}).get('unreleased', [])
        
        if unreleased:
            print(f"找到 {len(unreleased)} 个专辑数据")
            
            # 打印第一个专辑的信息
            first_era = unreleased[0]
            print(f"专辑名称: {first_era.get('title', {}).get('text', '')}")
            print(f"专辑封面: {first_era.get('cover', '')}")
            
            # 打印曲目信息
            children = first_era.get('children', [])
            print(f"找到 {len(children)} 个曲目")
            
            for i, track in enumerate(children):
                if track.get('type') == 'track':
                    print(f"曲目 {i+1}:")
                    print(f"  标题: {track.get('title', '')}")
                    print(f"  艺术家: {track.get('artists', '')}")
                    print(f"  原始URL: {track.get('originalUrl', '')}")
        else:
            print("未找到unreleased数据")
    else:
        print("未能提取JSON数据")

if __name__ == "__main__":
    main()
