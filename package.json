{"name": "density-dwarf", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "download-images": "node --experimental-json-modules scripts/download-images.js", "generate-og": "node scripts/generate-og-images.js", "generate-sitemap": "node scripts/generate-sitemaps.js", "generate-rss": "node scripts/generate-rss.js", "extract-albums": "node scripts/extract-albums.js", "indexnow": "node scripts/indexnow-submit.js", "indexnow-integration": "node scripts/indexnow-integration.js", "analyze-404": "node scripts/fix-404-errors.js", "verify-redirects": "node scripts/verify-redirects.js", "generate-seo": "npm run generate-og && npm run generate-sitemap && npm run generate-rss", "prebuild": "npm run extract-albums && npm run generate-seo", "postbuild": "npm run indexnow-integration"}, "dependencies": {"@astrojs/react": "^4.2.3", "astro": "^5.6.1", "canvas": "^3.1.0", "csv-parser": "^3.2.0", "feed": "^4.2.2", "lucide-react": "^0.487.0", "react": "^19.1.0", "react-dom": "^19.1.0", "sharp": "^0.34.0", "sitemap": "^8.0.0"}, "devDependencies": {"@astrojs/node": "^9.1.3", "@astrojs/tailwind": "^6.0.2", "@shadcn/ui": "^0.0.4", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "astro-compress": "^2.3.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "xml-formatter": "^3.6.5"}}