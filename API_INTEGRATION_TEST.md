# API集成测试文档

## 🎯 API集成概述

已成功将外部API `https://lh-api.dxwvv.com/api/v1/track` 集成到TrackerHive的track提交系统中。**调用API接口的同时保留原有数据结构到本地存储。**

## 📋 API请求格式

### 请求URL
```
POST https://lh-api.dxwvv.com/api/v1/track
```

### 请求头
```json
{
  "Content-Type": "application/json"
}
```

### 请求体参数
```json
{
  "artist": "艺术家名称",
  "category": "分类名称", 
  "album": "专辑名称",
  "track_title": "歌曲标题",
  "content": "内容描述"
}
```

## 🔧 集成实现

### 修改的文件
1. `src/components/content-submission/SimpleSubmissionForm.astro`
2. `src/components/content-submission/CollapsibleSubmissionForm.astro`

### 数据映射
- `artist`: 从表单的artistId获取对应的艺术家名称
- `category`: 从表单的categoryId获取对应的分类名称
- `album`: 从表单的albumId获取对应的专辑名称（如果没有则为"Unknown Album"）
- `track_title`: 表单中的title字段
- `content`: 表单中的content字段

### 错误处理
- API调用失败会显示错误提示给用户
- 错误信息记录到控制台
- 保留原有数据结构到本地存储

## 🧪 测试步骤

### 1. 基本功能测试
1. 访问任意艺术家页面（如 `/artists/ye`）
2. 滚动到页面底部的"Add Track"表单
3. 填写所有必填字段：
   - Artist: 选择艺术家
   - Category: 选择分类
   - Track Title: 输入歌曲标题
   - Content: 输入内容描述
4. 点击"ADD TRACK"按钮
5. 检查浏览器开发者工具的Network标签，确认API请求已发送

### 2. API请求验证
打开浏览器开发者工具，在Network标签中查找：
- 请求URL: `https://lh-api.dxwvv.com/api/v1/track`
- 请求方法: POST
- 请求头包含: `Content-Type: application/json`
- 请求体格式正确

### 3. 错误处理测试
- 断网情况下提交表单，确认显示错误提示
- 检查控制台是否有适当的错误日志

## 📝 示例API请求

```bash
curl -X POST https://lh-api.dxwvv.com/api/v1/track \
  -H "Content-Type: application/json" \
  -d '{
    "artist": "Kanye West",
    "category": "Unreleased", 
    "album": "Donda 2",
    "track_title": "True Love",
    "content": "Unreleased track from Donda 2 sessions"
  }'
```

## ✅ 集成特点

1. **双重保存**: 调用外部API的同时保留原有数据结构到本地存储
2. **数据清理**: 提交前清理HTML标签和特殊字符
3. **智能映射**: 自动将ID转换为可读的名称
4. **错误处理**: API失败时显示用户友好的错误提示
5. **数据完整性**: 确保album字段正确映射为可读名称

## 🔍 调试信息

在浏览器控制台中可以看到：
- `Successfully submitted to API` - API调用成功
- `API submission failed: [error]` - API调用失败

这样的集成确保了数据直接提交到远程API，提供了简洁高效的数据处理流程。

## 🔧 问题修复记录

### 修复1：Album字段获取错误
**问题**：API请求中album字段显示为"Unknown Album"而不是实际选择的专辑名称
**原因**：`getAlbumName()`函数中使用了错误的元素ID (`album-select` 而不是 `album`)
**修复**：更正了所有辅助函数中的元素ID引用

### 修复2：Era页面Album下拉无数据
**问题**：在 `/artists/ye/unreleased/eras/[eraId]` 页面中，album下拉选择器没有数据
**原因**：页面有预填的artistId和categoryId，但缺少初始化逻辑来加载对应的albums
**修复**：添加了初始化逻辑，在页面加载时自动加载预填参数对应的albums数据

### 修复3：URL更新
**问题**：需要将所有 `https://aitrackerhive.com` 更新为 `http://192.168.0.195:8001`
**修复**：更新了所有相关文件中的URL引用

### 修复4：表单数据保留
**问题**：提交成功后表单数据被完全清除
**原因**：使用了 `form.reset()` 清除了所有表单数据
**修复**：改为保留所有表单数据，包括title和content

### 修复5：SimpleSubmissionForm中Album字段问题
**问题**：SimpleSubmissionForm中选择album后API请求仍显示"Unknown Album"
**根本原因**：表单数据获取时缺少albumId字段
**详细分析**：
- 表单HTML中album字段的name属性正确：`name="albumId"`
- 用户确实选择了album（如"BULLY [V1]"）
- 但在FormData获取时，data对象中缺少`albumId: formData.get('albumId')`这一行
- 导致sanitizedData.albumId为undefined，getAlbumName函数返回"Unknown Album"
**修复**：在表单数据获取对象中添加缺失的albumId字段

### 修复6：非英文页面最近播放状态显示问题
**问题**：在葡萄牙语和阿拉伯语页面的"最近播放"部分，状态列显示`[object Object]`和`{{t('common.played', lang)}}`
**根本原因**：JavaScript中使用了Astro模板语法，但在客户端无法解析
**详细分析**：
- 葡萄牙语和阿拉伯语页面在JavaScript中使用了`{t('common.played', lang)}`
- 这种语法只在Astro服务端渲染时有效，在客户端JavaScript中无法解析
- 导致显示原始模板字符串而不是翻译后的文本
- 同时硬编码显示"played"状态，而应该显示track的实际状态（Official、Leaked等）
**修复**：
- 在JavaScript中定义完整的翻译对象和翻译函数
- 修改状态显示逻辑，根据track.status显示正确的状态和颜色
- 统一三种语言页面的状态显示逻辑
