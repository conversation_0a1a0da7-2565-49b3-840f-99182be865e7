---
// CollapsibleSubmissionForm.astro - 可折叠的内容提交表单
import { t, getLanguageFromURL } from '../../i18n/index.js';

export interface Props {
  artistId?: string;
  categoryId?: string;
  eraId?: string;
  className?: string;
}

const { artistId = '', categoryId = '', eraId = '', className = '' } = Astro.props;

// 获取当前语言
const currentLang = getLanguageFromURL(Astro.url.href);

// 加载表单翻译
import { getFormTranslations } from '../../i18n/form-translations.js';
const formTranslations = getFormTranslations(currentLang);
---

<section class={`collapsible-submission-section ${className}`}>
  <!-- 折叠按钮 -->
  <div class="collapsible-header">
    <button id="toggle-form-btn" class="toggle-form-button">
      <svg class="toggle-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
      </svg>
      <span class="button-text">{formTranslations.form.buttons.addTrack}</span>
      <svg class="chevron-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </button>
  </div>

  <!-- 可折叠的表单内容 -->
  <div id="form-content" class="form-content collapsed">
    <div class="form-wrapper">
      <div class="form-header">
        <h3>{formTranslations.form.title}</h3>
        <p>{formTranslations.form.subtitle}</p>
      </div>

      <form class="submission-form" id="collapsible-submission-form">
        <div class="form-grid">
          <!-- 艺术家选择 -->
          <div class="form-group">
            <label for="artist">{formTranslations.form.fields.artist}</label>
            <select id="artist" name="artistId" required>
              <option value="">{formTranslations.form.placeholders.selectArtist}</option>
              <option value="ye" selected={artistId === 'ye'}>Ye (Kanye West)</option>
              <option value="playboi-carti" selected={artistId === 'playboi-carti'}>Playboi Carti</option>
            </select>
          </div>

          <!-- 分类选择 -->
          <div class="form-group">
            <label for="category">{formTranslations.form.fields.category}</label>
            <select id="category" name="categoryId" required>
              <option value="">{formTranslations.form.placeholders.selectCategory}</option>
              <option value="released" selected={categoryId === 'released'}>Released</option>
              <option value="art" selected={categoryId === 'art'}>Art</option>
              <option value="recent" selected={categoryId === 'recent'}>Recent</option>
              <option value="best-of" selected={categoryId === 'best-of'}>Best Of</option>
            </select>
            <div class="validation-message" id="category-validation"></div>
          </div>
        </div>

        <!-- 专辑选择 -->
        <div class="form-group">
          <label for="album">{formTranslations.form.fields.album}</label>
          <select id="album" name="albumId">
            <option value="">{formTranslations.form.placeholders.selectAlbum}</option>
          </select>
          <div class="validation-message" id="album-validation"></div>
        </div>

        <!-- 曲目标题 -->
        <div class="form-group">
          <label for="track-title">{formTranslations.form.fields.trackTitle}</label>
          <input 
            type="text" 
            id="track-title" 
            name="title" 
            placeholder={formTranslations.form.placeholders.enterTrackName}
            required 
            maxlength="100"
          />
        </div>

        <!-- 内容/链接 -->
        <div class="form-group">
          <label for="content">{formTranslations.form.fields.content}</label>
          <textarea 
            id="content" 
            name="content" 
            placeholder={formTranslations.form.placeholders.pasteContent}
            required 
            rows="3"
            maxlength="500"
          ></textarea>
          <div class="char-count">
            <span id="content-count">0</span>/500 {formTranslations.form.charCount}
          </div>
        </div>

        <!-- 提交按钮 -->
        <button type="submit" class="submit-btn" id="submit-btn">
          <span class="submit-text">{formTranslations.form.buttons.addTrack}</span>
          <span class="submit-loading" style="display: none;">{formTranslations.form.buttons.submitting}</span>
        </button>
      </form>

      <!-- 成功消息 -->
      <div class="success-message hidden" id="success-message">
        <div class="success-content">
          <h4>{formTranslations.form.success.title}</h4>
          <p>{formTranslations.form.success.message}</p>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  .collapsible-submission-section {
    margin: 2rem auto;
    max-width: 800px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    background: linear-gradient(135deg, rgba(24, 24, 24, 0.95) 0%, rgba(40, 40, 40, 0.95) 100%);
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.05) inset;
    backdrop-filter: blur(20px);
    overflow: hidden;
    position: relative;
  }

  .collapsible-submission-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  }

  @media (max-width: 768px) {
    .collapsible-submission-section {
      margin: 1rem;
      border-radius: 16px;
    }
  }

  .collapsible-header {
    background: linear-gradient(135deg, rgba(24, 24, 24, 0.9) 0%, rgba(40, 40, 40, 0.9) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    position: relative;
  }

  .collapsible-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(29, 185, 84, 0.2), transparent);
  }

  .toggle-form-button {
    width: 100%;
    padding: 1.75rem 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: none;
    border: none;
    color: #22c55e;
    font-weight: 700;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    letter-spacing: 0.025em;
  }

  .toggle-form-button:hover {
    background: linear-gradient(135deg, rgba(29, 185, 84, 0.12) 0%, rgba(22, 163, 74, 0.12) 100%);
    color: #16a34a;
    transform: translateY(-1px);
  }

  .toggle-form-button:hover .toggle-icon {
    transform: scale(1.15) rotate(5deg);
    filter: drop-shadow(0 0 8px rgba(29, 185, 84, 0.3));
  }

  .toggle-icon {
    width: 1.5rem;
    height: 1.5rem;
    flex-shrink: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 2px 4px rgba(29, 185, 84, 0.15));
  }

  .button-text {
    flex: 1;
    text-align: left;
  }

  .submit-text {
    text-align: center;
  }

  .chevron-icon {
    width: 1.25rem;
    height: 1.25rem;
    flex-shrink: 0;
    transition: transform 0.2s ease;
  }

  .toggle-form-button.expanded .chevron-icon {
    transform: rotate(180deg);
  }

  .form-content {
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .form-content.collapsed {
    max-height: 0;
    opacity: 0;
  }

  .form-content.expanded {
    max-height: 1000px;
    opacity: 1;
  }

  .form-wrapper {
    padding: 2rem 2.5rem 2.5rem;
    background: linear-gradient(135deg, rgba(24, 24, 24, 0.6) 0%, rgba(40, 40, 40, 0.6) 100%);
  }

  @media (max-width: 768px) {
    .form-wrapper {
      padding: 1.5rem 1.5rem 2rem;
    }
  }

  .form-header {
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .form-header h3 {
    font-size: 1.75rem;
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 0.75rem;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 50%, #15803d 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.025em;
    text-shadow: 0 2px 4px rgba(34, 197, 94, 0.1);
  }

  .form-header p {
    color: #cbd5e1;
    font-size: 0.95rem;
    font-weight: 400;
    line-height: 1.5;
    opacity: 0.9;
  }

  .submission-form {
    max-width: 600px;
    margin: 0 auto;
  }

  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
  }

  @media (max-width: 768px) {
    .form-grid {
      grid-template-columns: 1fr;
      gap: 1.25rem;
    }
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-group label {
    display: block;
    color: #f1f5f9;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
    letter-spacing: 0.025em;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 1rem 1.25rem;
    background: linear-gradient(135deg, rgba(40, 40, 40, 0.8) 0%, rgba(51, 51, 51, 0.8) 100%);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    color: #f8fafc;
    font-size: 0.95rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.05) inset;
  }

  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    outline: none;
    border-color: rgba(29, 185, 84, 0.5);
    background: linear-gradient(135deg, rgba(40, 40, 40, 0.9) 0%, rgba(51, 51, 51, 0.9) 100%);
    box-shadow:
      0 0 0 3px rgba(29, 185, 84, 0.12),
      0 4px 12px rgba(29, 185, 84, 0.08),
      0 0 0 1px rgba(29, 185, 84, 0.15) inset;
    transform: translateY(-1px);
  }

  .form-group input::placeholder,
  .form-group textarea::placeholder {
    color: #94a3b8;
    font-weight: 400;
  }

  .char-count {
    text-align: right;
    font-size: 0.75rem;
    color: #666;
    margin-top: 0.25rem;
  }

  .validation-message {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    min-height: 1rem;
  }

  .validation-message.invalid {
    color: #ef4444;
  }

  .validation-message.valid {
    color: #10b981;
  }

  .submit-btn {
    width: 100%;
    max-width: 280px;
    margin: 2rem auto 0;
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, #1DB954 0%, #1ed760 100%);
    border: none;
    border-radius: 8px;
    color: #ffffff;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .submit-btn:hover {
    transform: translateY(-2px);
  }

  .submit-btn:active {
    transform: translateY(0);
  }

  .submit-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #6b7280;
    transform: none;
  }

  .submit-btn:disabled:hover {
    background: #6b7280;
    transform: none;
  }

  @media (max-width: 768px) {
    .submit-btn {
      max-width: 100%;
      padding: 1.125rem 1.75rem;
      font-size: 0.95rem;
    }
  }

  .btn-icon {
    width: 1rem;
    height: 1rem;
  }

  .success-message {
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(22, 163, 74, 0.1);
    border: 1px solid rgba(22, 163, 74, 0.2);
    border-radius: 8px;
    text-align: center;
    color: #16a34a;
    font-weight: 500;
  }

  .success-message.hidden {
    display: none;
  }

  .success-content h4 {
    color: #16a34a;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .success-content p {
    color: #22c55e;
    font-size: 0.875rem;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', async () => {
    const toggleBtn = document.getElementById('toggle-form-btn');
    const formContent = document.getElementById('form-content');
    const form = document.getElementById('collapsible-submission-form');
    const contentTextarea = document.getElementById('content');
    const contentCount = document.getElementById('content-count');
    const submitBtn = document.getElementById('submit-btn');
    const successMessage = document.getElementById('success-message');
    const artistSelect = document.getElementById('artist');
    const categorySelect = document.getElementById('category');
    const albumSelect = document.getElementById('album');

    // 表单翻译数据（内联以避免构建时fetch问题）
    const formTranslationsData = {
      "en": {
        "form": {
          "placeholders": {
            "selectAlbum": "Select Album"
          },
          "validation": {
            "pleaseSelectCategory": "Please select a category first",
            "contentTooShort": "Content must be at least 3 characters long",
            "fillAllFields": "Please fill in all fields",
            "contentLooksGood": "✓ Content looks good"
          }
        }
      },
      "ar": {
        "form": {
          "placeholders": {
            "selectAlbum": "اختر الألبوم"
          },
          "validation": {
            "pleaseSelectCategory": "يرجى اختيار فئة أولاً",
            "contentTooShort": "يجب أن يكون المحتوى 3 أحرف على الأقل",
            "fillAllFields": "يرجى ملء جميع الحقول",
            "contentLooksGood": "✓ المحتوى يبدو جيداً"
          }
        }
      },
      "pt": {
        "form": {
          "placeholders": {
            "selectAlbum": "Selecionar Álbum"
          },
          "validation": {
            "pleaseSelectCategory": "Por favor, selecione uma categoria primeiro",
            "contentTooShort": "O conteúdo deve ter pelo menos 3 caracteres",
            "fillAllFields": "Por favor, preencha todos os campos",
            "contentLooksGood": "✓ Conteúdo parece bom"
          }
        }
      }
    };

    const currentLang = getCurrentLanguage();
    const formTranslations = formTranslationsData[currentLang] || formTranslationsData['en'];

    // 获取当前语言
    function getCurrentLanguage() {
      const pathname = window.location.pathname;
      const segments = pathname.split('/').filter(Boolean);
      const supportedLanguages = ['en', 'ar', 'pt'];
      return supportedLanguages.includes(segments[0]) ? segments[0] : 'en';
    }

    // 折叠/展开功能
    toggleBtn?.addEventListener('click', () => {
      const isExpanded = formContent.classList.contains('expanded');

      if (isExpanded) {
        formContent.classList.remove('expanded');
        formContent.classList.add('collapsed');
        toggleBtn.classList.remove('expanded');
      } else {
        formContent.classList.remove('collapsed');
        formContent.classList.add('expanded');
        toggleBtn.classList.add('expanded');
      }
    });

    // 加载专辑列表
    async function loadAlbums(artistId, categoryId) {
      if (!artistId || !categoryId) {
        albumSelect.innerHTML = `<option value="">${formTranslations.form?.placeholders?.selectAlbum || 'Select Album'}</option>`;
        return;
      }

      try {
        const response = await fetch(`/data/artists/${artistId}/categories/${categoryId}/albums.json`);
        if (!response.ok) {
          throw new Error('Albums not found');
        }

        const albums = await response.json();
        albumSelect.innerHTML = `<option value="">${formTranslations.form?.placeholders?.selectAlbum || 'Select Album'}</option>`;

        albums.forEach(album => {
          const option = document.createElement('option');
          option.value = album.id;
          option.textContent = album.name;
          albumSelect.appendChild(option);
        });
      } catch (error) {
        console.warn('Could not load albums:', error);
        albumSelect.innerHTML = `<option value="">${formTranslations.form?.placeholders?.selectAlbum || 'Select Album'}</option>`;
      }
    }

    // 分类变化时加载专辑
    categorySelect?.addEventListener('change', () => {
      const artistId = artistSelect.value;
      const categoryId = categorySelect.value;

      if (!categoryId) {
        document.getElementById('category-validation').textContent = formTranslations.form.validation.pleaseSelectCategory;
        document.getElementById('category-validation').className = 'validation-message invalid';
      } else {
        document.getElementById('category-validation').textContent = '';
        document.getElementById('category-validation').className = 'validation-message';
      }

      loadAlbums(artistId, categoryId);
    });

    // 艺术家变化时重新加载专辑
    artistSelect?.addEventListener('change', () => {
      const artistId = artistSelect.value;
      const categoryId = categorySelect.value;
      loadAlbums(artistId, categoryId);
    });

    // 字符计数
    contentTextarea?.addEventListener('input', () => {
      const length = contentTextarea.value.length;
      contentCount.textContent = length;
    });

    // 设置自定义验证消息
    artistSelect?.addEventListener('invalid', function() {
      this.setCustomValidity('Please select an artist');
    });
    artistSelect?.addEventListener('change', function() {
      this.setCustomValidity('');
    });

    categorySelect?.addEventListener('invalid', function() {
      this.setCustomValidity('Please select a category');
    });
    categorySelect?.addEventListener('change', function() {
      this.setCustomValidity('');
    });

    document.getElementById('track-title')?.addEventListener('invalid', function() {
      this.setCustomValidity('Please enter a track title');
    });
    document.getElementById('track-title')?.addEventListener('input', function() {
      this.setCustomValidity('');
    });

    contentTextarea?.addEventListener('invalid', function() {
      this.setCustomValidity('Please enter some content');
    });
    contentTextarea?.addEventListener('input', function() {
      this.setCustomValidity('');
    });

    // 辅助函数：获取艺术家名称
    function getArtistName(artistId) {
      const artistSelect = document.getElementById('artist');
      const selectedOption = artistSelect?.querySelector(`option[value="${artistId}"]`);
      return selectedOption?.textContent || artistId || 'Unknown Artist';
    }

    // 辅助函数：获取分类名称
    function getCategoryName(categoryId) {
      const categorySelect = document.getElementById('category');
      const selectedOption = categorySelect?.querySelector(`option[value="${categoryId}"]`);
      return selectedOption?.textContent || categoryId || 'Unknown Category';
    }

    // 辅助函数：获取专辑名称
    function getAlbumName(albumId) {
      if (!albumId) return "Unknown Album";
      const albumSelect = document.getElementById('album');
      const selectedOption = albumSelect?.querySelector(`option[value="${albumId}"]`);
      return selectedOption?.textContent || albumId || "Unknown Album";
    }

    // 表单提交
    form?.addEventListener('submit', async (e) => {
      e.preventDefault();

      const formData = new FormData(form);
      const data = {
        id: 'track_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        artistId: formData.get('artistId'),
        categoryId: formData.get('categoryId'),
        albumId: formData.get('albumId'),
        title: formData.get('title').trim(),
        content: formData.get('content').trim(),
        submissionDate: new Date().toISOString(),
        status: 'pending'
      };

      // 基本验证
      if (!data.artistId || !data.categoryId || !data.title || !data.content) {
        alert(formTranslations.form.validation.fillAllFields);
        return;
      }

      if (data.content.length < 3) {
        alert(formTranslations.form.validation.contentTooShort);
        return;
      }

      // 显示加载状态
      submitBtn.disabled = true;
      submitBtn.querySelector('.submit-text').style.display = 'none';
      submitBtn.querySelector('.submit-loading').style.display = 'inline';

      try {
        // 基本的数据清理
        const sanitizedData = {
          ...data,
          title: data.title.replace(/<[^>]*>/g, ''),
          content: data.content.replace(/<[^>]*>/g, '')
        };

        // 准备API请求数据
        const apiData = {
          artist: getArtistName(sanitizedData.artistId),
          category: getCategoryName(sanitizedData.categoryId),
          album: getAlbumName(sanitizedData.albumId),
          track_title: sanitizedData.title,
          content: sanitizedData.content
        };

        // 调用外部API
        const apiResponse = await fetch('https://lh-api.dxwvv.com/api/v1/track', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(apiData)
        });

        if (!apiResponse.ok) {
          throw new Error(`API submission failed: ${apiResponse.status} ${apiResponse.statusText}`);
        }

        console.log('Successfully submitted to API');

        // 保留原有数据结构到本地存储
        const submissions = JSON.parse(localStorage.getItem('trackSubmissions') || '[]');
        submissions.unshift(sanitizedData);

        // 限制存储数量
        if (submissions.length > 50) {
          submissions.splice(50);
        }

        localStorage.setItem('trackSubmissions', JSON.stringify(submissions));

        // 显示成功消息
        successMessage.classList.remove('hidden');

        // 保留所有表单数据，不清除任何字段

        // 3秒后隐藏成功消息
        setTimeout(() => {
          successMessage.classList.add('hidden');
        }, 3000);

      } catch (error) {
        console.error('API submission failed:', error);
        alert('Failed to submit track to server. Please try again.');
      } finally {
        // 恢复按钮状态
        submitBtn.disabled = false;
        submitBtn.querySelector('.submit-text').style.display = 'inline';
        submitBtn.querySelector('.submit-loading').style.display = 'none';
      }
    });

    // 初始化：如果有预填数据，加载对应的专辑
    // 确保在formTranslations加载完成后再初始化
    const initialArtistId = artistSelect?.value;
    const initialCategoryId = categorySelect?.value;
    if (initialArtistId && initialCategoryId) {
      // 使用setTimeout确保DOM完全加载和formTranslations已设置
      setTimeout(() => {
        loadAlbums(initialArtistId, initialCategoryId);
      }, 100);
    }
  });
</script>
