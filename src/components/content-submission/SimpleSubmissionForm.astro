---
// SimpleSubmissionForm.astro - 简洁的内容提交表单
import { t, getLanguageFromURL } from '../../i18n/index.js';

export interface Props {
  artistId?: string;
  categoryId?: string;
  className?: string;
  hideHeader?: boolean;
}

const { artistId = '', categoryId = '', className = '', hideHeader = false } = Astro.props;

// 获取当前语言
const currentLang = getLanguageFromURL(Astro.url.href);

// 加载表单翻译
import { getFormTranslations } from '../../i18n/form-translations.js';
const formTranslations = getFormTranslations(currentLang);
---

<section class={`submission-section ${className}`}>
  {!hideHeader && (
    <div class="section-header">
      <h2>{formTranslations.form.sectionTitle}</h2>
      <p>{formTranslations.form.sectionSubtitle}</p>
    </div>
  )}

    <div class="form-wrapper">
      <div class="form-header">
        <h3>{formTranslations.form.title}</h3>
        <p>{formTranslations.form.subtitle}</p>
      </div>

      <form class="submission-form" id="simple-submission-form">
    <div class="form-grid">
      <!-- 艺术家选择 -->
      <div class="form-group">
        <label for="artist">{formTranslations.form.fields.artist}</label>
        <select id="artist" name="artistId" required>
          <option value="">{formTranslations.form.placeholders.selectArtist}</option>
          <option value="ye" selected={artistId === 'ye'}>Ye (Kanye West)</option>
          <option value="playboi-carti" selected={artistId === 'playboi-carti'}>Playboi Carti</option>
        </select>
      </div>

      <!-- 分类选择 -->
      <div class="form-group">
        <label for="category">{formTranslations.form.fields.category}</label>
        <select id="category" name="categoryId" required>
          <option value="">{formTranslations.form.placeholders.selectCategory}</option>
          <option value="released" selected={categoryId === 'released'}>Released</option>
          <option value="unreleased" selected={categoryId === 'unreleased'}>Unreleased</option>
          <option value="art" selected={categoryId === 'art'}>Art</option>
          <option value="recent" selected={categoryId === 'recent'}>Recent</option>
        </select>
        <div class="validation-message" id="category-validation"></div>
      </div>
    </div>

    <!-- 专辑选择 -->
    <div class="form-group">
      <label for="album">{formTranslations.form.fields.album}</label>
      <select id="album" name="albumId">
        <option value="">{formTranslations.form.placeholders.selectAlbum}</option>
      </select>
      <div class="validation-message" id="album-validation"></div>
    </div>

    <!-- 曲目标题 -->
    <div class="form-group">
      <label for="track-title">{formTranslations.form.fields.trackTitle}</label>
      <input
        type="text"
        id="track-title"
        name="title"
        placeholder={formTranslations.form.placeholders.enterTrackName}
        required
        maxlength="100"
      />
    </div>

    <!-- 内容/链接 -->
    <div class="form-group">
      <label for="content">{formTranslations.form.fields.content}</label>
      <textarea
        id="content"
        name="content"
        placeholder={formTranslations.form.placeholders.pasteContent}
        required
        rows="3"
        maxlength="500"
      ></textarea>
      <div class="char-count">
        <span id="content-count">0</span>/500 {formTranslations.form.charCount}
      </div>
    </div>

    <!-- 提交按钮 -->
    <button type="submit" class="submit-btn" id="submit-btn">
      <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
      </svg>
      <span class="submit-text">{formTranslations.form.buttons.addTrack}</span>
      <span class="submit-loading" style="display: none;">{formTranslations.form.buttons.submitting}</span>
    </button>
      </form>

      <!-- 成功消息 -->
      <div class="success-message hidden" id="success-message">
        <svg class="success-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span>Track added successfully!</span>
      </div>
    </div>
  </div>
</section>

<style>
  .submission-section {
    margin: 4rem 0 -1.5rem 0;
  }

  .section-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .section-header h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5rem;
  }

  .section-header p {
    color: #999;
    font-size: 0.875rem;
    max-width: 500px;
    margin: 0 auto;
    line-height: 1.5;
  }

  .submission-form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .form-wrapper {
    background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(45, 45, 45, 0.95) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
  }

  .form-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .form-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #1DB954 0%, #1ed760 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .form-header p {
    color: #999;
    font-size: 0.875rem;
  }

  .submission-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
  }

  .form-group label {
    font-size: 0.875rem;
    font-weight: 600;
    color: white;
    margin-bottom: 0.5rem;
    letter-spacing: 0.05em;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1rem 1.25rem;
    color: white;
    font-size: 0.95rem;
    font-weight: 500;
    transition: all 0.3s ease;
    resize: none;
    width: 100%;
  }

  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    outline: none;
    border-color: #1DB954;
    box-shadow: 0 0 0 3px rgba(29, 185, 84, 0.1);
    background: rgba(255, 255, 255, 0.08);
  }

  .form-group input::placeholder,
  .form-group textarea::placeholder {
    color: #94a3b8;
    font-weight: 400;
  }

  .char-count {
    text-align: right;
    font-size: 0.75rem;
    color: #666;
    margin-top: 0.25rem;
  }

  .validation-message {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    min-height: 1rem;
  }

  .validation-message.invalid {
    color: #ef4444;
  }

  .validation-message.valid {
    color: #10b981;
  }

  .submit-btn {
    background: linear-gradient(135deg, #1DB954 0%, #1ed760 100%);
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-top: 1rem;
    width: 100%;
    max-width: 250px;
    margin-left: auto;
    margin-right: auto;
  }

  .submit-btn:hover {
    transform: translateY(-2px);
  }

  .submit-btn:active {
    transform: translateY(0);
  }

  .submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .btn-icon {
    width: 1rem;
    height: 1rem;
  }

  .success-message {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background: rgba(29, 185, 84, 0.1);
    border: 1px solid rgba(29, 185, 84, 0.3);
    border-radius: 12px;
    padding: 1rem;
    color: #1DB954;
    font-weight: 500;
    margin-top: 1rem;
    animation: slideIn 0.3s ease;
  }

  .success-icon {
    width: 1.25rem;
    height: 1.25rem;
  }

  .hidden {
    display: none !important;
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .submission-section {
      margin: 3rem 0 -1rem 0;
    }

    .section-header h2 {
      font-size: 1.5rem;
    }

    .section-header {
      margin-bottom: 1.5rem;
    }

    .form-wrapper {
      padding: 1.5rem;
      margin: 0 0.5rem;
    }

    .form-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .form-header h3 {
      font-size: 1.25rem;
    }

    .form-header {
      margin-bottom: 1.5rem;
    }

    .submit-btn {
      padding: 0.875rem 1.5rem;
      font-size: 0.8rem;
      max-width: 200px;
    }
  }

  @media (max-width: 480px) {
    .submission-section {
      margin: 2rem 0 -0.5rem 0;
    }

    .section-header {
      margin-bottom: 1rem;
    }

    .section-header h2 {
      font-size: 1.25rem;
    }

    .form-wrapper {
      padding: 1.25rem;
      border-radius: 12px;
    }

    .form-header {
      margin-bottom: 1.25rem;
    }

    .form-header h3 {
      font-size: 1.125rem;
    }
  }

  /* 音乐网站特有的视觉效果 */
  .form-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #1DB954 0%, #1ed760 50%, #1DB954 100%);
    border-radius: 16px 16px 0 0;
  }

  .form-wrapper::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(29, 185, 84, 0.03) 0%, transparent 70%);
    pointer-events: none;
    animation: pulse 8s ease-in-out infinite;
  }

  .submission-form {
    position: relative;
    z-index: 2;
  }

  @keyframes pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 0.8; }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', async () => {
    const form = document.getElementById('simple-submission-form');
    const contentTextarea = document.getElementById('content');
    const contentCount = document.getElementById('content-count');
    const submitBtn = document.getElementById('submit-btn');
    const successMessage = document.getElementById('success-message');
    const artistSelect = document.getElementById('artist');
    const categorySelect = document.getElementById('category');
    const albumSelect = document.getElementById('album');

    // 表单翻译数据（内联以避免构建时fetch问题）
    const formTranslationsData = {
      "en": {
        "form": {
          "placeholders": {
            "selectAlbum": "Select Album"
          },
          "validation": {
            "pleaseSelectCategory": "Please select a category first",
            "contentTooShort": "Content must be at least 3 characters long",
            "fillAllFields": "Please fill in all fields",
            "contentLooksGood": "✓ Content looks good"
          }
        }
      },
      "ar": {
        "form": {
          "placeholders": {
            "selectAlbum": "اختر الألبوم"
          },
          "validation": {
            "pleaseSelectCategory": "يرجى اختيار فئة أولاً",
            "contentTooShort": "يجب أن يكون المحتوى 3 أحرف على الأقل",
            "fillAllFields": "يرجى ملء جميع الحقول",
            "contentLooksGood": "✓ المحتوى يبدو جيداً"
          }
        }
      },
      "pt": {
        "form": {
          "placeholders": {
            "selectAlbum": "Selecionar Álbum"
          },
          "validation": {
            "pleaseSelectCategory": "Por favor, selecione uma categoria primeiro",
            "contentTooShort": "O conteúdo deve ter pelo menos 3 caracteres",
            "fillAllFields": "Por favor, preencha todos os campos",
            "contentLooksGood": "✓ Conteúdo parece bom"
          }
        }
      }
    };

    const currentLang = getCurrentLanguage();
    const formTranslations = formTranslationsData[currentLang] || formTranslationsData['en'];

    // 获取当前语言
    function getCurrentLanguage() {
      const pathname = window.location.pathname;
      const segments = pathname.split('/').filter(Boolean);
      const supportedLanguages = ['en', 'ar', 'pt'];
      return supportedLanguages.includes(segments[0]) ? segments[0] : 'en';
    }

    // 加载专辑列表
    async function loadAlbums(artistId, categoryId) {
      if (!artistId || !categoryId) {
        albumSelect.innerHTML = `<option value="">${formTranslations.form?.placeholders?.selectAlbum || 'Select Album'}</option>`;
        return;
      }

      try {
        const response = await fetch(`/data/artists/${artistId}/categories/${categoryId}/albums.json`);
        if (!response.ok) {
          throw new Error('Albums not found');
        }

        const albums = await response.json();
        albumSelect.innerHTML = `<option value="">${formTranslations.form?.placeholders?.selectAlbum || 'Select Album'}</option>`;

        albums.forEach(album => {
          const option = document.createElement('option');
          option.value = album.id;
          option.textContent = album.name;
          albumSelect.appendChild(option);
        });
      } catch (error) {
        console.warn('Could not load albums:', error);
        albumSelect.innerHTML = `<option value="">${formTranslations.form?.placeholders?.selectAlbum || 'Select Album'}</option>`;
      }
    }

    // 分类变化时加载专辑
    categorySelect?.addEventListener('change', () => {
      const artistId = artistSelect.value;
      const categoryId = categorySelect.value;

      if (!categoryId) {
        document.getElementById('category-validation').textContent = formTranslations.form.validation.pleaseSelectCategory;
        document.getElementById('category-validation').className = 'validation-message invalid';
      } else {
        document.getElementById('category-validation').textContent = '';
        document.getElementById('category-validation').className = 'validation-message';
      }

      loadAlbums(artistId, categoryId);
    });

    // 艺术家变化时重新加载专辑
    artistSelect?.addEventListener('change', () => {
      const artistId = artistSelect.value;
      const categoryId = categorySelect.value;
      loadAlbums(artistId, categoryId);
    });

    // 字符计数
    contentTextarea?.addEventListener('input', () => {
      const count = contentTextarea.value.length;
      contentCount.textContent = count;
      
      if (count > 450) {
        contentCount.style.color = '#ff6b6b';
      } else {
        contentCount.style.color = '#666';
      }
    });

    // 设置自定义验证消息
    artistSelect?.addEventListener('invalid', function() {
      this.setCustomValidity('Please select an artist');
    });
    artistSelect?.addEventListener('change', function() {
      this.setCustomValidity('');
    });

    categorySelect?.addEventListener('change', function() {
      this.setCustomValidity('');
    });
    categorySelect?.addEventListener('invalid', function() {
      this.setCustomValidity('Please select a category');
    });

    document.getElementById('track-title')?.addEventListener('invalid', function() {
      this.setCustomValidity('Please enter a track title');
    });
    document.getElementById('track-title')?.addEventListener('input', function() {
      this.setCustomValidity('');
    });

    contentTextarea?.addEventListener('invalid', function() {
      this.setCustomValidity('Please enter some content');
    });
    contentTextarea?.addEventListener('input', function() {
      this.setCustomValidity('');
    });

    // 辅助函数：获取艺术家名称
    function getArtistName(artistId) {
      const artistSelect = document.getElementById('artist');
      const selectedOption = artistSelect?.querySelector(`option[value="${artistId}"]`);
      return selectedOption?.textContent || artistId || 'Unknown Artist';
    }

    // 辅助函数：获取分类名称
    function getCategoryName(categoryId) {
      const categorySelect = document.getElementById('category');
      const selectedOption = categorySelect?.querySelector(`option[value="${categoryId}"]`);
      return selectedOption?.textContent || categoryId || 'Unknown Category';
    }

    // 辅助函数：获取专辑名称
    function getAlbumName(albumId) {
      if (!albumId) return "Unknown Album";
      const albumSelect = document.getElementById('album');
      const selectedOption = albumSelect?.querySelector(`option[value="${albumId}"]`);
      return selectedOption?.textContent || albumId || "Unknown Album";
    }

    // 表单提交
    form?.addEventListener('submit', async (e) => {
      e.preventDefault();
      
      const formData = new FormData(form);
      const data = {
        id: 'track_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
        artistId: formData.get('artistId'),
        categoryId: formData.get('categoryId'),
        albumId: formData.get('albumId'),
        title: formData.get('title').trim(),
        content: formData.get('content').trim(),
        submissionDate: new Date().toISOString(),
        status: 'pending'
      };

      // 基本验证
      if (!data.artistId || !data.categoryId || !data.title || !data.content) {
        alert(formTranslations.form.validation.fillAllFields);
        return;
      }

      if (data.content.length < 3) {
        alert(formTranslations.form.validation.contentTooShort);
        return;
      }

      // 显示加载状态
      submitBtn.disabled = true;
      submitBtn.querySelector('.submit-text').style.display = 'none';
      submitBtn.querySelector('.submit-loading').style.display = 'inline';

      try {
        // 基本的数据清理
        const sanitizedData = {
          ...data,
          title: data.title.replace(/<[^>]*>/g, ''),
          content: data.content.replace(/<[^>]*>/g, '')
        };

        // 准备API请求数据
        const apiData = {
          artist: getArtistName(sanitizedData.artistId),
          category: getCategoryName(sanitizedData.categoryId),
          album: getAlbumName(sanitizedData.albumId),
          track_title: sanitizedData.title,
          content: sanitizedData.content
        };

        // 调用外部API
        const apiResponse = await fetch('https://lh-api.dxwvv.com/api/v1/track', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(apiData)
        });

        if (!apiResponse.ok) {
          throw new Error(`API submission failed: ${apiResponse.status} ${apiResponse.statusText}`);
        }

        console.log('Successfully submitted to API');

        // 保留原有数据结构到本地存储
        const submissions = JSON.parse(localStorage.getItem('trackSubmissions') || '[]');
        submissions.unshift(sanitizedData);

        // 限制存储数量
        if (submissions.length > 50) {
          submissions.splice(50);
        }

        localStorage.setItem('trackSubmissions', JSON.stringify(submissions));

        // 显示成功消息
        successMessage.classList.remove('hidden');

        // 保留所有表单数据，不清除任何字段

        // 3秒后隐藏成功消息
        setTimeout(() => {
          successMessage.classList.add('hidden');
        }, 3000);
        
      } catch (error) {
        console.error('API submission failed:', error);
        alert('Failed to submit track to server. Please try again.');
      } finally {
        // 恢复按钮状态
        submitBtn.disabled = false;
        submitBtn.querySelector('.submit-text').style.display = 'inline';
        submitBtn.querySelector('.submit-loading').style.display = 'none';
      }
    });

    // 初始化：如果有预填数据，加载对应的专辑
    // 确保在formTranslations加载完成后再初始化
    const initialArtistId = artistSelect?.value;
    const initialCategoryId = categorySelect?.value;
    if (initialArtistId && initialCategoryId) {
      // 使用setTimeout确保DOM完全加载和formTranslations已设置
      setTimeout(() => {
        loadAlbums(initialArtistId, initialCategoryId);
      }, 100);
    }
  });
</script>
