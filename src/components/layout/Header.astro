---
import LanguageSwitcher from './LanguageSwitcher.astro';
import { getLanguageFromURL } from '../../i18n/index.js';

// 获取当前语言
let currentLang = getLanguageFromURL(Astro.request.url);

// 检查 URL 参数中的语言设置
const url = new URL(Astro.request.url);
const langParam = url.searchParams.get('lang');
if (langParam && ['en', 'ar', 'pt'].includes(langParam)) {
  currentLang = langParam;
}
---

<header class="w-full bg-black bg-opacity-95 backdrop-blur-sm sticky top-0 z-10 shadow-md">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-4">
      <!-- Logo - 在移动端显示，在桌面端隐藏 -->
      <div class="flex items-center sm:hidden">
        <a href={currentLang === 'en' ? '/' : `/${currentLang}/`} class="flex items-center">
          <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
            <svg class="w-5 h-5 text-black" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2"/>
              <path d="M6.85046 16C7.17627 16.6667 8.82373 16.6667 9.14954 16L12 10.9282L14.8505 16C15.1763 16.6667 16.8237 16.6667 17.1495 16L20 10.9282" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M9 9C9 9.55228 8.55228 10 8 10C7.44772 10 7 9.55228 7 9C7 8.44772 7.44772 8 8 8C8.55228 8 9 8.44772 9 9Z" fill="currentColor"/>
              <path d="M17 9C17 9.55228 16.5523 10 16 10C15.4477 10 15 9.55228 15 9C15 8.44772 15.4477 8 16 8C16.5523 8 17 8.44772 17 9Z" fill="currentColor"/>
            </svg>
          </div>
          <span class="ml-2 text-lg font-medium">AITrackerHive</span>
        </a>
      </div>
      
      <!-- 桌面端留出空白区域 -->
      <div class="hidden sm:block sm:flex-1"></div>
      
      <!-- 语言切换功能 -->      
      <div class="ml-auto">
        <LanguageSwitcher currentLocale={currentLang} />
      </div>
    </div>
  </div>
</header>
