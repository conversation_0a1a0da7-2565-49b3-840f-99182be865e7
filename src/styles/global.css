@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --radius: 0.5rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.7 0.3 142); /* Spotify green */
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: #121212; /* Spotify background */
  --foreground: #ffffff;
  --card: #181818; /* Spotify card background */
  --card-foreground: #ffffff;
  --popover: #282828; /* Spotify popover */
  --popover-foreground: #ffffff;
  --primary: #16a34a; /* Softer green for dark theme */
  --primary-foreground: #000000;
  --secondary: #333333; /* Spotify secondary */
  --secondary-foreground: #ffffff;
  --muted: #282828; /* Spotify muted */
  --muted-foreground: #b3b3b3; /* Spotify secondary text */
  --accent: #535353; /* Spotify accent */
  --accent-foreground: #ffffff;
  --destructive: #e91429; /* Spotify red */
  --border: rgba(255, 255, 255, 0.1);
  --input: rgba(255, 255, 255, 0.1);
  --ring: rgba(255, 255, 255, 0.3);
  --chart-1: #16a34a; /* Softer green for dark theme */
  --chart-2: #4687d6; /* Spotify blue */
  --chart-3: #8c1932; /* Spotify red */
  --chart-4: #af2896; /* Spotify purple */
  --chart-5: #eb9b34; /* Spotify orange */
  --sidebar: #000000; /* Spotify sidebar */
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #1db954; /* Spotify green */
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #282828; /* Spotify sidebar accent */
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --sidebar-ring: rgba(255, 255, 255, 0.3);
}

@layer base {
  * {
    border: var(--border);
    outline: var(--ring) solid var(--ring);
  }
  body {
    background-color: var(--background);
    color: var(--foreground);
    font-family: sans-serif;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02";
  }
  
  /* Spotify-style scrollbar */
  ::-webkit-scrollbar {
    width: 12px;
  }
  
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    border: 3px solid transparent;
    background-clip: content-box;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
    border: 3px solid transparent;
    background-clip: content-box;
  }
  
  /* Improved focus styles */
  :focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }
  
  /* Smooth transitions */
  a, button {
    transition: all 0.2s ease;
  }
}