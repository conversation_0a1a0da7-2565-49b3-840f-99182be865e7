import { defineMiddleware } from 'astro:middleware';

// 定义自定义的Locals类型
declare global {
  namespace App {
    interface Locals {
      headers: Record<string, string>;
    }
  }
}

// 支持的语言
const SUPPORTED_LANGUAGES = ['en', 'ar', 'pt'];
const DEFAULT_LANGUAGE = 'en';

export const onRequest = defineMiddleware(async (context, next) => {
  const { url, cookies, request, redirect } = context;
  const { pathname } = url;

  // 语言重定向逻辑
  // 1. 如果访问 /en/* 路径，重定向到无前缀路径
  if (pathname.startsWith('/en/') || pathname === '/en') {
    const newPath = pathname === '/en' ? '/' : pathname.replace(/^\/en/, '');
    return redirect(newPath, 301);
  }

  // 检查是否是语言前缀路径
  const langPathRegex = /^\/([a-z]{2})(\/?|$)/;
  const langMatch = pathname.match(langPathRegex);

  if (langMatch) {
    const lang = langMatch[1];

    // 如果是有效的语言代码，设置 cookie
    if (SUPPORTED_LANGUAGES.includes(lang)) {
      cookies.set('preferred_language', lang, {
        path: '/',
        maxAge: 60 * 60 * 24 * 365, // 一年
        secure: url.protocol === 'https:',
        sameSite: 'lax',
      });
    }
  } else {
    // 这是一个无语言前缀的路径
    // 检查 cookie 中存储的语言偏好
    const storedLang = cookies.get('preferred_language')?.value;

    // 尝试从请求头中获取 Cookie（解决新标签页问题）
    let cookieLang = storedLang;
    if (!cookieLang) {
      const cookieHeader = request.headers.get('cookie');
      if (cookieHeader) {
        const match = cookieHeader.match(/preferred_language=([a-z]{2})/);
        if (match && match[1]) {
          cookieLang = match[1];
        }
      }
    }

    // 尝试获取浏览器语言偏好
    let browserLang = null;
    const acceptLanguage = request.headers.get('accept-language');
    if (acceptLanguage) {
      const preferredLang = acceptLanguage.split(',')[0].trim().split('-')[0].toLowerCase();
      if (SUPPORTED_LANGUAGES.includes(preferredLang)) {
        browserLang = preferredLang;
      }
    }

    // 按照存储的语言、浏览器语言偏好、默认语言的顺序选择语言
    const userLang = cookieLang || browserLang || DEFAULT_LANGUAGE;

    // 只有当语言不是默认语言时才进行重定向
    if (userLang !== DEFAULT_LANGUAGE) {
      // 构建重定向URL
      const redirectUrl = `/${userLang}${pathname === '/' ? '' : pathname}`;
      return redirect(redirectUrl);
    }
  }

  // 初始化headers
  const headers: Record<string, string> = {
    'X-Robots-Tag': 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1',
    'X-Content-Type-Options': 'nosniff',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'X-Frame-Options': 'SAMEORIGIN',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
  };

  // 如果不是API路由，添加缓存控制
  if (!pathname.includes('/api/')) {
    headers['Cache-Control'] = 'public, max-age=3600';
  }

  // 获取响应
  const response = await next();

  // 应用所有响应头
  Object.entries(headers).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  return response;
});
