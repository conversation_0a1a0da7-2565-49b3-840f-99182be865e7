---
import '../styles/global.css';
import DeviceDetection from '../components/DeviceDetection.astro';

// 导入i18n工具
import { t, getCurrentLanguage, getLanguageFromURL, SUPPORTED_LANGUAGES } from '../i18n/index.js';

interface Props {
  title: string;
  description?: string;
  canonicalUrl?: string;
  ogImage?: string;
  type?: 'website' | 'article' | 'music';
  jsonLd?: object;
}

// 获取当前语言和方向
// 先尝试从 URL 参数获取语言
let currentLang = getLanguageFromURL(Astro.request.url);

// 检查 URL 参数中的语言设置
const url = new URL(Astro.request.url);
const langParam = url.searchParams.get('lang');
if (langParam && ['en', 'ar', 'pt'].includes(langParam)) {
  currentLang = langParam;
}

// 如果都没有，使用当前语言
if (!currentLang) {
  currentLang = getCurrentLanguage();
}

// 设置文档方向
const dir = SUPPORTED_LANGUAGES[currentLang]?.dir || 'ltr';

const { 
  title, 
  description = 'AITrackerHive - Track Ye and other artists with real-time updates, unreleased music catalogs, and comprehensive discographies. Your ultimate music resource hub.',
  // 确保规范URL处理：首页保留斜杠，其他页面不带尾部斜杠
  canonicalUrl = Astro.url.pathname ? 
    `https://aitrackerhive.com${Astro.url.pathname === '/' || Astro.url.pathname === '/ar/' || Astro.url.pathname === '/pt/' ? Astro.url.pathname : Astro.url.pathname.endsWith('/') ? Astro.url.pathname.slice(0, -1) : Astro.url.pathname}` : 
    'https://aitrackerhive.com',
  ogImage = '/images/og-image.jpg',
  type = 'website',
  jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'AITrackerHive',
    description
  }
} = Astro.props;
---

<!DOCTYPE html>
<html lang={currentLang} dir={dir} class="dark">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content={description} />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />

    <!-- 性能优化：预加载关键资源 -->
    <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin />
    <link rel="preload" href="/images/artists/placeholder.svg" as="image" />
    <link rel="preload" href="/images/album-placeholder.svg" as="image" />

    <!-- DNS预解析 -->
    <link rel="dns-prefetch" href="//api.pillowcase.su" />
    <link rel="dns-prefetch" href="//pillowcase.su" />

    <!-- 预连接到关键第三方域名 -->
    <link rel="preconnect" href="https://api.pillowcase.su" crossorigin />
    <link rel="preconnect" href="https://pillowcase.su" crossorigin />

    <!-- 关键CSS内联 - 首屏渲染优化 -->
    <style>
      /* 关键首屏样式 - 避免FOUC */
      body {
        background-color: #000;
        color: #fff;
        font-family: system-ui, -apple-system, sans-serif;
        margin: 0;
        padding: 0;
      }
      .loading-skeleton {
        background: linear-gradient(90deg, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }
      @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
      }
      /* 隐藏未加载的图片，避免布局跳动 */
      img:not([src]) { visibility: hidden; }
    </style>
    
    <!-- IndexNow for faster indexing -->
    <meta name="IndexNow" content="a1b2c3d4e5f6g7h8i9j0" />

    <!-- Web App Manifest -->
    <link rel="manifest" href="/site.webmanifest" />
    
    <!-- Favicon集合 -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="AITrackerHive" />
    
    <title>{title}</title>
    
    <!-- 百度站点验证 -->
    <meta name="baidu-site-verification" content="codeva-umJ3RSSwyR" />
    
    <!-- SEO Meta Tags -->
    <link rel="canonical" href={canonicalUrl} />
    {Object.entries(SUPPORTED_LANGUAGES).map(([lang, { name }]) => {
      // 构建基于canonicalUrl的多语言URL，正确处理语言前缀
      const baseUrl = 'https://aitrackerhive.com';
      const path = Astro.url.pathname;

      // 移除当前路径中的语言前缀
      let cleanPath = path;
      const supportedLangs = ['en', 'ar', 'pt'];
      const pathSegments = path.split('/').filter(Boolean);

      if (pathSegments.length > 0 && supportedLangs.includes(pathSegments[0])) {
        cleanPath = '/' + pathSegments.slice(1).join('/');
        if (cleanPath === '/') cleanPath = '';
      }

      // 构建正确的多语言URL
      let langUrl = '';
      if (lang === 'en') {
        // 英文是默认语言，不添加语言前缀
        langUrl = cleanPath === '' ? baseUrl : `${baseUrl}${cleanPath}`;
      } else {
        // 其他语言添加语言前缀
        langUrl = `${baseUrl}/${lang}${cleanPath}`;
      }

      return (
        <link
          rel="alternate"
          hreflang={lang}
          href={langUrl}
        />
      );
    })}
    <meta name="robots" content="index, follow" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content={type} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={ogImage} />
    <meta property="og:url" content={Astro.url.href} />
    <meta property="og:site_name" content="AITrackerHive" />
    <meta property="og:locale" content={currentLang} />
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={title} />
    <meta name="twitter:description" content={description} />
    <meta name="twitter:image" content={ogImage} />
    
    <!-- Structured Data -->
    <script type="application/ld+json" set:html={JSON.stringify(jsonLd)} />
    
    <!-- 优化字体加载 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
    <noscript>
      <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    </noscript>
    <meta name="theme-color" content="#121212" />
    <meta name="color-scheme" content="dark" />
    
    <!-- 设备检测组件 -->
    <DeviceDetection />

    <!-- 下载功能脚本 -->
    <script src="/utils/newDownloader.js" defer></script>

    <!-- Google Analytics (GA4) - 优化加载 -->
    <script defer src="https://www.googletagmanager.com/gtag/js?id=G-8F9JZCYK5H"></script>
    <script>
      // 延迟加载GA4，减少对页面加载性能的影响
      window.addEventListener('load', function() {
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-8F9JZCYK5H', {
          'send_page_view': true,
          'anonymize_ip': true,
          'cookie_flags': 'SameSite=None;Secure'
        });
      });
    </script>
    
    <!-- Microsoft Clarity 分析 - 优化加载 -->
    <script type="text/javascript">
      // 延迟加载Clarity分析，减少对关键渲染路径的阻塞
      window.addEventListener('load', function() {
        setTimeout(function() {
          (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
          })(window, document, "clarity", "script", "s7ec5vw3tw");
        }, 1000); // 页面加载完成后延迟1秒加载Clarity
      });
    </script>
    
    <!-- Adsterra Social Bar 广告 - 延迟加载 -->
    <script>
      // 延迟加载广告脚本，避免阻塞关键渲染路径
      window.addEventListener('load', function() {
        setTimeout(function() {
          const adScript = document.createElement('script');
          adScript.type = 'text/javascript';
          adScript.async = true;
          adScript.src = '//pl26439272.profitableratecpm.com/35/11/a9/3511a933f6081a1023fdda1fb77e4de8.js';
          document.head.appendChild(adScript);
        }, 2000); // 延迟2秒加载
      });
    </script>
  </head>
  <body class="min-h-screen bg-black text-white font-sans antialiased overflow-x-hidden">
    <slot />

    <!-- 性能优化：延迟加载非关键脚本 -->
    <script>
      // 图片懒加载增强（为不支持原生lazy loading的浏览器）
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target;
              if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
              }
              observer.unobserve(img);
            }
          });
        }, {
          rootMargin: '50px 0px',
          threshold: 0.01
        });

        // 观察所有带有data-src的图片
        document.querySelectorAll('img[data-src]').forEach(img => {
          imageObserver.observe(img);
        });
      }

      // 预加载关键页面
      const prefetchLinks = ['/artists/ye', '/artists/playboi-carti'];
      const prefetchOnIdle = () => {
        if ('requestIdleCallback' in window) {
          requestIdleCallback(() => {
            prefetchLinks.forEach(link => {
              const linkEl = document.createElement('link');
              linkEl.rel = 'prefetch';
              linkEl.href = link;
              document.head.appendChild(linkEl);
            });
          });
        }
      };

      // 在页面加载完成后预加载
      if (document.readyState === 'complete') {
        prefetchOnIdle();
      } else {
        window.addEventListener('load', prefetchOnIdle);
      }

      // Service Worker注册（用于缓存优化）
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then(registration => {
              console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
