---
import { getLanguageFromURL, SUPPORTED_LANGUAGES } from '../i18n';
import Header from '../components/layout/Header.astro';
import Footer from '../components/layout/Footer.astro';

// 获取当前语言
const currentLang = getLanguageFromURL(Astro.url);
const dir = SUPPORTED_LANGUAGES[currentLang]?.dir || 'ltr';

// 页面元数据
const { title, description } = Astro.props;
---

<!DOCTYPE html>
<html lang={currentLang} dir={dir}>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{title}</title>
    <meta name="description" content={description} />
    
    <!-- 添加语言替代链接，有利于SEO -->
    {Object.keys(SUPPORTED_LANGUAGES).map(lang => {
      const isDefault = lang === 'en';
      const path = Astro.url.pathname;
      const segments = path.split('/').filter(Boolean);
      const hasLocalePrefix = ['ar', 'pt'].includes(segments[0]);
      
      let href;
      if (isDefault) {
        href = hasLocalePrefix ? `/${segments.slice(1).join('/')}` : path;
      } else {
        href = hasLocalePrefix 
          ? `/${lang}/${segments.slice(1).join('/')}` 
          : `/${lang}${path}`;
      }
      
      return (
        <link 
          rel="alternate" 
          hreflang={lang} 
          href={new URL(href, Astro.url.origin).toString()} 
        />
      );
    })}
    
    <!-- 网站图标 - 多种格式和尺寸 -->
    <link rel="icon" href="/favicon.ico" sizes="any" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png" />
    <link rel="manifest" href="/site.webmanifest" />
    
    <link rel="stylesheet" href="/styles/global.css" />
  </head>
  <body class={`min-h-screen flex flex-col ${dir === 'rtl' ? 'rtl' : ''}`}>
    <Header />
    
    <main class="flex-grow">
      <slot />
    </main>
    
    <Footer />
    
    <!-- 客户端脚本 -->
    <script>
      // 设置文档方向
      document.documentElement.dir = '{dir}';
    </script>
  </body>
</html>
