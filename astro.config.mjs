// @ts-check
import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import react from '@astrojs/react';
import compress from 'astro-compress';

// https://astro.build/config
export default defineConfig({
  output: 'static',  // 确保使用静态输出
  server: {
    headers: {
      'X-Robots-Tag': 'index, follow',
      'X-Content-Type-Options': 'nosniff',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'X-Frame-Options': 'SAMEORIGIN',
      'X-XSS-Protection': '1; mode=block'
    }
  },
  
  integrations: [
    tailwind({
      applyBaseStyles: false,
    }),
    react(),
    compress({
      css: true,
      html: true,
      js: true,
      img: true,
      svg: true,
      logger: 1,
    })
  ],

  // 多语言支持配置
  i18n: {
    locales: ['en', 'ar', 'pt'],
    defaultLocale: 'en',
    routing: {
      prefixDefaultLocale: false
    }
  }
});